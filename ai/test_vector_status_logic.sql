-- 测试新的向量状态逻辑的SQL查询
-- 基于scopedAssistantId（用户管理的助手列表）来判断向量状态

-- 假设scopedAssistantId = [1, 2, 3]，测试各种场景

-- 场景1：检查在scopedAssistantId中绑定的QA（无论启用还是禁用）
SELECT
    d.id as doc_id,
    d.index_text as question,
    ad.assistant_id,
    ad.state as assistant_doc_state,
    CASE
        WHEN ad.state = 1 THEN 'enabled'
        WHEN ad.state = 2 THEN 'disabled'
        WHEN ad.state = 3 THEN 'deleting'
        WHEN ad.state = 4 THEN 'unbounded'
        ELSE 'unknown'
    END as state_name,
    'bound_in_scoped' as scenario
FROM t_doc d
INNER JOIN t_assistant_doc ad ON d.id = ad.doc_id
WHERE d.data_type = 1  -- QA类型
  AND ad.assistant_id IN (1, 2, 3)  -- scopedAssistantId
ORDER BY d.id, ad.assistant_id;

-- 场景2：检查在scopedAssistantId中有sync_version记录的QA（包括孤立记录）
SELECT 
    d.id as doc_id,
    d.index_text as question,
    dsv.collection_id,
    ac.assistant_id,
    dsv.doc_version,
    dsv.rag_version,
    CASE 
        WHEN dsv.doc_version != dsv.rag_version THEN 'syncing'
        WHEN dsv.rag_version > 0 THEN 'synced'
        ELSE 'never_synced'
    END as sync_status,
    'has_sync_version_in_scoped' as scenario
FROM t_doc d
INNER JOIN t_doc_sync_version dsv ON d.id = dsv.doc_id
INNER JOIN t_assistant_collection ac ON dsv.collection_id = ac.collection_id
WHERE d.data_type = 1  -- QA类型
  AND ac.assistant_id IN (1, 2, 3)  -- scopedAssistantId
ORDER BY d.id, ac.assistant_id;

-- 场景3：检查绑定但缺失sync_version记录的QA（无论启用还是禁用）
SELECT
    d.id as doc_id,
    d.index_text as question,
    ad.assistant_id,
    ad.state as assistant_doc_state,
    'missing_sync_version' as scenario
FROM t_doc d
INNER JOIN t_assistant_doc ad ON d.id = ad.doc_id
INNER JOIN t_assistant_collection ac ON ad.assistant_id = ac.assistant_id
LEFT JOIN t_doc_sync_version dsv ON d.id = dsv.doc_id AND ac.collection_id = dsv.collection_id
WHERE d.data_type = 1  -- QA类型
  AND ad.assistant_id IN (1, 2, 3)  -- scopedAssistantId
  AND dsv.doc_id IS NULL  -- 缺失sync_version记录
ORDER BY d.id, ad.assistant_id;

-- 场景4：检查孤立的sync_version记录（t_assistant_doc已删除但sync_version还存在）
SELECT 
    d.id as doc_id,
    d.index_text as question,
    dsv.collection_id,
    ac.assistant_id,
    dsv.doc_version,
    dsv.rag_version,
    'orphaned_sync_version' as scenario
FROM t_doc d
INNER JOIN t_doc_sync_version dsv ON d.id = dsv.doc_id
INNER JOIN t_assistant_collection ac ON dsv.collection_id = ac.collection_id
LEFT JOIN t_assistant_doc ad ON d.id = ad.doc_id AND ac.assistant_id = ad.assistant_id AND ad.state = 1
WHERE d.data_type = 1  -- QA类型
  AND ac.assistant_id IN (1, 2, 3)  -- scopedAssistantId
  AND ad.doc_id IS NULL  -- 没有启用的assistant_doc记录
ORDER BY d.id, ac.assistant_id;

-- 场景5：综合查询 - 模拟新的向量状态判断逻辑
WITH scoped_docs AS (
    -- 在scopedAssistantId中有关联的所有文档（绑定状态或有sync_version记录）
    SELECT DISTINCT d.id as doc_id
    FROM t_doc d
    WHERE d.data_type = 1  -- QA类型
      AND (
          -- 在scopedAssistantId中绑定（无论启用还是禁用）
          EXISTS (
              SELECT 1 FROM t_assistant_doc ad
              WHERE ad.doc_id = d.id
                AND ad.assistant_id IN (1, 2, 3)
          )
          OR
          -- 在scopedAssistantId中有sync_version记录
          EXISTS (
              SELECT 1 FROM t_doc_sync_version dsv
              INNER JOIN t_assistant_collection ac ON dsv.collection_id = ac.collection_id
              WHERE dsv.doc_id = d.id
                AND ac.assistant_id IN (1, 2, 3)
          )
      )
),
doc_status AS (
    SELECT 
        sd.doc_id,
        -- 检查是否有同步中的记录
        EXISTS (
            SELECT 1 FROM t_doc_sync_version dsv
            INNER JOIN t_assistant_collection ac ON dsv.collection_id = ac.collection_id
            WHERE dsv.doc_id = sd.doc_id 
              AND ac.assistant_id IN (1, 2, 3)
              AND dsv.doc_version != dsv.rag_version 
              AND dsv.doc_version > 0
        ) as has_syncing,
        -- 检查是否有已同步的记录
        EXISTS (
            SELECT 1 FROM t_doc_sync_version dsv
            INNER JOIN t_assistant_collection ac ON dsv.collection_id = ac.collection_id
            WHERE dsv.doc_id = sd.doc_id 
              AND ac.assistant_id IN (1, 2, 3)
              AND dsv.rag_version > 0
        ) as has_synced,
        -- 检查是否没有sync_version记录（简化逻辑）
        NOT EXISTS (
            SELECT 1 FROM t_doc_sync_version dsv
            INNER JOIN t_assistant_collection ac ON dsv.collection_id = ac.collection_id
            WHERE dsv.doc_id = sd.doc_id
              AND ac.assistant_id IN (1, 2, 3)
        ) as has_no_sync_version
    FROM scoped_docs sd
)
SELECT 
    d.id as doc_id,
    d.index_text as question,
    ds.has_syncing,
    ds.has_synced,
    ds.has_no_sync_version,
    CASE
        WHEN ds.has_syncing OR ds.has_no_sync_version THEN 'SYNCING'
        WHEN ds.has_synced AND NOT ds.has_syncing THEN 'SYNCED'
        ELSE 'UNSPECIFIED'
    END as embedding_state
FROM scoped_docs sd
INNER JOIN t_doc d ON sd.doc_id = d.id
INNER JOIN doc_status ds ON sd.doc_id = ds.doc_id
ORDER BY d.id;

-- 场景6：不在scopedAssistantId范围内的QA（应该返回UNSPECIFIED）
SELECT 
    d.id as doc_id,
    d.index_text as question,
    'not_in_scoped' as scenario
FROM t_doc d
WHERE d.data_type = 1  -- QA类型
  AND NOT EXISTS (
      -- 不在scopedAssistantId中绑定
      SELECT 1 FROM t_assistant_doc ad
      WHERE ad.doc_id = d.id
        AND ad.assistant_id IN (1, 2, 3)
  )
  AND NOT EXISTS (
      -- 不在scopedAssistantId中有sync_version记录
      SELECT 1 FROM t_doc_sync_version dsv
      INNER JOIN t_assistant_collection ac ON dsv.collection_id = ac.collection_id
      WHERE dsv.doc_id = d.id 
        AND ac.assistant_id IN (1, 2, 3)
  )
ORDER BY d.id
LIMIT 10;
