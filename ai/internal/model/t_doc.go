package model

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode"

	"e.coding.net/tencent-ssv/tanlive/gokits/xslice"
	"e.coding.net/tencent-ssv/tanlive/gokits/xstrings"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"golang.org/x/exp/slices"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"e.coding.net/tencent-ssv/tanlive/services/ai/util"
)

const (
	// EmptyStringMD5 空字符串的MD5值
	EmptyStringMD5 = "d41d8cd98f00b204e9800998ecf8427e"
)

// NormalizeQAReg 匹配非字母、数字、空白的字符
var NormalizeQAReg = regexp.MustCompile(`[^\p{L}\p{N}\s]`)

// NormalizeQA 处理QA的问题，去掉标点符号和不可见字符
func NormalizeQA(question string) string {
	normalizedText := NormalizeQAReg.ReplaceAllString(question, "")
	normalizedText = strings.TrimSpace(normalizedText)
	return normalizedText
}

func (m *TDoc) BeforeSave(tx *gorm.DB) error {
	if slices.Contains(tx.Statement.Selects, "index_text") {
		tx.Statement.Selects = append(tx.Statement.Selects, "index_text_cerpt", "normalized_hash")
	}
	// 计算文本片段
	if m.IndexTextCerpt == "" && len(m.IndexText) > 0 {
		if len(m.IndexText) > 300 {
			m.IndexTextCerpt = xstrings.Substring(m.IndexText, 0, 300)
		} else {
			m.IndexTextCerpt = m.IndexText
		}
	}

	if len(m.IndexText) > 0 {
		m.NormalizedHash = util.CalculateMD5String(NormalizeQA(m.IndexText))
	}

	return nil
}

// AfterFind hook. 填充助手对应的每个doc对应的collection,及其对应的状态
// TODO: 如果同一个collection绑定到了多个助手，而doc在不同助手的启用/禁用状态不一致，如何定义行为？
func (m *TDoc) AfterFind(tx *gorm.DB) error {
	mp := make(map[uint64]*TCollection)
	if len(m.Assistants) != 0 {
		for _, v := range m.Assistants {
			if len(v.Collections) != 0 {
				for _, c := range v.Collections {
					mp[c.ID] = &c.TCollection
				}
			}
		}
		for _, v := range mp {
			m.Collections = append(m.Collections, v)
		}
	}

	// 对于文件，当前全局状态为解析中/解析失败/上传失败时 重写每个助手对应的状态
	// 删除：重新解析后，会影响助手对应的状态
	// switch m.State {
	// case ai.DocState_DOC_STATE_PARSING, ai.DocState_DOC_STATE_PARES_FAILED,
	// 	ai.DocState_DOC_STATE_FILE_UPLOADING:
	// 	for _, v := range m.States {
	// 		v.State = m.State
	// 	}
	// default:
	// }
	return nil
}

// IgnoreHelpCenterData 忽略帮助中心数据
func IgnoreHelpCenterData(db *gorm.DB) *gorm.DB {
	return db.Where("ugc_type not in (5, 6)")
}

// ToQAProto 转换成proto格式
func (t *TDoc) ToQAProto() *ai.QA {
	if t == nil {
		return nil
	}
	p := &ai.QA{
		Id:       t.ID,
		Question: t.IndexText,
		Answer:   t.Text,
		State:    t.State,
		UpdateBy: &ai.Operator{
			Type:   t.UpdateByType,
			Id:     t.UpdateBy,
			UserId: t.UpdateByUser,
		},
		CreateBy: &ai.Operator{
			Type:   t.CreateByType,
			Id:     t.CreateBy,
			UserId: t.CreateByUser,
		},
		UpdateDate:      timestamppb.New(t.UpdateDate),
		CreateDate:      timestamppb.New(t.CreateDate),
		HitCount:        uint32(t.HitCount),
		VersionLag:      t.Version - t.RagVersion,
		ShowContributor: t.ShowContributor,
	}
	p.States = t.StatesToProto()
	p.Contributor = t.ContributorToPb()
	for _, v := range t.Assistants {
		p.Assistants = append(p.Assistants, v.ToProto())
	}
	p.EmbeddingVersion = t.GenEmbeddingVersion()
	p.Reference = t.ReferenceToProto()
	for _, v := range t.Labels {
		p.Labels = append(p.Labels, v.ToLabelPb())
	}

	p.MatchPatterns = make([]ai.DocMatchPattern, 0, len(t.MatchPatterns))
	for _, m := range t.MatchPatterns {
		p.MatchPatterns = append(p.MatchPatterns, m.MatchPattern)
	}
	p.ShareReceivers = t.DocSharesToProto()

	return p
}

// FromQAProto ...
func (t *TDoc) FromQAProto(p *ai.QA) *TDoc {
	t.ID = p.Id
	t.Text = p.Answer
	t.IndexText = p.Question
	t.CreateBy = p.GetCreateBy().GetId()
	t.CreateByType = p.GetCreateBy().GetType()
	t.CreateByUser = p.GetCreateBy().GetUserId()
	t.UpdateBy = p.GetUpdateBy().GetId()
	t.UpdateByType = p.GetUpdateBy().GetType()
	t.UpdateByUser = p.GetUpdateBy().GetUserId()
	t.HitCount = uint64(p.HitCount)
	for _, v := range p.Assistants {
		t.Assistants = append(t.Assistants, &TAssistant{ID: v.Id})
	}
	t.ShowContributor = p.ShowContributor
	t.DataType = uint32(ai.DocType_DOCTYPE_QA)
	t.ReferenceFromProtoWithSort(p.Reference)
	t.FillContributorsFromProto(p.Contributor...)
	t.FillStatesFromProto(p.States...)
	for _, v := range p.Labels {
		t.Labels = append(t.Labels, (&TObjectLabel{}).FromLabelPb(t.ID, ai.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_QA, v))
	}
	LabelMcSort(t.Labels)

	for _, mp := range p.MatchPatterns {
		t.MatchPatterns = append(t.MatchPatterns, &TDocMatchPattern{
			MatchPattern: mp,
		})
	}

	return t
}

func (t *TDoc) ReferenceToProto() (refs []*ai.DocReference) {
	// 创建一个临时结构来存储所有参考资料及其排序
	type refWithSort struct {
		ref       *ai.DocReference
		sortOrder int32
	}

	var allRefs []refWithSort

	// 处理引用文档参考资料 - 使用DocReferences关联表数据
	if len(t.DocReferences) > 0 {
		// 创建引用文档ID到详细信息的映射
		refDetailMap := make(map[uint64]*TDoc)
		for _, vv := range t.References {
			refDetailMap[vv.ID] = vv
		}

		for _, docRef := range t.DocReferences {
			refID := uint64(docRef.RefID)
			ref := &ai.DocReference{
				Id: refID,
			}

			// 如果有详细信息，填充名称和URL
			if detail, exists := refDetailMap[refID]; exists {
				ref.Name = detail.FileName
				if detail.Ref != nil {
					ref.Url = detail.Ref.Url
				}
			}

			allRefs = append(allRefs, refWithSort{
				ref:       ref,
				sortOrder: docRef.SortOrder,
			})
		}
	}

	// 添加纯文本参考资料到返回结果
	for _, textRef := range t.TextReferences {
		allRefs = append(allRefs, refWithSort{
			ref: &ai.DocReference{
				Text: textRef.Text,
			},
			sortOrder: textRef.SortOrder,
		})
	}

	// 按排序字段排序
	sort.Slice(allRefs, func(i, j int) bool {
		return allRefs[i].sortOrder < allRefs[j].sortOrder
	})

	// 提取排序后的参考资料
	for _, item := range allRefs {
		refs = append(refs, item.ref)
	}

	return
}

func (t *TDoc) ReferenceFromProto(refs []*ai.DocReference) *TDoc {
	return t.ReferenceFromProtoWithSort(refs)
}

func (t *TDoc) ReferenceFromProtoWithSort(refs []*ai.DocReference) *TDoc {
	var textRefs []*TDocTextReference // 纯文本参考资料列表
	var docRefs []*TDocReference      // 引用文档参考资料关联表

	// 使用map来跟踪已添加的DocID和RefID组合，避免重复
	docRefMap := make(map[string]bool)

	for i, v := range refs {
		sortOrder := int32(i) // 使用索引作为排序字段
		if v.Id != 0 {
			// id 不为0，代表引用了其他doc，创建关联表记录
			docID := int64(t.ID)
			refID := int64(v.Id)

			// 创建唯一键，用于检查是否已存在相同的DocID和RefID组合
			key := fmt.Sprintf("%d-%d", docID, refID)

			// 如果这个组合已经存在，跳过添加
			if _, exists := docRefMap[key]; exists {
				continue
			}

			// 标记这个组合为已添加
			docRefMap[key] = true

			docRef := &TDocReference{
				DocID:     docID,
				RefID:     refID,
				SortOrder: sortOrder,
			}
			docRefs = append(docRefs, docRef)
		} else if v.Text != "" {
			// 纯文本参考资料，准备存入新表
			textRef := &TDocTextReference{
				DocID:     t.ID,
				Text:      v.Text,
				SortOrder: sortOrder,
			}
			textRefs = append(textRefs, textRef)
		}
	}

	// 保存关联表数据和纯文本参考资料
	t.DocReferences = docRefs
	t.TextReferences = textRefs

	return t
}

// ReplaceReferencesWithSort 替换参考资料并保持排序
func (t *TDoc) ReplaceReferencesWithSort(tx *gorm.DB, refs []*ai.DocReference) error {
	// 先删除现有的参考资料
	if err := tx.Where("doc_id = ?", t.ID).Delete(&TDocReference{}).Error; err != nil {
		return err
	}
	if err := tx.Where("doc_id = ?", t.ID).Delete(&TDocTextReference{}).Error; err != nil {
		return err
	}

	// 重新填充参考资料数据
	t.ReferenceFromProtoWithSort(refs)

	// 批量插入新的参考资料
	if len(t.DocReferences) > 0 {
		if err := tx.Create(t.DocReferences).Error; err != nil {
			return err
		}
	}
	if len(t.TextReferences) > 0 {
		if err := tx.Create(t.TextReferences).Error; err != nil {
			return err
		}
	}

	return nil
}

// DocSharesToProto 分享至团队和个人的id
func (t *TDoc) DocSharesToProto() []*ai.DocShareReceiver {
	receivers := make([]*ai.DocShareReceiver, 0, len(t.DocShares))
	for _, v := range t.DocShares {
		receivers = append(receivers, &ai.DocShareReceiver{
			Id:        v.TargetID,
			ShareType: v.ShareType,
		})
	}
	return receivers
}

// ToFullTextFilePb 协议转换
func (t *TDoc) ToFullTextFilePb() *ai.FullTextFile {
	if t == nil {
		return nil
	}
	return &ai.FullTextFile{
		Doc:    t.ToTextFileProto(),
		Copies: TDocs(t.Copies).ToTextFilePb(),
		// HasOverSizedTable 字段在handler层处理
	}
}

// ToTextFileProto 转换成proto格式
func (t *TDoc) ToTextFileProto() *ai.TextFile {
	p := &ai.TextFile{
		Id:   t.ID,
		Name: t.FileName,
		Text: func() string {
			if len(t.IndexText) != 0 {
				return t.IndexText
			}
			return t.IndexTextCerpt
		}(),
		State: t.State,
		UpdateBy: &ai.Operator{
			Type:   t.UpdateByType,
			Id:     t.UpdateBy,
			UserId: t.UpdateByUser,
		},
		CreateBy: &ai.Operator{
			Type:   t.CreateByType,
			Id:     t.CreateBy,
			UserId: t.CreateByUser,
		},
		UpdateDate:      timestamppb.New(t.UpdateDate),
		CreateDate:      timestamppb.New(t.CreateDate),
		HitCount:        uint32(t.HitCount),
		UgcId:           t.UgcID,
		UgcType:         t.UgcType,
		Type:            t.DataType,
		VersionLag:      t.Version - t.RagVersion,
		ShowContributor: t.ShowContributor,
		IsCopy:          t.IsCopy,
		MainId:          t.MainID,
		UgcTitle:        t.UgcTitle,
		// IsSystem:        t.IsSystem,
		DataSource:    t.DataSource,
		ContentState:  t.ContentState,
		DownloadAsRef: t.DownloadAsRef,
	}
	p.States = t.StatesToProto()
	p.Contributor = t.ContributorToPb()
	if t.Ref != nil {
		p.Url = t.Ref.Url
	}
	if t.DocExtend != nil {
		p.ParseMode = t.DocExtend.ParseMode
	}
	if t.DocDataSource != nil {
		p.DataSourceState = t.DocDataSource.State
	}

	for _, v := range t.Assistants {
		p.Assistants = append(p.Assistants, v.ToProto())
	}
	p.EmbeddingVersion = t.GenEmbeddingVersion()
	for _, v := range t.Labels {
		p.Labels = append(p.Labels, v.ToLabelPb())
	}
	p.Reference = t.ReferenceToProto()
	p.ShareReceivers = t.DocSharesToProto()
	return p
}

// GenEmbeddingVersionMap 生成向量同步状态,只生成 tdoc 关联的的助手状态
func (t *TDoc) GenEmbeddingVersionMap() map[uint64]uint32 {
	mp := make(map[uint64]uint32)
	for _, v := range t.Assistants {
		mp[v.ID] = 0
		for _, vv := range v.Collections {
			for _, vvv := range t.SyncVersions {
				if vvv.CollectionID == vv.ID {
					lag := uint32(vvv.DocVersion - vvv.RagVersion)
					if lag != 0 {
						mp[v.ID] = lag
					}
				}
			}
		}
	}
	return mp
}

// GenEmbeddingVersion 生成向量同步状态
// 基于scopedAssistantId（用户管理的助手列表）来生成向量状态：
// 1. 优先返回在scopedAssistantId中启用的助手的sync_version记录
// 2. 对于启用但缺失sync_version记录的助手，生成默认记录（DocVersion=1, RagVersion=0）
// 3. 包含孤立的sync_version记录（t_assistant_doc已删除但sync_version还存在）
// ! 必须先preload SyncVersions.AssistantCollection
func (t *TDoc) GenEmbeddingVersion(scopedAssistantId ...uint64) []*ai.EmbeddingVersion {
	var ret []*ai.EmbeddingVersion

	// 如果没有指定scopedAssistantId，按原逻辑处理
	if len(scopedAssistantId) == 0 {
		for _, v := range t.SyncVersions {
			if v.AssistantCollection == nil {
				continue
			}
			ret = append(ret, &ai.EmbeddingVersion{
				AssistantId: v.AssistantCollection.AssistantID,
				DocVersion:  uint32(v.DocVersion),
				RagVersion:  uint32(v.RagVersion),
			})
		}
		return ret
	}

	// 收集已有的sync_version记录（在scopedAssistantId范围内）
	syncVersionMap := make(map[uint64]*ai.EmbeddingVersion)
	for _, v := range t.SyncVersions {
		if v.AssistantCollection == nil {
			continue
		}
		if slices.Contains(scopedAssistantId, v.AssistantCollection.AssistantID) {
			syncVersionMap[v.AssistantCollection.AssistantID] = &ai.EmbeddingVersion{
				AssistantId: v.AssistantCollection.AssistantID,
				DocVersion:  uint32(v.DocVersion),
				RagVersion:  uint32(v.RagVersion),
			}
		}
	}

	// 收集在scopedAssistantId中启用的助手
	enabledAssistantMap := make(map[uint64]bool)
	for _, state := range t.States {
		if slices.Contains(scopedAssistantId, state.AssistantID) && state.State == ai.DocState_DOC_STATE_ENABLED {
			enabledAssistantMap[state.AssistantID] = true
		}
	}

	// 为启用但缺失sync_version记录的助手生成默认记录
	for assistantID := range enabledAssistantMap {
		if _, exists := syncVersionMap[assistantID]; !exists {
			syncVersionMap[assistantID] = &ai.EmbeddingVersion{
				AssistantId: assistantID,
				DocVersion:  1, // 默认版本
				RagVersion:  0, // 未同步
			}
		}
	}

	// 转换为数组返回
	for _, version := range syncVersionMap {
		ret = append(ret, version)
	}

	return ret
}

// GenEmbeddingVersion 生成向量同步状态, 包括在scopedAssistantId，不只是t_doc关联的助手状态
// 只供 list 接口使用
func GenEmbeddingVersion(ctx context.Context, docs []*TDoc, scopedAssistantId []uint64) ([][]*ai.EmbeddingVersion, error) {
	var uniqueCollectionId []uint64
	for _, v := range docs {
		for _, vv := range v.SyncVersions {
			uniqueCollectionId = append(uniqueCollectionId, vv.CollectionID)
		}
	}
	xslice.SetUint64Unique(&uniqueCollectionId, xslice.SortNot)
	// 根据 collection获取助手id
	collection2Assistant := make([]*TAssistantCollection, 0, len(uniqueCollectionId))
	db := NewQuery[TAssistantCollection](ctx).DB().
		Where("collection_id IN (?)", uniqueCollectionId)
	if len(scopedAssistantId) != 0 {
		db.Where("assistant_id IN (?)", scopedAssistantId)
	}
	err := db.Find(&collection2Assistant).Error
	if err != nil {
		return nil, err
	}
	collection2AssistantIdMap := make(map[uint64]uint64)
	for _, v := range collection2Assistant {
		collection2AssistantIdMap[v.CollectionID] = v.AssistantID
	}

	var ret [][]*ai.EmbeddingVersion
	for _, v := range docs {
		var tmp []*ai.EmbeddingVersion
		for _, vv := range v.SyncVersions {
			if aid, ok := collection2AssistantIdMap[vv.CollectionID]; ok {
				tmp = append(tmp, &ai.EmbeddingVersion{
					AssistantId: aid,
					DocVersion:  uint32(vv.DocVersion),
					RagVersion:  uint32(vv.RagVersion),
				})
			}
		}
		ret = append(ret, tmp)
	}
	return ret, nil
}

func (t *TDoc) FromTextFileProto(p *ai.TextFile) *TDoc {
	t.ID = p.Id
	t.IndexText = p.Text
	t.Ref = &DocReference{
		Name:      p.Name,
		Url:       p.Url,
		ParsedUrl: p.ParsedUrl,
	}
	t.FileName = p.Name
	t.State = p.State
	t.CreateBy = p.GetCreateBy().GetId()
	t.CreateByType = p.GetCreateBy().GetType()
	t.CreateByUser = p.GetCreateBy().GetUserId()
	t.UpdateBy = p.GetUpdateBy().GetId()
	t.UpdateByType = p.GetUpdateBy().GetType()
	t.UpdateByUser = p.GetUpdateBy().GetUserId()
	t.HitCount = uint64(p.HitCount)
	for _, v := range p.Assistants {
		t.Assistants = append(t.Assistants, &TAssistant{ID: v.Id})
	}
	t.UgcType = p.UgcType
	t.UgcID = p.UgcId
	t.DataType = p.Type
	t.FillContributorsFromProto(p.Contributor...)
	t.ShowContributor = p.ShowContributor
	t.FillStatesFromProto(p.States...)
	t.IsCopy = p.IsCopy
	t.MainID = p.MainId
	t.UgcTitle = p.UgcTitle
	t.DataSource = p.DataSource
	// t.IsSystem = p.IsSystem
	t.ContentState = p.ContentState
	for _, v := range p.Labels {
		t.Labels = append(t.Labels, (&TObjectLabel{}).FromLabelPb(t.ID, ai.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_FILE, v))
	}
	LabelMcSort(t.Labels)
	t.ReferenceFromProtoWithSort(p.Reference)
	t.DownloadAsRef = p.DownloadAsRef
	t.DocExtend = &TDocExtend{
		DocID:       p.Id,
		ParseMode:   p.ParseMode,
		ParseUserID: p.GetUpdateBy().GetId(),
		ParseByType: p.GetUpdateBy().GetType(),
		ParseByUser: p.GetUpdateBy().GetUserId(),
		UpdateDate:  time.Now(),
	}
	return t
}

func (doc *TDoc) TransformTDocToMessageDoc() *ai.ChatMessageDoc {
	pbd := &ai.ChatMessageDoc{
		Id:          doc.ID,
		UgcType:     doc.UgcType,
		UgcId:       doc.UgcID,
		RagFilename: doc.RagFilename,
		Contributor: doc.ContributorToPb(),
		DataType:    doc.DataType,
		FileName:    doc.FileName,
		IndexText:   TitleTruncate(doc.IndexText, 40, 80),
		Text:        doc.Text,
		DataSource:  doc.DataSource,
	}
	allRefs := doc.ReferenceToProto()
	refDownloadMap := make(map[uint64]*TDoc)
	for _, v := range doc.References {
		refDownloadMap[v.ID] = v
	}

	if doc.Ref != nil {
		// 文件，且没有关联其他参考资料，返回文件自己的 url 链接
		if doc.DataType == uint32(ai.DocType_DOCTYPE_FILE) && len(allRefs) == 0 &&
			doc.DownloadAsRef != ai.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_HIDDEN {
			ref := &ai.DocReference{Id: doc.ID, Name: doc.FileName, ShowType: doc.DownloadAsRef}
			if doc.DownloadAsRef == ai.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_SHOW_URL ||
				doc.DownloadAsRef == ai.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_SEND {
				ref.Url = doc.Ref.Url
			}
			pbd.Reference = append(pbd.Reference, ref)
		}
	}
	// qa/文本/文件 关联的其他参考资料
	for _, ref := range allRefs {
		if ref.Id == 0 {
			pbd.Reference = append(pbd.Reference, &ai.DocReference{Text: ref.Text})
		} else {
			if vv, ok := refDownloadMap[ref.Id]; ok && vv.DownloadAsRef != ai.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_HIDDEN {
				refToShow := &ai.DocReference{
					Id:       vv.ID,
					Text:     vv.FileName,
					Name:     vv.FileName,
					ShowType: vv.DownloadAsRef,
				}
				if (vv.DownloadAsRef == ai.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_SHOW_URL ||
					vv.DownloadAsRef == ai.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_SEND) &&
					vv.Ref != nil {
					refToShow.Url = vv.Ref.Url
				}
				pbd.Reference = append(pbd.Reference, refToShow)
			}
		}
	}
	// 3.24更新，这里注释是为了无论有没有参考资料都展示贡献者
	// 不是帮助中心，且无参考资料，返回nil
	// if pbd.UgcType == base.DataType_DATA_TYPE_UNSPECIFIED && len(pbd.Reference) == 0 {
	//	return nil
	// }

	if pbd.UgcType == base.DataType_DATA_TYPE_HELP_CENTER && len(pbd.Reference) == 0 {
		pbd.Reference = append(pbd.Reference, &ai.DocReference{
			Name: pbd.FileName,
		})
	}

	return pbd
}

// func (doc *TDoc) TransformTDocToMessageDoc(referenceDocs map[uint64]*TDoc) *ai.ChatMessageDoc {
// 	pbd := &ai.ChatMessageDoc{
// 		Id:          doc.ID,
// 		UgcType:     doc.UgcType,
// 		UgcId:       doc.UgcID,
// 		RagFilename: doc.RagFilename,
// 		Contributor: doc.ContributorToPb(),
// 		DataType:    doc.DataType,
// 		FileName:    doc.FileName,
// 		IndexText:   TitleTruncate(doc.IndexText, 40, 80),
// 		Text:        doc.Text,
// 		DataSource:  doc.DataSource,
// 	}
//
// 	if doc.Ref != nil {
// 		// 文件，且没有关联其他参考资料，返回文件自己的 url 链接
// 		if doc.DataType == uint32(ai.DocType_DOCTYPE_FILE) && len(doc.Ref.QaRef) == 0 &&
// 			doc.DownloadAsRef != ai.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_HIDDEN {
// 			ref := &ai.DocReference{Name: doc.FileName, ShowType: doc.DownloadAsRef}
// 			if doc.DownloadAsRef == ai.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_SHOW_URL ||
// 				doc.DownloadAsRef == ai.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_SEND {
// 				ref.Url = doc.Ref.Url
// 			}
// 			pbd.Reference = append(pbd.Reference, ref)
// 		}
//
// 		// qa/文本/文件 关联的其他参考资料
// 		for _, ref := range doc.Ref.QaRef {
// 			if ref.Id == 0 {
// 				pbd.Reference = append(pbd.Reference, &ai.DocReference{Text: ref.Text})
// 			} else {
// 				if vv, ok := referenceDocs[ref.Id]; ok && vv.DownloadAsRef != ai.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_HIDDEN {
// 					refToShow := &ai.DocReference{
// 						Id:       vv.ID,
// 						Text:     vv.FileName,
// 						Name:     vv.FileName,
// 						ShowType: vv.DownloadAsRef,
// 					}
// 					if (vv.DownloadAsRef == ai.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_SHOW_URL ||
// 						vv.DownloadAsRef == ai.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_SEND) &&
// 						vv.Ref != nil {
// 						refToShow.Url = vv.Ref.Url
// 					}
// 					pbd.Reference = append(pbd.Reference, refToShow)
// 				}
// 			}
// 		}
// 	}
// 	// 3.24更新，这里注释是为了无论有没有参考资料都展示贡献者
// 	// 不是帮助中心，且无参考资料，返回nil
// 	// if pbd.UgcType == base.DataType_DATA_TYPE_UNSPECIFIED && len(pbd.Reference) == 0 {
// 	//	return nil
// 	// }
//
// 	if pbd.UgcType == base.DataType_DATA_TYPE_HELP_CENTER && len(pbd.Reference) == 0 {
// 		pbd.Reference = append(pbd.Reference, &ai.DocReference{
// 			Name: pbd.FileName,
// 		})
// 	}
//
// 	return pbd
// }

// ToChatMessageDoc ...
func (m *TDoc) ToChatMessageDoc() *ai.ChatMessageDoc {
	if m == nil {
		return nil
	}

	return &ai.ChatMessageDoc{
		UgcId:       m.UgcID,
		UgcType:     m.UgcType,
		Id:          m.ID,
		RagFilename: m.RagFilename,
		Contributor: m.ContributorToPb(),
		DataType:    m.DataType,
		FileName:    m.FileName,
		IndexText:   m.IndexText,
		Text:        m.Text,
		DataSource:  m.DataSource,
	}
}

// func (doc *TDoc) TransformTDocToMessageDoc(referenceDocs map[uint64]*TDoc) *ai.ChatMessageDoc {
// 	pbd := &ai.ChatMessageDoc{
// 		Id:          doc.ID,
// 		UgcType:     doc.UgcType,
// 		UgcId:       doc.UgcID,
// 		RagFilename: doc.RagFilename,
// 		Contributor: doc.ContributorToPb(),
// 		DataType:    doc.DataType,
// 		FileName:    doc.FileName,
// 		IndexText:   TitleTruncate(doc.IndexText, 40, 80),
// 		Text:        doc.Text,
// 		DataSource:  doc.DataSource,
// 	}
//
// 	if doc.Ref != nil {
// 		// 文件，且没有关联其他参考资料，返回文件自己的 url 链接
// 		if doc.DataType == uint32(ai.DocType_DOCTYPE_FILE) && len(doc.Ref.QaRef) == 0 &&
// 			doc.DownloadAsRef != ai.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_HIDDEN {
// 			ref := &ai.DocReference{Name: doc.FileName, ShowType: doc.DownloadAsRef}
// 			if doc.DownloadAsRef == ai.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_SHOW_URL ||
// 				doc.DownloadAsRef == ai.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_SEND {
// 				ref.Url = doc.Ref.Url
// 			}
// 			pbd.Reference = append(pbd.Reference, ref)
// 		}
//
// 		// qa/文本/文件 关联的其他参考资料
// 		for _, ref := range doc.Ref.QaRef {
// 			if ref.Id == 0 {
// 				pbd.Reference = append(pbd.Reference, &ai.DocReference{Text: ref.Text})
// 			} else {
// 				if vv, ok := referenceDocs[ref.Id]; ok && vv.DownloadAsRef != ai.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_HIDDEN {
// 					refToShow := &ai.DocReference{
// 						Id:       vv.ID,
// 						Text:     vv.FileName,
// 						Name:     vv.FileName,
// 						ShowType: vv.DownloadAsRef,
// 					}
// 					if (vv.DownloadAsRef == ai.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_SHOW_URL ||
// 						vv.DownloadAsRef == ai.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_SEND) &&
// 						vv.Ref != nil {
// 						refToShow.Url = vv.Ref.Url
// 					}
// 					pbd.Reference = append(pbd.Reference, refToShow)
// 				}
// 			}
// 		}
// 	}
// 	// 3.24更新，这里注释是为了无论有没有参考资料都展示贡献者
// 	// 不是帮助中心，且无参考资料，返回nil
// 	// if pbd.UgcType == base.DataType_DATA_TYPE_UNSPECIFIED && len(pbd.Reference) == 0 {
// 	//	return nil
// 	// }
//
// 	if pbd.UgcType == base.DataType_DATA_TYPE_HELP_CENTER && len(pbd.Reference) == 0 {
// 		pbd.Reference = append(pbd.Reference, &ai.DocReference{
// 			Name: pbd.FileName,
// 		})
// 	}
//
// 	return pbd
// }

// TitleTruncate 根据限制截断字符串
/**
20中文字 40个英文字符内，且英文单词不截断。超出的+ ...
@param input 输入字符串
@param chineseLimit 中文字符限制
@param englishLimit 英文字符限制
*/
func TitleTruncate(input string, chineseLimit int, englishLimit int) string {
	var length int         // 当前字符串长度
	var runeCount int      // 字符计数
	lastTruncateIndex := 0 // 上一次可截断的索引

	if chineseLimit == 0 {
		chineseLimit = 20
	}

	if englishLimit == 0 {
		englishLimit = 40
	}

	for i, r := range input {
		if unicode.Is(unicode.Han, r) {
			length += 2 // 中文字符宽度
		} else if r >= 'a' && r <= 'z' || r >= 'A' && r <= 'Z' || unicode.IsDigit(r) {
			length++ // 英文字母或数字宽度
		} else {
			length++ // 其他字符宽度
		}

		// 记录空格或其他截断点
		// if r == ' ' || r == '|' || r == '：' || r == ':' {
		//	lastSpaceIndex = i
		// }

		// 超过限制
		if length > chineseLimit*2 || runeCount >= englishLimit {
			// 优先按照空格或其他截断点截断
			lastTruncateIndex = i
			return input[:lastTruncateIndex] + "..."
		}

		runeCount++
	}

	return input
}

// DocWithEmbeddingState 向量状态过滤
func DocWithEmbeddingState(state ai.DocEmbeddingState, scopedAssistantId ...uint64) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		switch state {
		case ai.DocEmbeddingState_DOC_EMBEDDING_STATE_DELETING:
			db.Scopes(DocWithCollectionDeleting(scopedAssistantId...))
		case ai.DocEmbeddingState_DOC_EMBEDDING_STATE_SYNCED:
			db.Scopes(DocWithCollectionSyncSucceed(scopedAssistantId...))
		case ai.DocEmbeddingState_DOC_EMBEDDING_STATE_SYNCING:
			db.Scopes(DocWithCollectionSyncing(scopedAssistantId...))
		}
		return db
	}
}

// DocWithCollectionDeleting 删除中的状态
// tdoc表state 状态为删除中 或者 t_assistant_doc状态为删除中
func DocWithCollectionDeleting(scopedAssistantId ...uint64) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		ctx := db.Statement.Context
		or := NewQuery[TDoc](ctx).Where("state = ?", ai.DocState_DOC_STATE_DELETING).DB()

		subQ := NewQuery[TAssistantDoc](ctx).
			DB().
			Select("1").
			Where("t_assistant_doc.doc_id = t_doc.id").
			Where("t_assistant_doc.state = ?", ai.DocState_DOC_STATE_DELETING)

		if len(scopedAssistantId) != 0 {
			subQ.Where("t_assistant_doc.assistant_id IN (?)", scopedAssistantId)
		}
		// 存在正在删除的状态
		or.Or("EXISTS (?)", subQ)

		return db.Where(or)
	}
}

// buildDocSyncStatusQuery 构建文档同步状态查询
// synced - true表示已同步完成，false表示同步中
// 基于scopedAssistantId（用户管理的助手列表）来判断向量状态：
// 1. 如果doc没有绑定到scopedAssistantId中的任何助手，不会被此查询匹配（返回UNSPECIFIED）
// 2. 如果doc绑定到scopedAssistantId中的助手，显示对应的同步状态
// 3. 特殊处理：即使t_assistant_doc记录被删除，但t_doc_sync_version中还有对应助手的同步记录，仍显示同步状态
func buildDocSyncStatusQuery(db *gorm.DB, synced bool, scopedAssistantId ...uint64) *gorm.DB {
	// 不限定助手，直接使用 t_doc表的字段查询
	if len(scopedAssistantId) == 0 {
		if synced {
			// 至少同步过一次
			return db.Where("version = rag_version and rag_version <> 0")
		}
		// 至少请求过同步
		return db.Where("version != rag_version and version <> 0")
	}

	ctx := db.Statement.Context

	// 构建查询：在scopedAssistantId中启用的文档
	enabledInScopedAssistantsSubQuery := NewQuery[TAssistantDoc](ctx).
		DB().
		Select("1").
		Where("t_assistant_doc.doc_id = t_doc.id").
		Where("t_assistant_doc.assistant_id IN (?)", scopedAssistantId).
		Where("t_assistant_doc.state = ?", ai.DocState_DOC_STATE_ENABLED)

	// 构建查询：在scopedAssistantId中有sync_version记录的文档（包括孤立记录）
	syncVersionInScopedSubQuery := NewQuery[TDocSyncVersion](ctx).
		DB().
		Select("1").
		InnerJoins("JOIN t_assistant_collection ON t_doc_sync_version.collection_id = t_assistant_collection.collection_id").
		Where("t_doc_sync_version.doc_id = t_doc.id").
		Where("t_assistant_collection.assistant_id IN (?)", scopedAssistantId)

	// 构建查询：在scopedAssistantId中同步中的文档
	syncingInScopedSubQuery := syncVersionInScopedSubQuery.Session(&gorm.Session{}).
		Where("t_doc_sync_version.doc_version != t_doc_sync_version.rag_version and t_doc_sync_version.doc_version > 0")

	// 构建查询：在scopedAssistantId中至少同步过一次的文档
	syncedInScopedSubQuery := syncVersionInScopedSubQuery.Session(&gorm.Session{}).
		Where("t_doc_sync_version.rag_version > 0")

	// 构建查询：在scopedAssistantId中启用但缺失sync_version记录的文档
	missingSyncVersionInScopedSubQuery := NewQuery[TAssistantDoc](ctx).
		DB().
		Select("1").
		Where("t_assistant_doc.doc_id = t_doc.id").
		Where("t_assistant_doc.assistant_id IN (?)", scopedAssistantId).
		Where("t_assistant_doc.state = ?", ai.DocState_DOC_STATE_ENABLED).
		Where("NOT EXISTS (?)",
			NewQuery[TDocSyncVersion](ctx).
				DB().
				Select("1").
				InnerJoins("JOIN t_assistant_collection ON t_doc_sync_version.collection_id = t_assistant_collection.collection_id").
				Where("t_doc_sync_version.doc_id = t_assistant_doc.doc_id").
				Where("t_assistant_collection.assistant_id = t_assistant_doc.assistant_id"))

	// 构建主查询条件：必须在scopedAssistantId范围内有关联（启用状态或有sync_version记录）
	inScopeQuery := NewQuery[TDoc](ctx).DB()
	inScopeQuery.Where("EXISTS (?)", enabledInScopedAssistantsSubQuery)
	inScopeQuery.Or("EXISTS (?)", syncVersionInScopedSubQuery)

	if synced {
		// 已同步：在scope内且已同步完成，排除同步中的和缺失记录的
		return db.Where(inScopeQuery).
			Where("EXISTS (?)", syncedInScopedSubQuery).
			Where("NOT EXISTS (?)", syncingInScopedSubQuery).
			Where("NOT EXISTS (?)", missingSyncVersionInScopedSubQuery)
	} else {
		// 同步中：在scope内且（有未完成同步的记录 或 启用但缺失sync_version记录）
		syncingConditions := NewQuery[TDoc](ctx).DB()
		syncingConditions.Where("EXISTS (?)", syncingInScopedSubQuery)
		syncingConditions.Or("EXISTS (?)", missingSyncVersionInScopedSubQuery)

		return db.Where(inScopeQuery).Where(syncingConditions)
	}
}

// DocWithCollectionSyncing 查询向量同步中的 doc
func DocWithCollectionSyncing(scopedAssistantId ...uint64) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return buildDocSyncStatusQuery(db, false, scopedAssistantId...)
	}
}

// DocWithCollectionSyncSucceed 查询向量同步完成的 doc
func DocWithCollectionSyncSucceed(scopedAssistantId ...uint64) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return buildDocSyncStatusQuery(db, true, scopedAssistantId...)
	}
}

// DocWithAssistantIdFilter doc添加助手id过滤条件
// excluded=false: 只查询绑定了对应助手的doc
// excluded=true:  只查询没有绑定了对应助手的doc
func DocWithAssistantIdFilter(assistantId []uint64, excluded bool, states []ai.DocState, isOr ...bool) func(db *gorm.DB) *gorm.DB {
	return func(tx *gorm.DB) *gorm.DB {
		if len(assistantId) == 0 {
			return tx
		}

		subQuery := NewQuery[TAssistantDoc](tx.Statement.Context).
			DB().
			Select("1").
			Where("assistant_id IN (?)", assistantId).
			Where("doc_id = t_doc.id")
		if len(states) != 0 {
			subQuery.Where("state IN (?)", states)
		}

		if len(isOr) > 0 && isOr[0] {
			if excluded {
				return tx.Or("NOT EXISTS (?)", subQuery)
			}
			return tx.Or("EXISTS (?)", subQuery)
		}

		if excluded {
			return tx.Where("NOT EXISTS (?)", subQuery)
		}

		return tx.Where("EXISTS (?)", subQuery)
	}
}

// DocWithSharedReceiverFilter 分享筛选，或关系
func DocWithSharedReceiverFilter(receivers *ai.DocSharedReceiverFilter) func(db *gorm.DB) *gorm.DB {
	return func(tx *gorm.DB) *gorm.DB {
		if receivers == nil {
			return tx
		}
		scope := NewQuery[TDoc](tx.Statement.Context).DB()
		// 分享给助手
		if len(receivers.AssistantId) != 0 {
			scope.Scopes(DocWithSharedAssistantIdFilter(receivers.AssistantId, true))
		}
		// 分享给用户
		if len(receivers.TeamId) != 0 {
			scope.Scopes(DocWithShareTargetIdFilter(receivers.UserId, ai.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_USER, true))
		}
		// 分享给团队
		scope.Scopes(DocWithShareTargetIdFilter(receivers.TeamId, ai.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_TEAM, true))
		return tx.Where(scope)
	}
}

// DocWithSharedAssistantIdFilter 分享至的助手
// zeroAsNoAssistant 是否把assistantId中的 0 值视为没有分享至助手
func DocWithSharedAssistantIdFilter(assistantId []uint64, zeroAsNoAssistant bool) func(db *gorm.DB) *gorm.DB {
	return func(tx *gorm.DB) *gorm.DB {
		if len(assistantId) == 0 {
			return tx
		}
		// 扩号扩住的条件组
		quoteQuery := NewQuery[TDoc](tx.Statement.Context).DB()

		hasZero := slices.Contains(assistantId, 0)
		if hasZero && zeroAsNoAssistant {
			quoteQuery.Scopes(DocWithNoSharedAssistantIdFilter(true))
			assistantId = slices.DeleteFunc(assistantId, func(id uint64) bool {
				return id == 0
			})
		}

		// 去除0后，如果还有值，则构建子查询
		if len(assistantId) != 0 {
			// 构建子查询
			subQuery := NewQuery[TAssistantDoc](tx.Statement.Context).
				DB().
				Select("1").
				Where("assistant_id IN (?)", assistantId).
				Where("is_shared = ?", 2).
				Where("state IN (?)", DocStateCanSee).
				Where("doc_id = t_doc.id")

			// 使用 EXISTS 判断子查询是否存在
			quoteQuery.Or("EXISTS (?)", subQuery)
		}

		return tx.Where(quoteQuery)
	}
}

// DocWithReceivedShareFilter 筛选收到分享的知识
// 根据新的分享定义：
// 1. 用户收到分享：t_doc_share 表中存在 share_type=USER 且 target_id=用户ID 的记录
// 2. 团队收到分享：t_doc_share 表中存在 share_type=TEAM 且 target_id=团队ID 的记录
// 3. 助手收到分享：t_assistant_doc 表中存在 is_shared=2 的记录
func DocWithReceivedShareFilter(receiverType base.IdentityType, receiverID uint64, managedAssistantIDs []uint64) func(db *gorm.DB) *gorm.DB {
	return func(tx *gorm.DB) *gorm.DB {
		ctx := tx.Statement.Context
		receivedQuery := NewQuery[TDoc](ctx).DB()

		// 如果有管理的助手，还需要查询助手收到的分享
		// 注意：这里只查询 is_shared=2 的记录，表示助手直接收到分享
		// is_shared=1 的记录表示用户绑定，不算助手收到分享
		if len(managedAssistantIDs) > 0 {
			receivedQuery.Scopes(DocWithSharedAssistantIdFilter(managedAssistantIDs, false))
		}

		switch receiverType {
		case base.IdentityType_IDENTITY_TYPE_USER:
			// 用户收到分享：查询 t_doc_share 表
			receivedQuery.Scopes(DocWithShareTargetIdFilter(
				[]uint64{receiverID},
				ai.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_USER,
				true,
			))
		case base.IdentityType_IDENTITY_TYPE_TEAM:
			// 团队收到分享：查询 t_doc_share 表
			receivedQuery.Scopes(DocWithShareTargetIdFilter(
				[]uint64{receiverID},
				ai.DocShareReceiverType_DOC_SHARE_RECEIVER_TYPE_TEAM,
				true,
			))
		}

		return tx.Where(receivedQuery)
	}
}

// DocWithNoSharedAssistantIdFilter 指定没有分享至的助手
func DocWithNoSharedAssistantIdFilter(isOr ...bool) func(db *gorm.DB) *gorm.DB {
	return func(tx *gorm.DB) *gorm.DB {
		// 构建子查询
		subQuery := NewQuery[TAssistantDoc](tx.Statement.Context).
			DB().
			Select("1").
			Where("is_shared = ?", 2).
			Where("doc_id = t_doc.id")
		if len(isOr) > 0 && isOr[0] {
			return tx.Or("NOT EXISTS (?)", subQuery)
		}
		return tx.Where(" NOT EXISTS (?)", subQuery)
	}
}

// DocStateCanSee 可以查看的状态,启用、禁用、删除中
var DocStateCanSee = []ai.DocState{ai.DocState_DOC_STATE_ENABLED, ai.DocState_DOC_STATE_DISABLED, ai.DocState_DOC_STATE_DELETING}

// DocWithNoAssistantIdFilter 指定没有绑定的助手
func DocWithNoAssistantIdFilter(scopedAssistantId []uint64, isOr ...bool) func(tx *gorm.DB) *gorm.DB {
	return func(tx *gorm.DB) *gorm.DB {
		subQuery := NewQuery[TAssistantDoc](tx.Statement.Context).
			DB().
			Select("1").
			Where("doc_id = t_doc.id")
		if len(scopedAssistantId) != 0 {
			subQuery.Where("assistant_id IN (?)", scopedAssistantId)
			subQuery.Where("state IN (?)", DocStateCanSee)
		}
		if len(isOr) > 0 && isOr[0] {
			return tx.Or("NOT EXISTS (?)", subQuery)
		}
		return tx.Where("NOT EXISTS (?)", subQuery)
	}
}

// WithContributorFilter 贡献者过滤条件
func WithContributorFilter(contributor []*ai.ContributorFilter, excluded ...bool) func(*gorm.DB) *gorm.DB {
	return func(tx *gorm.DB) *gorm.DB {
		if len(contributor) != 0 {
			var conditions []string
			var args []interface{}

			// 构建不同的条件
			for _, v := range contributor {
				if v.OnlyMatchType {
					conditions = append(conditions, "(contributor_type = ?)")
					args = append(args, v.Contributor.Type)
				} else {
					if v.Contributor.Type != base.IdentityType_IDENTITY_TYPE_CUSTOM {
						conditions = append(conditions, "(contributor_id = ? and contributor_type = ?)")
						args = append(args, v.Contributor.Id, v.Contributor.Type)
					} else {
						conditions = append(conditions, "(contributor_text = ? and contributor_type = ?)")
						args = append(args, v.Contributor.Text, v.Contributor.Type)
					}
				}
			}

			// 合并所有条件
			condition := "(" + strings.Join(conditions, " OR ") + ")"

			// 构建子查询
			subQ := NewQuery[TDocContributor](tx.Statement.Context).
				DB().
				Select("1").
				Where(condition, args...).
				Where("doc_id = t_doc.id")

			// 根据 excluded 参数决定是否排除这些记录
			if len(excluded) != 0 && excluded[0] == true {
				return tx.Not("EXISTS (?)", subQ)
			}
			return tx.Where("EXISTS (?)", subQ)
		}
		return tx
	}
}

// DocWithStateFilter 状态过滤
func DocWithStateFilter(state []ai.DocState, scopedAssistantId ...uint64) func(*gorm.DB) *gorm.DB {
	return func(tx *gorm.DB) *gorm.DB {
		// 构建子查询
		subQ := NewQuery[TAssistantDoc](tx.Statement.Context).
			DB().
			Select("1").
			Where("state IN (?)", state).
			Where("doc_id = t_doc.id")

		// 如果有 scopedAssistantId，则进一步限制条件
		if len(scopedAssistantId) != 0 {
			subQ = subQ.Where("assistant_id IN (?)", scopedAssistantId)
		}

		// 使用 EXISTS 判断子查询是否存在
		return tx.Where("EXISTS (?)", subQ)
	}
}

// DocWithSharedStateFilter 分享状态过滤
func DocWithSharedStateFilter(state []ai.DocState, scopedAssistantId ...uint64) func(*gorm.DB) *gorm.DB {
	return func(tx *gorm.DB) *gorm.DB {
		// 构建子查询
		subQ := NewQuery[TAssistantDoc](tx.Statement.Context).
			DB().
			Select("1").
			Where("state IN (?)", state).
			Where("is_shared = ?", 2).
			Where("doc_id = t_doc.id")

		// 如果有 scopedAssistantId，则进一步限制条件
		if len(scopedAssistantId) != 0 {
			subQ = subQ.Where("assistant_id IN (?)", scopedAssistantId)
		}

		// 使用 EXISTS 判断子查询是否存在
		return tx.Where("EXISTS (?)", subQ)
	}
}

// DocWithUpdateByFilter 操作人过滤
func DocWithUpdateByFilter(operator ...*ai.OperatorFilter) func(*gorm.DB) *gorm.DB {
	return func(tx *gorm.DB) *gorm.DB {
		if len(operator) == 0 {
			return tx
		}

		var conditions []string
		var args []interface{}

		// 构建过滤条件
		for _, v := range operator {
			if v.OnlyMatchType {
				conditions = append(conditions, "(update_by_type = ?)")
				args = append(args, v.Operator.Type)
			} else {
				if v.Operator.UserId == 0 {
					conditions = append(conditions, "(update_by = ? and update_by_type = ?)")
					args = append(args, v.Operator.Id, v.Operator.Type)
				} else {
					conditions = append(conditions, "(update_by = ? and update_by_type = ? and update_by_user = ?)")
					args = append(args, v.Operator.Id, v.Operator.Type, v.Operator.UserId)
				}
			}
		}
		condition := strings.Join(conditions, " OR ")

		// 使用动态生成的条件和参数执行查询
		return tx.Where(condition, args...)
	}
}

// DocWithCreateByFilter 创建人过滤
// DocWithCreateByFilter 创建人过滤
func DocWithCreateByFilter(operator ...*ai.OperatorFilter) func(*gorm.DB) *gorm.DB {
	return func(tx *gorm.DB) *gorm.DB {
		if len(operator) == 0 {
			return tx
		}

		var conditions []string
		var args []interface{}

		// 构建过滤条件
		for _, v := range operator {
			if v.OnlyMatchType {
				conditions = append(conditions, "(create_by_type = ?)")
				args = append(args, v.Operator.Type)
			} else {
				if v.Operator.UserId == 0 {
					conditions = append(conditions, "(create_by = ? and create_by_type = ?)")
					args = append(args, v.Operator.Id, v.Operator.Type)
				} else {
					conditions = append(conditions, "(create_by = ? and create_by_type = ? and create_by_user = ?)")
					args = append(args, v.Operator.Id, v.Operator.Type, v.Operator.UserId)
				}
			}
		}
		condition := strings.Join(conditions, " OR ")
		// 使用动态生成的条件和参数执行查询
		return tx.Where(condition, args...)
	}
}

// DocWithMd5Repeated 过虑md5有重复的
func DocWithMd5Repeated() func(*gorm.DB) *gorm.DB {
	return func(tx *gorm.DB) *gorm.DB {
		subQ := tx.WithContext(tx.Statement.Context).Select("index_text_md5").
			Where("index_text_md5 != ?", EmptyStringMD5). // 过滤空字符串MD5
			Group("index_text_md5").Having("count(*) > 1")
		return tx.Where("index_text_md5 IN (?)", subQ)

		// 构建子查询，使用 EXISTS 判断重复的 md5
		// subQ := tx.WithContext(tx.Statement.Context).
		// 	Table("t_doc as t2").
		// 	Select("1").
		// 	Where("t2.index_text_md5 = t_doc.index_text_md5").
		// 	Where("t2.id != t_doc.id")
		//
		// // 使用 EXISTS 判断子查询是否存在
		// return tx.Where("EXISTS (?)", subQ)
	}
}

// DocWithColumnMatch 全文搜索
func DocWithColumnMatch(column, text string, isOr ...bool) func(*gorm.DB) *gorm.DB {
	return func(tx *gorm.DB) *gorm.DB {
		cond := fmt.Sprintf("MATCH(%s) AGAINST(? IN BOOLEAN MODE)", column)
		arg := `"` + text + `"`
		if len(isOr) != 0 && isOr[0] {
			return tx.Or(cond, arg)
		}
		return tx.Where(cond, arg)
	}
}

// DocWithParseModeFilter 过滤解析模式
func DocWithParseModeFilter(parseMode []ai.DocParseMode) func(*gorm.DB) *gorm.DB {
	return func(tx *gorm.DB) *gorm.DB {
		if len(parseMode) == 0 {
			return tx
		}
		subQ := NewQuery[TDocExtend](tx.Statement.Context).DB().Select("doc_id").
			Where("parse_mode in ?", parseMode)
		return tx.Where("id IN (?)", subQ)
	}
}

// DocWithDataSourceStateFilter 外部数据源状态
func DocWithDataSourceStateFilter(state uint32) func(*gorm.DB) *gorm.DB {
	return func(tx *gorm.DB) *gorm.DB {
		if state != 0 {
			// 构建子查询
			subQ := NewQuery[TDocExternalSource](tx.Statement.Context).
				DB().
				Select("1").
				Where("state = ?", state).
				Where("doc_id = t_doc.id")
			// 使用 EXISTS 判断子查询是否存在
			return tx.Where("EXISTS (?)", subQ)
		}
		return tx
	}
}

// FillContributorsFromProto 填充contributor至模型中
func (t *TDoc) FillContributorsFromProto(contributor ...*ai.Contributor) {
	for _, v := range contributor {
		t.Contributors = append(t.Contributors, &TDocContributor{
			DocID:         t.ID,
			ContributorID: v.Id,
			ContributorText: func() string {
				if v.Id == 0 {
					return v.Text
				}
				return ""
			}(),
			ContributorType: uint32(v.Type),
		})
	}
}

func (t *TDoc) FillLabelsFromProto(labels []*ai.CustomLabel) {
	for _, v := range labels {
		dt := ai.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_FILE
		if t.GetDocType() == uint32(ai.DocType_DOCTYPE_QA) {
			dt = ai.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_QA
		}
		t.Labels = append(t.Labels, (&TObjectLabel{}).FromLabelPb(t.ID, dt, v))
	}
}

// FillStatesFromProto 填充状态
func (t *TDoc) FillStatesFromProto(states ...*ai.DocAssistantState) {
	for _, v := range states {
		if v != nil {
			item := &TAssistantDoc{
				DocID:       t.ID,
				AssistantID: v.AssistantId,
				State:       v.State,
				IsShared:    v.IsShared,
			}
			t.States = append(t.States, item)
		}
	}
}

// FillAssistantsFromId 填充助手id
func (t *TDoc) FillAssistantsFromId(assistant ...uint64) {
	if len(assistant) != 0 {
		for _, v := range assistant {
			t.Assistants = append(t.Assistants, &TAssistant{
				ID: v,
			})
		}
	}
}

// SetStates 设置所有助手的状态
func (t *TDoc) SetStates(state ai.DocState) {
	for _, v := range t.States {
		v.State = state
	}
}

// StatesToProto 状态信息转proto
func (t *TDoc) StatesToProto() []*ai.DocAssistantState {
	if len(t.States) == 0 {
		return nil
	}
	s := make([]*ai.DocAssistantState, 0, len(t.States))
	for _, v := range t.States {
		s = append(s, &ai.DocAssistantState{
			AssistantId: v.AssistantID,
			State:       v.State,
			IsShared:    v.IsShared,
		})
	}
	return s
}

// ContributorToPb 贡献者转为pb结构
func (t *TDoc) ContributorToPb() []*ai.Contributor {
	if len(t.Contributors) == 0 {
		return nil
	}
	c := make([]*ai.Contributor, 0, len(t.Contributors))
	for _, v := range t.Contributors {
		c = append(c, &ai.Contributor{
			Type: base.IdentityType(v.ContributorType),
			Text: v.ContributorText,
			Id:   v.ContributorID,
		})
	}
	return c
}

// HasCopy 判断是否有副本
func (t *TDoc) HasCopy(ctx context.Context) (bool, error) {
	hasCopy, err := NewQuery[TDoc](ctx).
		Where("main_id = ?", t.ID).
		Where("is_copy = ?", 1).
		Limit(1).
		Count()
	if err != nil {
		return false, fmt.Errorf("count copy: %w", err)
	}
	return hasCopy > 0, nil
}

func LoadDoc(ctx context.Context, ids []uint64, selects []string, scopedAssistants ...uint64) ([]*TDoc, error) {
	data := make([]*TDoc, 0, len(ids))
	db := NewQuery[TDoc](ctx).DB()
	if len(selects) > 0 {
		db.Select(selects)
	}
	db = db.Preload("Assistants", AssistantWithCollection, func(db *gorm.DB) *gorm.DB {
		// Assistants 只读取id，忽略其他列
		return db.Select("id")
	}).Preload("States", func(db *gorm.DB) *gorm.DB {
		if len(scopedAssistants) != 0 {
			return db.Where("assistant_id in (?)", scopedAssistants)
		}
		return db
	}).Preload("MatchPatterns").Find(&data, "id IN ?", ids)
	if err := db.Error; err != nil {
		return nil, err
	}
	return data, nil
}

// LoadDocWithTx 获取 doc 信息，包括助手信息、collection 信息，状态信息
// scopedAssistants 只查询限定助手的状态
func LoadDocWithTx(tx *gorm.DB, ids []uint64, selects []string, scopedAssistants ...uint64) ([]*TDoc, error) {
	data := make([]*TDoc, 0, len(ids))
	db := tx.Model(&TDoc{})
	if len(selects) > 0 {
		db.Select(selects)
	}
	err := db.
		Preload("Assistants", AssistantWithCollection, func(db *gorm.DB) *gorm.DB {
			// Assistants 只读取id，忽略其他列
			return db.Select("id")
		}).
		Preload("States", func(db *gorm.DB) *gorm.DB {
			if len(scopedAssistants) != 0 {
				return db.Where("assistant_id in (?)", scopedAssistants)
			}
			return db
		}).
		Preload("MatchPatterns").
		Find(&data, "id IN ?", ids).Error
	if err != nil {
		return nil, err
	}
	return data, nil
}

// DocWithLLMRecallFilter 过滤只使用了大模型召回的知识
func DocWithLLMRecallFilter(db *gorm.DB) *gorm.DB {
	return db.Scopes(DocWithMatchPatternFilter(ai.DocMatchPattern_DOC_MATCH_PATTERN_LARGE_MODEL_RECALL))
}

// DocWithMatchPatternFilter 匹配模式过滤
func DocWithMatchPatternFilter(p ...ai.DocMatchPattern) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		// 构建子查询
		subQ := NewQuery[TDocMatchPattern](db.Statement.Context).
			DB().
			Select("1").
			Where("match_pattern in (?)", p).
			Where("doc_id = t_doc.id")

		// 使用 EXISTS 判断子查询是否存在
		return db.Where("EXISTS (?)", subQ)
	}
}

// QARef Doc的参考资料，包括qa/文本文件
// 历史原因，之前只有qa有参考资料，所以命名为这个
type QARef struct {
	Id   uint64 `json:",omitempty"`
	Text string `json:",omitempty"`
}

// DocReference doc的参考资料
type DocReference struct {
	QaRef     []*QARef `json:",omitempty"` // qa/文本文件的参考资料，包括纯字符和引用
	Name      string   `json:",omitempty"` // 文件名称
	Url       string   `json:",omitempty"` // 文件链接
	ParsedUrl string   `json:",omitempty"` // 文件解析后生成的文件链接
}

// GetDocType 获取doc的类型 1:qa 2:文本 3:文件
func (t *TDoc) GetDocType() uint32 {
	if t.DataType != 0 {
		return t.DataType
	}
	// qa没有文件名
	if len(t.FileName) == 0 {
		return uint32(ai.DocType_DOCTYPE_QA)
	}
	if t.Ref == nil {
		return uint32(ai.DocType_DOCTYPE_UNSPECIFIED)
	}
	// 文本只有名称，没有url
	if len(t.Ref.Url) == 0 {
		return uint32(ai.DocType_DOCTYPE_TEXT)
	}
	// 文件既有名称也有url
	if len(t.Ref.Name) != 0 {
		return uint32(ai.DocType_DOCTYPE_FILE)
	}
	return uint32(ai.DocType_DOCTYPE_UNSPECIFIED)
}

// UpdateDocVersion 更新doc版本号，只能递增加1
func UpdateDocVersion(tx *gorm.DB, id []uint64, cnt map[uint64]uint32) error {
	// 构建 CASE 语句的条件部分
	conditions := make([]string, 0, len(id))
	args := make([]interface{}, 0, len(id)*2) // 每个 id 需要两个参数：一个用于 WHEN，一个用于 THEN

	for _, v := range id {
		conditions = append(conditions, fmt.Sprintf("WHEN ? THEN version + ?"))
		args = append(args, v, cnt[v])
	}

	sql := fmt.Sprintf("CASE id %s END", strings.Join(conditions, " "))
	return tx.Model(&TDoc{}).Where("id IN ?", id).Omit("update_date").Update("version", gorm.Expr(sql, args...)).Error
}

// TryDeleteDoc 删除 doc，需要 doc 已经同步完成
func TryDeleteDoc(tx *gorm.DB, id uint64) error {
	doc := &TDoc{}
	err := tx.Model(&TDoc{}).Preload("States").First(&doc, "id = ?", id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil
		}
		return err
	}
	//  doc有绑定助手且已同步完成
	// if doc.Version == doc.RagVersion && doc.State == ai.DocState_DOC_STATE_DELETING {
	if doc.Version == doc.RagVersion {
		// 如果 doc 没有被标记删除，直接返回
		// if len(doc.States) == 0 && doc.State != ai.DocState_DOC_STATE_DELETING {
		if doc.State != ai.DocState_DOC_STATE_DELETING {
			return nil
		}
		for _, v := range doc.States {
			if v.State != ai.DocState_DOC_STATE_UNBOUNDED {
				return nil
			}
		}
		// 所有绑定的助手，状态都为删除中时，真正删除doc
		return tx.Model(&TDoc{ID: id}).Select(clause.Associations).Omit("Labels").Delete(&TDoc{ID: id}).Error
	}
	return nil
}

// UpdateDocRagVersion 更新doc rag的版本号
func UpdateDocRagVersion(tx *gorm.DB, id uint64, cnt uint64) error {
	err := tx.Model(&TDoc{}).Where("id = ?", id).Omit("update_date").Update("rag_version", gorm.Expr("rag_version + ?", cnt)).Error
	if err != nil {
		return err
	}
	return TryDeleteDoc(tx, id)
}

type QARefFile struct {
	Id   uint64 `json:"id"`
	Name string `json:"name"`
	Url  string `json:"url"`
}

// TDocs 文档列表
type TDocs []*TDoc

// ToTextFilePb 协议转换
func (t TDocs) ToTextFilePb() []*ai.TextFile {
	pbs := make([]*ai.TextFile, 0, len(t))
	for _, m := range t {
		pbs = append(pbs, m.ToTextFileProto())
	}
	return pbs
}

// ToFullTextFilePb 协议转换
func (t TDocs) ToFullTextFilePb() []*ai.FullTextFile {
	pbs := make([]*ai.FullTextFile, 0, len(t))
	for _, m := range t {
		pbs = append(pbs, m.ToFullTextFilePb())
	}
	return pbs
}

// ToChatMessageDocs ...
func (t TDocs) ToChatMessageDocs() []*ai.ChatMessageDoc {
	pbs := make([]*ai.ChatMessageDoc, 0, len(t))
	for _, m := range t {
		pbs = append(pbs, m.ToChatMessageDoc())
	}
	return pbs
}
