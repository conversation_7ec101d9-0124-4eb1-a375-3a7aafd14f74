// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TDocSyncVersion [...]
type TDocSyncVersion struct {
	DocID        uint64 `gorm:"column:doc_id" json:"docId"`
	CollectionID uint64 `gorm:"column:collection_id" json:"collectionId"`
	DocVersion   uint64 `gorm:"column:doc_version;default:0" json:"docVersion"`
	RagVersion   uint64 `gorm:"column:rag_version;default:0" json:"ragVersion"`

	AssistantCollection *TAssistantCollection `gorm:"foreignKey:collection_id;references:collection_id" json:"assistantCollection"`
}

// TableName get sql table name.获取数据库表名
func (m *TDocSyncVersion) TableName() string {
	return "t_doc_sync_version"
}

// TDocSyncVersionColumns get sql column name.获取数据库列名
var TDocSyncVersionColumns = struct {
	DocID        string
	CollectionID string
	DocVersion   string
	RagVersion   string
}{
	DocID:        "doc_id",
	CollectionID: "collection_id",
	DocVersion:   "doc_version",
	RagVersion:   "rag_version",
}
