package ai

import (
	"context"
	"slices"
	"strings"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	ailogic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/ai"
	iamlogic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/iam"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/ai"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	mclient "github.com/asim/go-micro/v3/client"
	"golang.org/x/sync/errgroup"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/fieldmaskpb"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// ListQA 获取QA列表
func (a *Ai) ListQA(ctx context.Context, req *bffaipb.ReqListQA, rsp *bffaipb.RspListQA) error {
	pbReq, managedAssistants, contributor, err := ailogic.CreateAiListQA(ctx, req)
	if err != nil {
		return err
	}

	pbRsp, err := client.AiNational.ListQA(ctx, pbReq, mclient.WithRequestTimeout(30*time.Second))
	if err != nil {
		log.WithContext(ctx).Errorf("list ai collection doc qa failed: %v", err)
		return err
	}
	var operators []*aipb.Operator
	for _, v := range pbRsp.Items {
		if v.CreateBy != nil {
			operators = append(operators, v.CreateBy)
		}
		if v.UpdateBy != nil {
			operators = append(operators, v.UpdateBy)
		}
	}
	operatorsToShow, err := ailogic.GetAiDocOperatorsShowInfo(ctx, operators...)
	if err != nil {
		return err
	}
	var contributors []*aipb.Contributor
	for _, v := range pbRsp.Items {
		contributors = append(contributors, v.Contributor...)
	}
	_, err = ailogic.GeAiDoctContributorShowInfo(ctx, contributors...)
	if err != nil {
		return err
	}
	contributor, err = ailogic.GetUserAsContributor(ctx)
	if err != nil {
		return err
	}

	var sharedReceivers []*aipb.DocShareReceiver
	for _, v := range pbRsp.Items {
		sharedReceivers = append(sharedReceivers, v.ShareReceivers...)
	}
	sharedTeams, sharedUsers, err := ailogic.GetDocShareTeamUserShowInfo(ctx, sharedReceivers)
	if err != nil {
		return err
	}

	for _, v := range pbRsp.Items {
		owned, shared, isContributor := ailogic.SpiltDocAssistantsByShared(contributor, managedAssistants, v.Contributor, v.States, &v.State)
		team, user := ailogic.BuildDocShareReceiversConditional(isContributor, v.ShareReceivers, sharedTeams, sharedUsers)
		// 检查是否收到分享
		receivedShare := ailogic.CheckReceivedShare(ctx, isContributor, contributor, managedAssistants, v.ShareReceivers, v.States)
		rsp.Items = append(rsp.Items, &bffaipb.CollectionQA{
			Id:               v.Id,
			Question:         v.Question,
			Answer:           v.Answer,
			Contributor:      v.Contributor,
			Assistants:       v.Assistants,
			Reference:        v.Reference,
			HitCount:         v.HitCount,
			CreateBy:         operatorsToShow[v.CreateBy],
			UpdateBy:         operatorsToShow[v.UpdateBy],
			UpdateDate:       v.UpdateDate,
			CreateDate:       v.CreateDate,
			EmbeddingState:   ailogic.MergeDocVersionLag(managedAssistants, v.EmbeddingVersion),
			States:           owned,
			SharedStates:     shared,
			SharedTeams:      team,
			SharedUsers:      user,
			ShowContributor:  v.ShowContributor,
			State:            v.State,
			Labels:           v.Labels,
			MatchPatterns:    v.MatchPatterns,
			HasRepeated:      v.HasRepeated,
			QuestionOversize: v.QuestionOversize,
			ReceivedShare:    receivedShare,
		})
	}
	rsp.TotalCount = pbRsp.Total
	return nil
}

// ListTextFiles 获取文本/文件列表
func (a *Ai) ListTextFiles(ctx context.Context, req *bffaipb.ReqListTextFiles, rsp *bffaipb.RspListTextFiles) error {
	opt := &ailogic.ListTextFilesOption{WithTips: true}
	return ailogic.ListTextFiles(ctx, req, rsp, true, opt)
}

// GetTextFile id获取文件/文本详情
func (a *Ai) GetTextFile(ctx context.Context, req *bffaipb.ReqGetTextFile, rsp *bffaipb.RspGetTextFile) error {
	pbReq := &aipb.ReqListTextFile{
		Id: []uint64{req.Id},
	}
	pbRsp, err := client.AiNational.ListTextFile(ctx, pbReq)
	if err != nil {
		return err
	}
	if len(pbRsp.Items) == 0 {
		return nil
	}
	v := pbRsp.Items[0]
	contributor, err := ailogic.GetUserAsContributor(ctx)
	if err != nil {
		return err
	}
	managed, err := ailogic.GetManagedAssistants(ctx)
	if err != nil {
		return err
	}
	owned, shared, _ := ailogic.SpiltDocAssistantsByShared(contributor, managed, v.Doc.Contributor, v.Doc.States, &v.Doc.State)

	var operators []*aipb.Operator
	if v.Doc.CreateBy != nil {
		operators = append(operators, v.Doc.CreateBy)
	}
	if v.Doc.UpdateBy != nil {
		operators = append(operators, v.Doc.UpdateBy)
	}
	operatorsToShow, err := ailogic.GetAiDocOperatorsShowInfo(ctx, operators...)
	if err != nil {
		return err
	}

	var contributors []*aipb.Contributor
	for _, v := range pbRsp.Items {
		contributors = append(contributors, v.Doc.Contributor...)
	}
	_, err = ailogic.GeAiDoctContributorShowInfo(ctx, contributors...)
	if err != nil {
		return err
	}

	rsp.Item = &bffaipb.CollectionTextFile{
		Id:                 v.Doc.Id,
		FileName:           v.Doc.Name,
		Text:               v.Doc.Text,
		Assistants:         v.Doc.Assistants,
		States:             owned,
		SharedStates:       shared,
		Contributor:        v.Doc.Contributor,
		Url:                v.Doc.Url,
		HitCount:           v.Doc.HitCount,
		UgcType:            v.Doc.UgcType,
		UgcId:              v.Doc.UgcId,
		UpdateDate:         v.Doc.UpdateDate,
		CreateDate:         v.Doc.CreateDate,
		CreateBy:           operatorsToShow[v.Doc.CreateBy],
		UpdateBy:           operatorsToShow[v.Doc.UpdateBy],
		EmbeddingState:     ailogic.MergeDocVersionLag(managed, v.Doc.EmbeddingVersion),
		ParseProgress:      v.Doc.ParseProgress,
		ShowContributor:    v.Doc.ShowContributor,
		State:              v.Doc.State,
		Labels:             v.Doc.Labels,
		Reference:          v.Doc.Reference,
		DownloadAsRef:      v.Doc.DownloadAsRef,
		HasOverSizedTables: v.Doc.HasOverSizedTables,
		HasRepeated:        v.Doc.HasRepeated,
		DataSourceState:    v.Doc.DataSourceState,
	}
	return nil
}

// CreateQAs 创建QA
func (a *Ai) CreateQAs(ctx context.Context, req *bffaipb.ReqCreateQAs, rsp *bffaipb.RspCreateQAs) error {
	for _, v := range req.Items {
		aids, err := ailogic.RewriteAssistantIds(ctx, v.AssistantId...)
		if err != nil {
			return err
		}
		v.AssistantId = aids
	}
	contributor, err := ailogic.GetUserAsContributor(ctx)
	if err != nil {
		return err
	}
	operator, err := ailogic.GetUserAsOperator(ctx, true)
	if err != nil {
		return err
	}
	pbReq := &aipb.ReqCreateQAInBulk{Items: make([]*aipb.QA, 0, len(req.Items))}
	for _, v := range req.Items {
		item := &aipb.QA{
			Question:    v.Question,
			Answer:      v.Answer,
			Reference:   v.Reference,
			State:       v.State,
			Contributor: v.Contributor,
			CreateBy:    operator,
			UpdateBy:    operator,
			States: func() []*aipb.DocAssistantState {
				r := make([]*aipb.DocAssistantState, 0, len(v.AssistantId))
				for _, vv := range v.AssistantId {
					r = append(r, &aipb.DocAssistantState{State: v.State, AssistantId: vv})
				}
				return r
			}(),
			ShowContributor: v.ShowContributor,
			MatchPatterns:   v.MatchPatterns,
		}
		if len(item.Contributor) == 0 {
			item.Contributor = append(item.Contributor, contributor)
		}
		pbReq.Items = append(pbReq.Items, item)
		pbReq.SharedAssistant = append(pbReq.SharedAssistant, &aipb.ReqCreateQAInBulk_Slice{Id: v.ShareAssistantId})
	}
	pbRsp, err := client.AiNational.CreateQAInBulk(ctx, pbReq)
	if err != nil {
		log.WithContext(ctx).Errorf("create ai collection doc qa failed: %v", err)
		return err
	}
	rsp.Id = pbRsp.Id
	return nil
}

// CreateTextFiles collection管理里，创建文本与文件
func (a *Ai) CreateTextFiles(ctx context.Context, req *bffaipb.ReqCreateTextFiles, rsp *bffaipb.RspCreateTextFiles) error {
	for _, v := range req.Items {
		ailogic.SetDefaultDataSource(v)
		aids, err := ailogic.RewriteAssistantIds(ctx, v.AssistantId...)
		if err != nil {
			return err
		}
		v.AssistantId = aids
	}
	pbReq := &aipb.ReqCreateTextFileInBulk{Items: make([]*aipb.TextFile, 0, len(req.Items))}
	contributor, err := ailogic.GetUserAsContributor(ctx)
	if err != nil {
		return err
	}
	operator, err := ailogic.GetUserAsOperator(ctx, true)
	if err != nil {
		return err
	}
	for _, v := range req.Items {
		item := &aipb.TextFile{
			Name:        v.FileName,
			Text:        v.Text,
			Url:         v.Url,
			State:       v.State,
			Reference:   v.Reference,
			Contributor: v.Contributor,
			CreateBy:    operator,
			UpdateBy:    operator,
			UgcType:     v.UgcType,
			UgcId:       v.UgcId,
			ParsedUrl:   v.ParsedUrl,
			Type:        v.Type,
			States: func() []*aipb.DocAssistantState {
				r := make([]*aipb.DocAssistantState, 0, len(v.AssistantId))
				for _, vv := range v.AssistantId {
					r = append(r, &aipb.DocAssistantState{State: v.State, AssistantId: vv})
				}
				return r
			}(),
			ShowContributor: v.ShowContributor,
			DownloadAsRef:   v.DownloadAsRef,
			ParseMode:       v.ParseMode,
			DataSource:      v.DataSource,
		}
		if len(item.Contributor) == 0 {
			item.Contributor = append(item.Contributor, contributor)
		}
		pbReq.Items = append(pbReq.Items, item)
	}
	pbRsp, err := client.AiNational.CreateTextFileInBulk(ctx, pbReq)
	if err != nil {
		log.WithContext(ctx).Errorf("create ai collection doc text files failed: %v", err)
		return err
	}
	rsp.Id = pbRsp.Id
	return nil
}

// UpdateQAs 更新QA
func (a *Ai) UpdateQAs(ctx context.Context, req *bffaipb.ReqUpdateQAs, _ *emptypb.Empty) error {
	managedAssistants, err := ailogic.GetManagedAssistants(ctx)
	if err != nil {
		return err
	}

	var docNeedCheckEdit []uint64
	for _, v := range req.Items {
		if len(v.GetMask().GetPaths()) == 1 && v.Mask.Paths[0] == "states" {
			err = ailogic.CheckDocChangeAssistant(ctx, managedAssistants, v.States)
			if err != nil {
				return err
			}
		} else {
			docNeedCheckEdit = append(docNeedCheckEdit, v.Id)
		}
	}

	err = ailogic.CheckDocEditPermission(ctx, docNeedCheckEdit...)
	if err != nil {
		return err
	}

	labelTenantId, err := ailogic.GetCustomLabelTenantId(ctx)
	if err != nil {
		return nil
	}

	operator, err := ailogic.GetUserAsOperator(ctx, true)
	if err != nil {
		return err
	}
	pbReq := &aipb.ReqUpdateQAInBulk{Items: make([]*aipb.ReqUpdateQA, 0, len(req.Items))}
	for _, v := range req.Items {
		// 如果没有助手，需要取消states mask
		if len(managedAssistants) == 0 && v.GetMask().GetPaths() != nil {
			v.Mask.Paths = slices.DeleteFunc(v.Mask.GetPaths(), func(e string) bool {
				return e == "states"
			})
		}
		if len(v.GetMask().GetPaths()) != 0 {
			v.Mask.Paths = append(v.Mask.Paths, "update_by")
		}
		pbReq.Items = append(pbReq.Items, &aipb.ReqUpdateQA{
			Question:          v.Question,
			Answer:            v.Answer,
			Reference:         v.Reference,
			States:            v.States,
			Id:                v.Id,
			Operator:          operator,
			Contributor:       v.Contributor,
			Mask:              v.Mask,
			ShowContributor:   v.ShowContributor,
			ScopedAssistantId: managedAssistants,
			Labels:            v.Labels,
			LabelTenant:       labelTenantId,
			MatchPatterns:     v.MatchPatterns,
		})
	}
	_, err = client.AiNational.UpdateQAInBulk(ctx, pbReq)
	if err != nil {
		log.WithContext(ctx).Errorf("update ai collection doc qa failed: %v", err)
		return err
	}
	return nil
}

// 重写fieldMask
func rewriteUpdateTextFileMask(mask *fieldmaskpb.FieldMask) *fieldmaskpb.FieldMask {
	for i, v := range mask.GetPaths() {
		if v == "file_name" {
			mask.Paths[i] = "name" // 重写mask路径
		}
	}
	if len(mask.GetPaths()) != 0 {
		mask.Paths = append(mask.Paths, "update_by")
	}
	return mask
}

// UpdateTextFiles 更新文本/文件列表
func (a *Ai) UpdateTextFiles(ctx context.Context, req *bffaipb.ReqUpdateTextFiles, _ *emptypb.Empty) error {
	managedAssistants, err := ailogic.GetManagedAssistants(ctx)
	if err != nil {
		return err
	}

	var docNeedCheckEdit []uint64
	for _, v := range req.Items {
		if len(v.GetMask().GetPaths()) == 1 && v.Mask.Paths[0] == "states" {
			err = ailogic.CheckDocChangeAssistant(ctx, managedAssistants, v.States)
			if err != nil {
				return err
			}
		} else {
			docNeedCheckEdit = append(docNeedCheckEdit, v.Id)
		}
	}

	err = ailogic.CheckDocEditPermission(ctx, docNeedCheckEdit...)
	if err != nil {
		return err
	}
	operator, err := ailogic.GetUserAsOperator(ctx, true)
	if err != nil {
		return err
	}

	labelTenantId, err := ailogic.GetCustomLabelTenantId(ctx)
	if err != nil {
		return nil
	}

	pbReq := &aipb.ReqUpdateTextFileInBulk{Items: make([]*aipb.ReqUpdateTextFile, 0, len(req.Items))}
	for _, v := range req.Items {
		// 如果没有助手，需要取消states mask
		if len(managedAssistants) == 0 && v.GetMask().GetPaths() != nil {
			v.Mask.Paths = slices.DeleteFunc(v.Mask.GetPaths(), func(e string) bool {
				return e == "states"
			})
		}
		pbReq.Items = append(pbReq.Items, &aipb.ReqUpdateTextFile{
			Id:                v.Id,
			Name:              v.FileName,
			Text:              v.Text,
			Url:               v.Url,
			ParsedUrl:         v.ParsedUrl,
			States:            v.States,
			Reference:         v.Reference,
			Contributor:       v.Contributor,
			UpdateBy:          operator,
			UgcType:           v.UgcType,
			UgcId:             v.UgcId,
			Mask:              rewriteUpdateTextFileMask(v.Mask),
			ShowContributor:   v.ShowContributor,
			ScopedAssistantId: managedAssistants,
			Labels:            v.Labels,
			LabelTenant:       labelTenantId,
			DownloadAsRef:     v.DownloadAsRef,
		})
	}

	_, err = client.AiNational.UpdateTextFileInBulk(ctx, pbReq)
	if err != nil {
		log.WithContext(ctx).Errorf("update ai collection doc text files failed: %v", err)
		return err
	}
	return nil
}

// DeleteDocs 删除doc
func (a *Ai) DeleteDocs(ctx context.Context, req *bffaipb.ReqDeleteDocs, rsp *bffaipb.RspDeleteDocs) error {
	if req.QueryId == 0 && len(req.Id) == 0 {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}
	managedAssistants, err := ailogic.GetManagedAssistants(ctx)
	if err != nil {
		return err
	}
	operator, err := ailogic.GetUserAsOperator(ctx, true)
	if err != nil {
		return err
	}
	if req.QueryId > 0 {
		pbReq := &aipb.ReqDeleteDocInBulk{
			Operator:          operator,
			ScopedAssistantId: managedAssistants,
			QueryId:           req.QueryId,
		}
		rpcRsp, err := client.AiNational.DeleteDocInBulk(ctx, pbReq)
		if rpcRsp != nil {
			rsp.Async = rpcRsp.Async
		}
		return err
	}

	scopeDelete, hardDelete, err := ailogic.SplitDocsByEditPermission(ctx, req.Id...)
	if err != nil {
		return err
	}
	wg := errgroup.Group{}
	if len(scopeDelete) != 0 {
		wg.Go(func() error {
			pbReq := &aipb.ReqDeleteDocInBulk{
				Id:                  scopeDelete,
				Operator:            operator,
				ScopedAssistantId:   managedAssistants,
				QueryId:             req.QueryId,
				IsContributorDelete: false,
			}
			rpcRsp, err := client.AiNational.DeleteDocInBulk(ctx, pbReq)
			if err != nil {
				return err
			}
			if rpcRsp != nil {
				rsp.Async = rpcRsp.Async
			}
			return nil
		})
	}
	if len(hardDelete) != 0 {
		wg.Go(func() error {
			pbReq := &aipb.ReqDeleteDocInBulk{
				Id:                  hardDelete,
				Operator:            operator,
				QueryId:             req.QueryId,
				IsContributorDelete: true,
			}
			rpcRsp, err := client.AiNational.DeleteDocInBulk(ctx, pbReq)
			if err != nil {
				return err
			}
			if rpcRsp != nil {
				rsp.Async = rpcRsp.Async
			}
			return nil
		})
	}

	err = wg.Wait()
	if err != nil {
		return err
	}
	return nil
}

// SearchCollection 搜索collection
func (a *Ai) SearchCollection(ctx context.Context, req *bffaipb.ReqSearchCollection, rsp *bffaipb.RspSearchCollection) error {
	pbReq := &aipb.ReqSearchCollectionOneShot{
		Search:            req.Search,
		AssistantId:       req.AssistantId,
		DocType:           req.DocType,
		TimeRecord:        true,
		Threshold:         req.Threshold,
		TextRecallTopN:    req.TextRecallTopN,
		IsSearchChat:      true,
		TopN:              req.TopN,
		TextRecallQuery:   req.TextRecallQuery,
		TextRecallPattern: req.TextRecallPattern,
		TextRecallSlop:    req.TextRecallSlop,
		CleanChunks:       req.CleanChunks,
		Temperature:       req.Temperature,
	}

	pbReq.From = req.Offset
	if req.TopN <= req.Offset {
		pbReq.TopN = 0
	}
	if req.TextRecallTopN <= req.Offset {
		pbReq.TextRecallTopN = 0
	}

	if req.TopN > req.Offset && req.TopN < (req.Offset+req.Limit) {
		pbReq.TopN = req.TopN
	}
	if req.TextRecallTopN > req.Offset && req.TextRecallTopN < (req.Offset+req.Limit) {
		pbReq.TextRecallTopN = req.TextRecallTopN
	}
	if req.TopN >= (req.Offset + req.Limit) {
		pbReq.TopN = req.Limit
	}
	if req.TextRecallTopN >= (req.Offset + req.Limit) {
		pbReq.TextRecallTopN = req.Limit
	}

	var searchItems, searchTextItems []*aipb.SearchCollectionItem
	var startTime, endTime *timestamppb.Timestamp
	var totalCount uint32

	matchRsp, matchPattern, err := ailogic.MatchQACollection(ctx, pbReq)
	if err != nil {
		return err
	}
	if matchRsp != nil && len(matchRsp.Items) > 0 {
		searchTextItems = matchRsp.Items
		startTime = matchRsp.StartTime
		endTime = matchRsp.EndTime
	} else {
		searchRsp, err := client.AiNational.SearchCollectionOneShot(ctx, pbReq, mclient.WithRequestTimeout(60*time.Second))
		if err != nil {
			log.WithContext(ctx).Errorf("search ai collection doc qa failed: %v", err)
			return err
		}
		searchItems = searchRsp.SearchItems
		searchTextItems = searchRsp.SearchTextItems
		startTime = searchRsp.StartTime
		endTime = searchRsp.EndTime
		totalCount = searchRsp.TotalCount
	}

	var operators []*aipb.Operator
	for _, v := range searchItems {
		if v != nil && v.UpdateBy != nil {
			operators = append(operators, v.UpdateBy)
		}
	}
	for _, v := range searchTextItems {
		if v != nil && v.UpdateBy != nil {
			operators = append(operators, v.UpdateBy)
		}
	}

	operatorsToShow, err := ailogic.GetAiDocOperatorsShowInfo(ctx, operators...)
	if err != nil {
		return err
	}
	var contributors []*aipb.Contributor
	for _, v := range searchItems {
		contributors = append(contributors, v.Contributor...)
	}
	for _, v := range searchTextItems {
		contributors = append(contributors, v.Contributor...)
	}
	_, err = ailogic.GeAiDoctContributorShowInfo(ctx, contributors...)
	if err != nil {
		return err
	}

	for _, v := range searchItems {
		rsp.Item = append(rsp.Item, &bffaipb.SearchCollectionItem{
			Text:        v.Text,
			Question:    v.Question,
			Score:       v.Score,
			FileName:    v.RefName,
			Url:         v.RefUrl,
			Contributor: v.Contributor,
			UpdateBy:    operatorsToShow[v.UpdateBy],
			Id:          v.Id,
			Type:        v.Type,
			IsRelated:   v.IsRelated,
			DocType:     v.DocType,
		})
	}
	for _, v := range searchTextItems {
		rsp.Item = append(rsp.Item, &bffaipb.SearchCollectionItem{
			Text:        v.Text,
			Question:    v.Question,
			Score:       v.Score,
			FileName:    v.RefName,
			Url:         v.RefUrl,
			Contributor: v.Contributor,
			UpdateBy:    operatorsToShow[v.UpdateBy],
			Id:          v.Id,
			Type:        v.Type,
			IsRelated:   v.IsRelated,
			DocType:     v.DocType,
		})
	}

	rsp.Start = startTime
	rsp.End = endTime
	rsp.TotalCount = totalCount
	rsp.MatchPattern = matchPattern

	return nil
}

// ValidateQAs 校验待创建的QA对
func (a *Ai) ValidateQAs(ctx context.Context, req *bffaipb.ReqValidateQAs, rsp *bffaipb.RspValidateQAs) error {
	aids, err := ailogic.GetManagedAssistants(ctx)
	if err != nil {
		return err
	}
	contributor, err := ailogic.GetUserAsContributor(ctx)
	if err != nil {
		return err
	}
	pbReq := &aipb.ReqValidateQAInBulk{
		ScopedAssistant: aids,
		Contributor:     contributor,
	}
	for _, v := range req.Items {
		pbReq.Items = append(pbReq.Items, &aipb.QA{
			Question:    v.Question,
			Answer:      v.Answer,
			Reference:   v.Reference,
			State:       v.State,
			Contributor: v.Contributor,
			States: func() []*aipb.DocAssistantState {
				r := make([]*aipb.DocAssistantState, 0, len(v.AssistantId))
				for _, vv := range v.AssistantId {
					r = append(r, &aipb.DocAssistantState{State: v.State, AssistantId: vv})
				}
				return r
			}(),
		})
	}
	pbRsp, err := client.AiNational.ValidateQAInBulk(ctx, pbReq, mclient.WithRequestTimeout(15*time.Second))
	if err != nil {
		log.WithContext(ctx).Errorf("validate ai collection qa failed: %v", err)
		return err
	}
	for _, v := range pbRsp.Errors {
		msg := ""
		if v.Error == errorspb.AiError_AiCollectionQuestionExisted {
			msg = "问题已经存在"
		}
		rsp.Errors = append(rsp.Errors, &bffaipb.RspValidateQAs_Err{
			Code:    v.Error,
			Message: msg,
			Id:      v.Id,
		})
	}
	return nil
}

// ValidateTextFiles 校验待创建的文本文件
func (a *Ai) ValidateTextFiles(ctx context.Context, req *bffaipb.ReqValidateTextFiles, rsp *bffaipb.RspValidateTextFiles) error {
	ailogic.SetDefaultDataSource(req)
	aids, err := ailogic.GetManagedAssistants(ctx)
	if err != nil {
		return err
	}
	contributor, err := ailogic.GetUserAsContributor(ctx)
	if err != nil {
		return err
	}
	pbReq := &aipb.ReqValidateTextFileInBulk{
		ScopedAssistant: aids,
		Contributor:     contributor,
		DataSource:      req.DataSource,
	}
	for _, v := range req.Items {
		pbReq.Items = append(pbReq.Items, &aipb.TextFile{
			Name: v.FileName,
			Text: v.Text,
		})
	}
	pbRsp, err := client.AiNational.ValidateTextFileInBulk(ctx, pbReq, mclient.WithRequestTimeout(15*time.Second))
	if err != nil {
		log.WithContext(ctx).Errorf("validate ai collection textfile failed: %v", err)
		return err
	}
	for _, v := range pbRsp.Errors {
		msg := ""
		if v.Error == errorspb.AiError_AiCollectionTextFileExisted {
			msg = "问题已经存在"
		}
		rsp.Errors = append(rsp.Errors, &bffaipb.RspValidateTextFiles_Err{
			Code:    v.Error,
			Message: msg,
			Id:      v.Id,
		})
	}
	return nil
}

// ListContributor 获取贡献者列表
func (a *Ai) ListContributor(ctx context.Context, req *bffaipb.ReqListContributor, rsp *bffaipb.RspListContributor) error {
	ailogic.SetDefaultDataSource(req)
	aids, err := ailogic.RewriteAssistantIds(ctx)
	if err != nil {
		return err
	}
	contributor, err := ailogic.GetUserAsContributor(ctx)
	if err != nil {
		return err
	}
	pbRsp, err := client.AiNational.ListContributor(ctx, &aipb.ReqListContributor{
		Or: &aipb.ReqListContributor_OrGroup{
			ScopedAssistantId: aids,
			Contributor:       contributor,
		},
		Type:       req.Type,
		DataSource: req.DataSource,
	})
	if err != nil {
		return err
	}
	info, err := ailogic.GeAiDoctContributorShowInfo(ctx, pbRsp.Contributors...)
	if err != nil {
		return err
	}
	// 运营端贡献者合并为一条
	hasMgmt := false
	for _, v := range info {
		if v.Type == base.IdentityType_IDENTITY_TYPE_MGMT {
			if hasMgmt {
				continue
			}
			hasMgmt = true
		}
		if len(req.Search) != 0 && !strings.Contains(v.Text, req.Search) {
			continue
		}
		rsp.Contributors = append(rsp.Contributors, v)
	}
	return nil
}

// ListOperator 获取更新人列表
func (a *Ai) ListOperator(ctx context.Context, req *bffaipb.ReqListOperator, rsp *bffaipb.RspListOperator) error {
	ailogic.SetDefaultDataSource(req)
	aids, err := ailogic.RewriteAssistantIds(ctx)
	if err != nil {
		return err
	}
	contributor, err := ailogic.GetUserAsContributor(ctx)
	if err != nil {
		return err
	}
	pbReq := &aipb.ReqListUpdateBy{
		DataSource: req.DataSource,
		Type:       req.Type,
		Or: &aipb.ReqListUpdateBy_OrGroup{
			ScopedAssistantId: aids,
			Contributor:       contributor,
		},
	}
	rpcF := client.AiNational.ListUpdateBy
	if req.Creator {
		rpcF = client.AiNational.ListCreateBy
	}
	pbRsp, err := rpcF(ctx, pbReq)
	if err != nil {
		return err
	}
	info, err := ailogic.GetAiDocOperatorsShowInfo(ctx, pbRsp.Operators...)
	if err != nil {
		return err
	}
	for _, v := range info {
		if len(req.Search) != 0 && !strings.Contains(v.Username, req.Search) && !strings.Contains(v.TeamName, req.Search) {
			continue
		}
		rsp.Operators = append(rsp.Operators, v)
	}
	rsp.Operators, err = ailogic.UniqueListDocOperator(ctx, rsp.Operators)
	if err != nil {
		return err
	}
	return nil
}

// ListSharedAssistant 查询已分享的助手列表，用于表头筛选
func (a *Ai) ListSharedAssistant(ctx context.Context, req *bffaipb.ReqListSharedAssistant, rsp *bffaipb.RspListSharedAssistant) error {
	contributor, err := ailogic.GetUserAsContributor(ctx)
	if err != nil {
		return err
	}
	pbRsp, err := client.AiNational.ListSharedAssistant(ctx, &aipb.ReqListSharedAssistant{
		Contributor: contributor,
		Type:        req.Type,
	})
	if err != nil {
		return err
	}
	for _, v := range pbRsp.Assistants {
		rsp.Assistants = append(rsp.Assistants, &bffaipb.Assistant{
			Id:     v.Id,
			Name:   v.Name,
			NameEn: v.NameEn,
		})
	}
	return nil
}

// ListSharedTeam 查询已分享的团队列表，用于表头筛选
func (a *Ai) ListSharedTeam(ctx context.Context, req *bffaipb.ReqListSharedTeam, rsp *bffaipb.RspListSharedTeam) error {
	contributor, err := ailogic.GetUserAsContributor(ctx)
	if err != nil {
		return err
	}
	pbRsp, err := client.AiNational.ListSharedTeam(ctx, &aipb.ReqListSharedTeam{
		Contributor: contributor,
		Type:        req.Type,
		DataSource:  req.DataSource,
	})
	if err != nil {
		return err
	}

	// AI服务只返回团队ID，需要获取团队详细信息
	if len(pbRsp.Ids) > 0 {
		teamsInfo, err := iamlogic.FetchTeamInfo(ctx, pbRsp.Ids)
		if err != nil {
			return err
		}

		for id, team := range teamsInfo {
			rsp.Teams = append(rsp.Teams, &bffaipb.RspListSharedTeam_SharedTeam{
				Id:   id,
				Name: team.ShortName,
			})
		}
	}

	return nil
}

// ListSharedUser 查询已分享的用户列表，用于表头筛选
func (a *Ai) ListSharedUser(ctx context.Context, req *bffaipb.ReqListSharedUser, rsp *bffaipb.RspListSharedUser) error {
	contributor, err := ailogic.GetUserAsContributor(ctx)
	if err != nil {
		return err
	}
	pbRsp, err := client.AiNational.ListSharedUser(ctx, &aipb.ReqListSharedUser{
		Contributor: contributor,
		Type:        req.Type,
		DataSource:  req.DataSource,
	})
	if err != nil {
		return err
	}

	// AI服务只返回用户ID，需要获取用户详细信息
	if len(pbRsp.Ids) > 0 {
		usersInfo, err := iamlogic.FetchUserName(ctx, pbRsp.Ids)
		if err != nil {
			return err
		}

		for id, user := range usersInfo {
			rsp.Users = append(rsp.Users, &bffaipb.RspListSharedUser_SharedUser{
				Id:   id,
				Name: user,
			})
		}
	}

	return nil
}

// ListCollectionFileName 获取文件名称列表
func (a *Ai) ListCollectionFileName(ctx context.Context, req *bffaipb.ReqListCollectionFileName, rsp *bffaipb.RspListCollectionFileName) error {
	ailogic.SetDefaultDataSource(req)
	aids, err := ailogic.RewriteAssistantIds(ctx)
	if err != nil {
		return err
	}
	contributor, err := ailogic.GetUserAsContributor(ctx)
	if err != nil {
		return err
	}
	pbReq := &aipb.ReqListTextFile{
		Page: &base.Paginator{
			Offset: req.Offset,
			Limit:  req.Limit,
		},
		Search: &aipb.ReqListTextFile_Search{
			FileName:     req.Search,
			FullFileName: req.FullSearch,
		},
		Mask:    &fieldmaskpb.FieldMask{Paths: []string{"id", "file_name", "ref"}},
		DocType: []aipb.DocType{aipb.DocType_DOCTYPE_FILE},
		TenantCond: &aipb.ReqListTextFile_TenantCond{
			AssistantId: aids,
			Contributor: []*aipb.ContributorFilter{{Contributor: contributor}},
		},
		DataSource: req.DataSource,
	}
	pbRsp, err := client.AiNational.ListTextFile(ctx, pbReq)
	if err != nil {
		return err
	}
	for _, v := range pbRsp.Items {
		rsp.Items = append(rsp.Items, &bffaipb.RspListCollectionFileName_Item{
			Url:  v.Doc.Url,
			Name: v.Doc.Name,
			Id:   v.Doc.Id,
		})
	}
	rsp.TotalCount = pbRsp.Total
	return nil
}

// CloneDoc 复制doc
func (a *Ai) CloneDoc(ctx context.Context, req *bffaipb.ReqCloneDoc, rsp *bffaipb.RspCloneDoc) error {
	aids, err := ailogic.GetManagedAssistants(ctx)
	if err != nil {
		return err
	}
	// if len(aids) == 0 {
	// 	// 用于名下无管理助手时，不克隆 doc 与助手的关联关系
	// 	aids = []uint64{0}
	// }
	operator, err := ailogic.GetUserAsOperator(ctx, true)
	if err != nil {
		return err
	}
	pbReq := &aipb.ReqCloneDocInBulk{
		Operator:          operator,
		ScopedAssistantId: aids,
		Id:                req.Id,
		// QueryId:           req.QueryId, // 2.11 克隆暂时不支持全选
	}
	pbRsp, err := client.AiNational.CloneDocInBulk(ctx, pbReq)
	if err != nil {
		return err
	}
	for _, v := range pbRsp.Id {
		rsp.Id = append(rsp.Id, v)
	}

	return nil
}

// OnOffDocs 启用/禁用
func (a *Ai) OnOffDocs(ctx context.Context, req *bffaipb.ReqOnOffDocs, rsp *bffaipb.RspOnOffDocs) error {
	if req.QueryId == 0 && len(req.Id) == 0 {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}
	aids, err := ailogic.GetManagedAssistants(ctx)
	if err != nil {
		return err
	}
	// 没有助手无法启用
	if len(aids) == 0 {
		return xerrors.ForbiddenError("")
	}
	operator, err := ailogic.GetUserAsOperator(ctx, true)
	if err != nil {
		return err
	}
	pbReq := &aipb.ReqOnOffDocInBulk{
		Operator:          operator,
		State:             req.State,
		ScopedAssistantId: aids,
		QueryId:           req.QueryId,
	}
	for _, v := range req.Id {
		pbReq.Id = append(pbReq.Id, v)
	}
	data, err := client.AiNational.OnOffDocInBulk(ctx, pbReq)
	if err != nil {
		return err
	}

	for _, v := range data.RepeatCollections {
		rsp.RepeatCollections = append(rsp.RepeatCollections, &bffaipb.RspOnOffDocs_RepeatCollection{Id: v.Id, FileName: v.FileName})
	}

	for _, v := range data.PreRepeatCollections {
		rsp.PreRepeatCollections = append(rsp.PreRepeatCollections, &bffaipb.RspOnOffDocs_RepeatCollection{Id: v.Id, FileName: v.FileName})
	}

	for _, v := range data.ExceedQaContainsMatchLimit {
		rsp.QaNumExceed = append(rsp.QaNumExceed, &bffaipb.RspOnOffDocs_QaContainsMatchCount{
			AssistantId: v.AssistantId,
			Cnt:         v.QaCnt,
		})
	}
	rsp.Async = data.Async
	return nil
}

// ImportTextFiles 导入文本/文件
// 如果遇到重复的文本/文件，直接内容覆盖
func (a *Ai) ImportTextFiles(ctx context.Context, req *bffaipb.ReqImportTextFiles, rsp *bffaipb.RspImportTextFiles) error {
	contributor, err := ailogic.GetUserAsContributor(ctx)
	if err != nil {
		return err
	}
	operator, err := ailogic.GetUserAsOperator(ctx, true)
	if err != nil {
		return err
	}

	scopedAssistant, err := ailogic.GetManagedAssistants(ctx)
	if err != nil {
		return err
	}

	for _, v := range req.Items {
		_, err := ailogic.RewriteAssistantIds(ctx, v.AssistantId...)
		if err != nil {
			return err
		}
	}

	importLogic := ailogic.NewTextFileImportLogic(context.WithoutCancel(ctx), contributor, operator, scopedAssistant, req)
	err = importLogic.Do()
	if err != nil {
		return err
	}

	return nil
}

// ImportQAs 导入qa
func (a *Ai) ImportQAs(ctx context.Context, req *bffaipb.ReqImportQAs, rsp *bffaipb.RspImportQAs) error {
	contributor, err := ailogic.GetUserAsContributor(ctx)
	if err != nil {
		return err
	}
	operator, err := ailogic.GetUserAsOperator(ctx, true)
	if err != nil {
		return err
	}

	scopedAssistant, err := ailogic.GetManagedAssistants(ctx)
	if err != nil {
		return err
	}

	for _, v := range req.Items {
		_, err := ailogic.RewriteAssistantIds(ctx, v.AssistantId...)
		if err != nil {
			return err
		}
	}

	importLogic := ailogic.NewQAImportLogic(context.WithoutCancel(ctx), contributor, operator, scopedAssistant, req)
	err = importLogic.Do()
	if err != nil {
		return err
	}

	return nil
}

// BatchUpdateDocAttr 批量更新doc
func (a *Ai) BatchUpdateDocAttr(ctx context.Context, req *bffaipb.ReqBatchUpdateDocAttr,
	rsp *bffaipb.RspBatchUpdateDocAttr,
) error {
	if req.QueryId == 0 && len(req.Id) == 0 {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}
	managedAssistants, err := ailogic.GetManagedAssistants(ctx)
	if err != nil {
		return err
	}
	// 如果没有管理的助手，不能修改绑定的助手
	if len(managedAssistants) == 0 && req.GetMask().GetPaths() != nil {
		req.Mask.Paths = slices.DeleteFunc(req.Mask.GetPaths(), func(e string) bool {
			return e == "assistant_id"
		})
	}
	if len(req.Mask.Paths) == 0 {
		return nil
	}
	// 只修改助手绑定，需要检查助手权限
	if len(req.GetMask().GetPaths()) == 1 && req.GetMask().Paths[0] == "assistant_id" {
		for _, v := range req.AssistantId {
			if !slices.Contains(managedAssistants, v) {
				return xerrors.ForbiddenError("")
			}
		}
	} else if len(req.Id) > 0 {
		var docNeedCheckEdit []uint64
		for _, v := range req.Id {
			docNeedCheckEdit = append(docNeedCheckEdit, v)
		}
		err = ailogic.CheckDocEditPermission(ctx, docNeedCheckEdit...)
		if err != nil {
			return err
		}
	}

	operator, err := ailogic.GetUserAsOperator(ctx, true)
	if err != nil {
		return err
	}
	pbReq := &aipb.ReqUpdateDocAttrInBulk{
		Id:                req.Id,
		Mask:              req.Mask,
		Contributor:       req.Contributor,
		ShowContributor:   req.ShowContributor,
		DownloadAsRef:     req.DownloadAsRef,
		ScopedAssistantId: managedAssistants,
		AssistantId:       req.AssistantId,
		UpdateBy:          operator,
		MatchPatterns:     req.MatchPatterns,
		Reference:         req.Reference,
		QueryId:           req.QueryId,
	}
	// 加大超时时间
	rpcTimeOut := time.Duration(10) * time.Second
	rpcRsp, err := client.AiNational.UpdateDocAttrInBulk(ctx, pbReq, mclient.WithRequestTimeout(rpcTimeOut))
	if err != nil {
		return err
	}
	if rpcRsp != nil {
		rsp.Async = rpcRsp.Async
	}
	return nil
}

// ReparseTextFiles 重新解析doc
func (a Ai) ReparseTextFiles(ctx context.Context, req *bffaipb.ReqReparseTextFiles,
	rsp *bffaipb.RspReparseTextFiles,
) error {
	if req.QueryId == 0 && len(req.Ids) == 0 {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}
	err := ailogic.CheckDocEditPermission(ctx, req.Ids...)
	if err != nil {
		return err
	}
	operator, err := ailogic.GetUserAsOperator(ctx, true)
	if err != nil {
		return err
	}
	rpcRsp, err := client.AiNational.ReparseTextFiles(ctx, &aipb.ReqReparseTextFiles{
		Ids:       req.Ids,
		Operator:  operator,
		ParseMode: req.ParseMode,
		QueryId:   req.QueryId,
	})
	if rpcRsp != nil {
		rsp.Async = rpcRsp.Async
	}
	return err
}

// GetTextFileTip 获取文本文件的知识提示（解析失败，文件名重复，表头过长）等信息
func (a *Ai) GetTextFileTip(ctx context.Context, req *bffaipb.ReqGetTextFileTip, rsp *bffaipb.RspGetTextFileTip) error {
	contributor, err := ailogic.GetUserAsContributor(ctx)
	if err != nil {
		return err
	}
	aids, err := ailogic.GetManagedAssistants(ctx)
	if err != nil {
		return err
	}

	// 构建调用微服务的请求参数
	pbReq := &aipb.ReqGetTextFileTip{
		Id:          req.Id,
		AssistantId: aids,
		Contributor: contributor,
	}

	// 调用AI服务(文档API都是使用国内服务，与区域无关)
	pbRsp, err := client.AiNational.GetTextFileTip(ctx, pbReq)
	if err != nil {
		log.WithContext(ctx).Errorf("get text file tip failed: %v", err)
		return err
	}

	// 将微服务返回的结果转换为BFF层的响应格式
	rsp.TableOverSize = pbRsp.TableOverSize
	rsp.State = pbRsp.State
	rsp.Repeated = pbRsp.Repeated

	return nil
}

// GetQaTip 查询QA的知识提示（问题超长，内容重复）等信息
func (a *Ai) GetQaTip(ctx context.Context, req *bffaipb.ReqGetQaTip, rsp *bffaipb.RspGetQaTip) error {
	contributor, err := ailogic.GetUserAsContributor(ctx)
	if err != nil {
		return err
	}
	aids, err := ailogic.GetManagedAssistants(ctx)
	if err != nil {
		return err
	}

	// 构建调用微服务的请求参数
	pbReq := &aipb.ReqGetQaTip{
		Id:          req.Id,
		AssistantId: aids,
		Contributor: contributor,
	}

	// 调用AI服务(文档API都是使用国内服务，与区域无关)
	pbRsp, err := client.AiNational.GetQaTip(ctx, pbReq)
	if err != nil {
		log.WithContext(ctx).Errorf("get qa tip failed: %v", err)
		return err
	}

	// 将微服务返回的结果转换为BFF层的响应格式
	rsp.QuestionOverSize = pbRsp.QuestionOverSize
	rsp.Repeated = pbRsp.Repeated

	return nil
}

// CreateDocQuery ...
func (a Ai) CreateDocQuery(ctx context.Context, req *bffaipb.ReqCreateDocQuery,
	rsp *bffaipb.RspCreateDocQuery,
) error {
	var err error

	operator, err := ailogic.GetUserAsOperator(ctx, true)
	if err != nil {
		return err
	}
	rpcReq := &aipb.ReqCreateDocQuery{
		CreateBy: operator,
	}

	if req.Doc != nil {
		rpcReq.Doc, _, _, err = ailogic.CreateAiListTextFile(ctx, req.Doc, true, &ailogic.ListTextFilesOption{
			WithTips: true,
		})
	} else if req.Qa != nil {
		rpcReq.Qa, _, _, err = ailogic.CreateAiListQA(ctx, req.Qa)
	} else {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}
	if err != nil {
		return err
	}

	rcpRsp, err := client.AiNational.CreateDocQuery(ctx, rpcReq)
	if err != nil {
		return err
	}
	rsp.QueryId = rcpRsp.QueryId
	rsp.TotalCount = rcpRsp.TotalCount
	rsp.IsEmpty = rcpRsp.IsEmpty
	return nil
}
