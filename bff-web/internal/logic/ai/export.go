package ai

import (
	"archive/zip"
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"reflect"
	"strings"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/tcloud/xcos"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/ai"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	reviewpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/review"
	"github.com/tencentyun/cos-go-sdk-v5"
	"github.com/xuri/excelize/v2"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type ExportTarget struct {
	Headers [][]string
	Values  [][]string
	Type    ai.ExportTaskType
}

type columnHandler struct {
	field   string
	handler columnHandleFunc
}

type handleList[T any] func(ctx context.Context, items []T, fields []*bffaipb.ExportField, lang string) (*ExportTarget, error)
type fetchItemsFunc[T any] func(ctx context.Context, limit uint32, offset uint32) (totalCount uint32, items []T, err error)
type columnHandleFunc func(value interface{}, item interface{}, lang string) string

type ListHandler[Req, Rsp any] func(ctx context.Context, req *Req, rsp *Rsp) error

func ExportTaskFactory[Req, Rsp any](ctx context.Context, req *Req, listFn ListHandler[Req, Rsp], task *ai.ExportTask) {
	newCtx, cancel := context.WithTimeout(context.WithoutCancel(ctx), time.Duration(config.GetIntOr("export.task.timeout", 30000))*time.Millisecond)
	xsync.SafeGo(newCtx, func(ctx context.Context) error {
		defer cancel()
		switch f := any(req).(type) {
		case *bffaipb.ReqCreateChatMessageExportTask:
			fetchFunc := func(ctx context.Context, limit uint32, offset uint32) (totalCount uint32, items []*bffaipb.ChatInfo, err error) {
				rsp := &bffaipb.RspListChat{}
				var genericRsp = any(rsp).(*Rsp)
				f.Filter.Limit = limit
				f.Filter.Offset = offset
				err = listFn(ctx, req, genericRsp)
				if err != nil {
					return 0, nil, err
				}
				return rsp.TotalCount, rsp.Chats, nil
			}
			task.MaxResponseThreshold = 10000000
			StartExport(ctx, task, fetchFunc, HandleAIChatMessage, f.Fields)
		case *bffaipb.ReqCreateChatExportTask:
			fetchFunc := func(ctx context.Context, limit uint32, offset uint32) (totalCount uint32, items []*bffaipb.ChatInfo, err error) {
				rsp := &bffaipb.RspListChat{}
				var genericRsp = any(rsp).(*Rsp)
				f.Filter.Limit = limit
				f.Filter.Offset = offset
				err = listFn(ctx, req, genericRsp)
				if err != nil {
					return 0, nil, err
				}
				return rsp.TotalCount, rsp.Chats, nil
			}
			task.MaxResponseThreshold = 10000000
			StartExport(ctx, task, fetchFunc, HandleAIChat, f.Fields)
		case *bffaipb.ReqCreateFileExportTask:
			fetchFunc := func(ctx context.Context, limit uint32, offset uint32) (totalCount uint32, items []*bffaipb.CollectionTextFile, err error) {
				rsp := &bffaipb.RspListTextFiles{}
				var genericRsp = any(rsp).(*Rsp)
				f.Filter.Limit = limit
				f.Filter.Offset = offset
				err = listFn(ctx, req, genericRsp)
				if err != nil {
					return 0, nil, err
				}
				return rsp.TotalCount, rsp.Items, nil
			}
			StartExport(ctx, task, fetchFunc, HandleCollectionTextFile, f.Fields)

		case *bffaipb.ReqCreateQaExportTask:
			fetchFunc := func(ctx context.Context, limit uint32, offset uint32) (totalCount uint32, items []*bffaipb.CollectionQA, err error) {
				rsp := &bffaipb.RspListQA{}
				var genericRsp = any(rsp).(*Rsp)
				f.Filter.Limit = limit
				f.Filter.Offset = offset
				err = listFn(ctx, req, genericRsp)
				if err != nil {
					return 0, nil, err
				}
				return rsp.TotalCount, rsp.Items, nil
			}
			StartExport(ctx, task, fetchFunc, HandleCollectionQA, f.Fields)
		}
		return nil
	}, boot.TraceGo(ctx))
}

var formatter = &Formatter{}
var TextFileExportColumnMapping = map[string]columnHandler{
	"reference": {
		handler: formatter.formatReference,
	},
	"contributor": {
		handler: formatter.formatContributors,
	},
	"show_contributor": {
		handler: formatter.formatUintBoolString,
	},
	"assistants": {
		handler: formatter.formatStateAssistants,
	},
	"share_to": {
		field:   "shared_states",
		handler: formatter.formatShareAssistants,
	},
	"create_date": {
		handler: formatter.formatTime,
	},
	"update_date": {
		handler: formatter.formatTime,
	},
	"update_by": {
		handler: formatter.formatUpdateBy,
	},
	"create_by": {
		handler: formatter.formatUpdateBy,
	},
	"state": {
		field:   "states",
		handler: formatter.formatAssistantSyncStates,
	},
	"vector_state": {
		field:   "states",
		handler: formatter.formatAssistantTransState,
	},
	"share_state": {
		field:   "states",
		handler: formatter.formatCollectionShareState,
	},
	"download_as_ref": {
		field:   "download_as_ref",
		handler: formatter.formatDlAsRef,
	},
	"parse_mode": {
		field:   "parse_mode",
		handler: formatter.formatParseMode,
	},
}

var QAExportColumnMapping = map[string]columnHandler{
	"reference": {
		handler: formatter.formatReference,
	},
	"contributor": {
		handler: formatter.formatContributors,
	},
	"show_contributor": {
		handler: formatter.formatUintBoolString,
	},
	"assistants": {
		handler: formatter.formatStateAssistants,
	},
	"share_to": {
		field:   "shared_states",
		handler: formatter.formatShareAssistants,
	},
	"create_date": {
		handler: formatter.formatTime,
	},
	"update_date": {
		handler: formatter.formatTime,
	},
	"update_by": {
		handler: formatter.formatUpdateBy,
	},
	"create_by": {
		handler: formatter.formatUpdateBy,
	},
	"state": {
		field:   "states",
		handler: formatter.formatAssistantSyncStates,
	},
	"vector_state": {
		field:   "states",
		handler: formatter.formatAssistantTransState,
	},
	"share_state": {
		field:   "states",
		handler: formatter.formatCollectionShareState,
	},
	"match_patterns": {
		field:   "match_patterns",
		handler: formatter.formatQaMatchPattern,
	},
}

var ChatExportColumnMapping = map[string]columnHandler{
	"chat_state": {
		handler: formatter.formatChatState,
	},
	"chat_type": {
		handler: formatter.formatChatType,
	},
	"create_date": {
		handler: formatter.formatTime,
	},
	"update_date": {
		handler: formatter.formatTime,
	},
	"region": {
		handler: formatter.formatRegion,
	},
	"username": {
		field:   "create_by",
		handler: formatter.formatCreateBy,
	},
	"rating_scale": {
		handler: formatter.formatRatingScale,
	},
	"assistant": {
		field: "assistant_name",
	},
	"avg_duration": {
		handler: formatter.formatAvgDuration,
	},
	"doc_hits": {
		handler: formatter.formatDocHits,
	},
}

func StartExport[T any](ctx context.Context, task *ai.ExportTask, fetchFunc fetchItemsFunc[T], handleFunc handleList[T], fields []*bffaipb.ExportField) {
	user := xsession.UserFromContext[iampb.UserInfo](ctx)
	runExportTask(ctx, task, user.Id, fetchFunc, handleFunc, fields)
}

// fetchWithRetry 增加重试
func fetchWithRetry[T any](ctx context.Context, fetchFunc fetchItemsFunc[T], batchSize, offset uint32, count *uint32) ([]T, error) {
	const maxRetry = 3
	var items []T
	var err error
	for i := 0; i < maxRetry; i++ {
		*count, items, err = fetchFunc(ctx, batchSize, offset)
		if err == nil {
			return items, nil
		}
	}
	return nil, fmt.Errorf("fetchFunc failed after %d retries: %w", maxRetry, err)
}

// fetchHandle 获取列表
func fetchHandle[T any](ctx context.Context, task *ai.ExportTask, fetchFunc fetchItemsFunc[T]) (<-chan []T, <-chan error) {
	var maxBatchSize = config.GetIntOr("export.task.max_batch_size", 50)                  // 每次请求的数量
	var maxResponseThreshold = config.GetIntOr("export.task.max_response_threshold", 500) // 每个excel文件最大写入数据条数
	if task.MaxBatchSize > 0 {
		maxBatchSize = int(task.MaxBatchSize)
	}
	if task.MaxResponseThreshold > 0 {
		maxResponseThreshold = int(task.MaxResponseThreshold)
	}

	ch := make(chan []T, 1)
	errCh := make(chan error, 1)
	var count uint32

	go func() {
		defer close(ch)
		defer close(errCh)

		var offset int
		var buffer []T

		for {
			// 每次获取 maxBatchSize 条数据
			items, err := fetchWithRetry(ctx, fetchFunc, uint32(maxBatchSize), uint32(offset), &count)
			if err != nil {
				errCh <- err
				return
			}

			// 如果没有更多数据了，退出循环
			if len(items) == 0 {
				if len(buffer) > 0 {
					ch <- buffer
				}
				return
			}

			buffer = append(buffer, items...)
			if len(buffer) >= maxResponseThreshold {
				ch <- buffer
				buffer = nil
			}

			// 更新偏移量
			offset += maxBatchSize
			if offset >= int(count) {
				if len(buffer) > 0 {
					ch <- buffer
				}
				return
			}
		}
	}()

	return ch, errCh
}

func runExportTask[T any](ctx context.Context, task *ai.ExportTask, userID uint64, fetchFunc fetchItemsFunc[T], handleFunc handleList[T], fields []*bffaipb.ExportField) {

	var (
		err       error
		target    *ExportTarget
		allBuffer [][]byte
		path      string
		count     int32
	)
	lang := bff.RequestFromContext(ctx).Header.Get("Lang")
	if err = UpdateTaskState(ctx, task.Id, ai.ExportTaskState_EXPORT_TASK_STATE_RUNNING); err != nil {
		return
	}

	ctx2 := context.WithoutCancel(ctx)
	defer func() {
		taskState := ai.ExportTaskState_EXPORT_TASK_STATE_COMPLETED
		path, err = compressFile(ctx2, task, userID, allBuffer)
		if err != nil || path == "" {
			log.WithContext(ctx).Errorw("runExportTask err", "err", err)
			taskState = ai.ExportTaskState_EXPORT_TASK_STATE_FAILED
		}
		var taskInfo ImportDocTaskInfo
		taskInfo.SucceedCount = uint64(count)
		tInfo, _ := json.Marshal(taskInfo)

		if _, updateErr := client.AiNational.UpdateExportTask(ctx2, &ai.ReqUpdateExportTask{
			Id:        task.Id,
			State:     taskState,
			Url:       path,
			ExtraInfo: string(tInfo),
		}); updateErr != nil {
			log.Errorw("runExportTask UpdateTaskState failed", "taskId", task.Id, "error", updateErr)
		}
	}()

	resultChan, fetchErrChan := fetchHandle(ctx, task, fetchFunc)

	for {
		select {
		case <-ctx.Done():
			err = ctx.Err()
			return
		case list, ok := <-resultChan:
			if !ok || list == nil {
				return
			}
			time.Sleep(50 * time.Millisecond)
			count += int32(len(list))
			if target, err = handleFunc(ctx, list, fields, lang); err != nil {
				return
			}
			var buffer []byte
			if buffer, err = buildTaskXlsx(target); err != nil {
				return
			}
			allBuffer = append(allBuffer, buffer)
		case err = <-fetchErrChan:
			return
		}
	}

}

func UpdateTaskState(ctx context.Context, taskID uint64, status ai.ExportTaskState, extraInfo ...string) error {
	_, err := client.AiNational.UpdateExportTask(ctx, &ai.ReqUpdateExportTask{
		Id:    taskID,
		State: status,
		ExtraInfo: func() string {
			if len(extraInfo) > 0 {
				return extraInfo[0]
			}
			return ""
		}(),
	})
	if err != nil {
		return err
	}
	return nil
}

func cosClient(bucketType reviewpb.CosBucketType) *cos.Client {
	switch bucketType {
	case reviewpb.CosBucketType_COS_BUCKET_TYPE_PUBLIC:
		return xcos.Client("public")
	case reviewpb.CosBucketType_COS_BUCKET_TYPE_PRIVATE:
		return xcos.Client("private")
	default:
		return xcos.Client()
	}
}

func addFileToZip(zipWriter *zip.Writer, fileName string, fileData []byte) error {
	w, err := zipWriter.Create(fileName)
	if err != nil {
		return err
	}
	_, err = w.Write(fileData)
	return err
}

func compressFile(ctx context.Context, task *ai.ExportTask, userID uint64, buffers [][]byte) (string, error) {
	if len(buffers) == 0 {
		return "", errors.New("buffer is empty")
	}
	var filePrefix string
	var configKey = "export.prefix"
	if bff.RequestFromContext(ctx).Header.Get("Lang") == "en" {
		configKey = "export.prefix.en"
	}
	switch task.Type {
	case ai.ExportTaskType_EXPORT_TASK_TYPE_FILE:
		filePrefix = config.GetString(fmt.Sprintf("%s.text_file_prefix", configKey))
	case ai.ExportTaskType_EXPORT_TASK_TYPE_QA:
		filePrefix = config.GetString(fmt.Sprintf("%s.qa_prefix", configKey))
	case ai.ExportTaskType_EXPORT_TASK_TYPE_MESSAGE:
		filePrefix = config.GetString(fmt.Sprintf("%s.message_prefix", configKey))
	case ai.ExportTaskType_EXPORT_TASK_TYPE_CHAT:
		filePrefix = config.GetString(fmt.Sprintf("%s.chat_prefix", configKey))
	}
	var fileName string
	var buffer bytes.Buffer
	date := time.Now().Format("2006_01_02")
	if len(buffers) > 1 {
		zipWriter := zip.NewWriter(&buffer)
		for index, buf := range buffers {
			name := fmt.Sprintf("%s_%s(%d).xlsx", filePrefix, date, index+1)
			if err := addFileToZip(zipWriter, name, buf); err != nil {
				_ = zipWriter.Close() // Close to avoid partial writes
				return "", err
			}
		}
		if err := zipWriter.Close(); err != nil { // Ensure all writes are complete
			return "", err
		}
		fileName = fmt.Sprintf("%s_%s.zip", filePrefix, date)
	} else if len(buffers) == 1 {
		if _, err := buffer.Write(buffers[0]); err != nil {
			return "", err
		}
		fileName = fmt.Sprintf("%s_%s.xlsx", filePrefix, date)
	}
	path := config.GetStringOr("export.prefix.prefix_path", "ai/export-task/") + handleHashId(userID) + "/" + fmt.Sprintf("%d_", time.Now().Unix()) + fileName
	encodedName := url.QueryEscape(fileName)

	_, err := cosClient(reviewpb.CosBucketType_COS_BUCKET_TYPE_PUBLIC).Object.Put(ctx, path, &buffer, &cos.ObjectPutOptions{
		ObjectPutHeaderOptions: &cos.ObjectPutHeaderOptions{
			ContentDisposition: fmt.Sprintf("attachment;filename=%s", encodedName),
		},
	})

	if err != nil {
		return "", err
	}
	log.WithContext(ctx).Infow("buildTaskXlsx successful", "path", path)
	return path, nil
}

func buildTaskXlsx(target *ExportTarget) ([]byte, error) {
	if len(target.Headers) == 0 {
		return nil, errors.New("buildTaskXlsx target header is empty")
	}

	var sheetName = config.GetStringOr("export.task.default_sheet", "Sheet1")

	f := excelize.NewFile()
	startColName, _ := excelize.ColumnNumberToName(1)
	endColName, _ := excelize.ColumnNumberToName(len(target.Headers[0]))
	f.SetColWidth(sheetName, startColName, endColName, config.GetFloat64Or("export.task.col_width", 32))

	for index, item := range target.Headers {
		for colIndex, field := range item {
			colName, _ := excelize.ColumnNumberToName(colIndex + 1)
			cell := fmt.Sprintf("%s%d", colName, index+1) // A1，B1
			f.SetCellStr(sheetName, cell, field)
		}
	}

	for rowIndex, row := range target.Values {
		for colIndex, value := range row {
			colName, _ := excelize.ColumnNumberToName(colIndex + 1)
			cell := fmt.Sprintf("%s%d", colName, rowIndex+len(target.Headers)+1)
			f.SetCellStr(sheetName, cell, value)
		}
	}

	var buffer bytes.Buffer
	if err := f.Write(&buffer); err != nil {
		log.Errorw("Failed to write excel file to buffer:", err)
		return nil, err
	}

	return buffer.Bytes(), nil
}

func HandleCollectionQA(_ context.Context, items []*bffaipb.CollectionQA, fields []*bffaipb.ExportField, lang string) (*ExportTarget, error) {
	var headers = make([][]string, 3)
	for _, column := range fields {
		headers[0] = append(headers[0], column.Label)
		headers[1] = append(headers[1], column.Rule)
		headers[2] = append(headers[2], column.Tips)
	}
	return handleTargetFields(items, fields, headers, QAExportColumnMapping, lang)
}

func HandleCollectionTextFile(_ context.Context, items []*bffaipb.CollectionTextFile, fields []*bffaipb.ExportField, lang string) (*ExportTarget, error) {
	var headers = make([][]string, 3)
	for _, column := range fields {
		headers[0] = append(headers[0], column.Label)
		headers[1] = append(headers[1], column.Rule)
		headers[2] = append(headers[2], column.Tips)
	}
	return handleTargetFields(items, fields, headers, TextFileExportColumnMapping, lang)
}

func HandleAIChat(_ context.Context, items []*bffaipb.ChatInfo, fields []*bffaipb.ExportField, lang string) (*ExportTarget, error) {
	var headers = make([][]string, 1)
	for _, column := range fields {
		headers[0] = append(headers[0], column.Label)
	}
	return handleTargetFields(items, fields, headers, ChatExportColumnMapping, lang)
}

func HandleAIChatMessage(ctx context.Context, items []*bffaipb.ChatInfo, fields []*bffaipb.ExportField, lang string) (*ExportTarget, error) {
	var headers = make([][]string, 1)
	for _, column := range fields {
		headers[0] = append(headers[0], column.Label)
	}
	var chats []*ai.ReqDescribeExportChatMessages_ExportChat

	for _, chat := range items {
		exportChat := &ai.ReqDescribeExportChatMessages_ExportChat{
			Id:            chat.Id,
			AssistantName: chat.AssistantName,
		}
		if chat.CreateBy != nil {
			if IsWechatUnionID(chat.CreateBy.Username) { // 如果是小程序的临时账号，则要导出nickname
				exportChat.UserName = chat.CreateBy.NickName
			} else {
				exportChat.UserName = chat.CreateBy.Username
			}
			exportChat.RegionCode = chat.CreateBy.RegionCode
		}

		chats = append(chats, exportChat)
	}

	if len(chats) == 0 {
		return nil, fmt.Errorf("no export chat found")
	}

	rsp, err := client.AiByID(chats[0].Id).DescribeExportChatMessages(ctx, &ai.ReqDescribeExportChatMessages{ExportChats: chats})
	if err != nil {
		return nil, err
	}
	docNamesSet := make(map[string]struct{})
	docNames := make([]string, 0, len(docNamesSet))

	for _, message := range rsp.ExportMessages {
		if message.Type == ai.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION {
			names := message.DocNames
			for _, name := range names {
				if name != "" {
					docNamesSet[name] = struct{}{}
				}
			}
		}
	}

	if len(docNamesSet) > 0 {
		for name := range docNamesSet {
			docNames = append(docNames, name)
		}
		docRes, err := client.AiNational.DescribeMessageDocs(ctx, &ai.ReqDescribeMessageDocs{
			DocNames: strings.Join(docNames, ","),
			HitCount: false,
		})
		if err != nil {
			return nil, err
		}
		var docMap = make(map[string]*ai.ChatMessageDoc)
		for _, doc := range docRes.Docs {
			docMap[doc.RagFilename] = doc
		}
		for _, message := range rsp.ExportMessages {
			if message.Type == ai.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION {
				var docTypes []string
				ragNames := message.DocNames
				for _, rn := range ragNames {
					if docMap[rn] != nil {
						switch docMap[rn].DataType {
						case uint32(ai.DocType_DOCTYPE_QA):
							docTypes = append(docTypes, fmt.Sprintf("【QA】%s", docMap[rn].IndexText))
						case uint32(ai.DocType_DOCTYPE_TEXT):
							docTypes = append(docTypes, fmt.Sprintf("【文本】%s", docMap[rn].FileName))
						case uint32(ai.DocType_DOCTYPE_FILE):
							docTypes = append(docTypes, fmt.Sprintf("【文件】%s", docMap[rn].FileName))
						}
					}
				}
				message.DocTypeLabel = strings.Join(docTypes, ";")
			}
		}
	}

	return handleTargetFields(rsp.ExportMessages, fields, headers, make(map[string]columnHandler), lang)
}

func handleTargetFields[T any](items []T, fields []*bffaipb.ExportField, headers [][]string, mapping map[string]columnHandler, lang string) (*ExportTarget, error) {
	exportTarget := &ExportTarget{
		Headers: headers,
		Values:  make([][]string, 0, len(items)),
	}

	for _, item := range items {
		var row []string
		v := reflect.ValueOf(item)
		if v.Kind() == reflect.Ptr {
			v = v.Elem()
		}

		for _, column := range fields {
			if column.Id == 0 {
				var cellValue string
				hn, ok := mapping[column.Key]
				if ok {
					fieldKey := camelCase(hn.field)
					if fieldKey == "" {
						fieldKey = camelCase(column.Key)
					}
					fieldVal := v.FieldByName(fieldKey)
					if fieldVal.IsValid() && !(fieldVal.Kind() == reflect.Ptr && fieldVal.IsNil()) {
						if hn.handler != nil {
							cellValue = hn.handler(fieldVal.Interface(), item, lang)
						} else {
							cellValue = fmt.Sprintf("%v", fieldVal.Interface())
						}
					} else {
						cellValue = ""
					}
				} else {
					fieldKey := camelCase(column.Key)
					fieldVal := v.FieldByName(fieldKey)
					if fieldVal.IsValid() && !(fieldVal.Kind() == reflect.Ptr && fieldVal.IsNil()) {
						cellValue = fmt.Sprintf("%v", fieldVal.Interface())
					} else {
						cellValue = ""
					}
				}
				row = append(row, cellValue)
			} else {
				cellValue := formatter.formatLabel(column.Id, item)
				row = append(row, cellValue)
			}
		}
		exportTarget.Values = append(exportTarget.Values, row)
	}

	return exportTarget, nil
}

type Formatter struct{}

// formatContributors 将贡献者列表转换为字符串
func (f *Formatter) formatContributors(v interface{}, _ interface{}, _ string) string {
	contributors, ok := v.([]*ai.Contributor)
	if !ok || len(contributors) == 0 {
		return ""
	}
	var names []string
	for _, contributor := range contributors {
		names = append(names, contributor.Text)
	}
	return strings.Join(names, ";")
}

func (f *Formatter) formatUpdateBy(val interface{}, _ interface{}, _ string) string {
	if operator, ok := val.(*bffaipb.DocOperator); ok {
		switch operator.Type {
		case basepb.IdentityType_IDENTITY_TYPE_MGMT:
			return "碳LIVE运营"
		case basepb.IdentityType_IDENTITY_TYPE_TEAM:
			return operator.TeamName
		default:
			return operator.Username
		}
	}
	return ""
}

func (f *Formatter) shareEnableState(states []*ai.DocAssistantState) string {
	var hasEnable, hasDisable bool
	var stateSlice []string
	for _, state := range states {
		if state.State == ai.DocState_DOC_STATE_ENABLED {
			hasEnable = true
		}
		if state.State == ai.DocState_DOC_STATE_DISABLED {
			hasDisable = true
		}
	}
	if hasEnable {
		stateSlice = append(stateSlice, "启用")
	}
	if hasDisable {
		stateSlice = append(stateSlice, "停用")
	}
	if len(stateSlice) > 0 {
		return strings.Join(stateSlice, "、")
	}
	return ""
}

// Ai助手同步状态
func (f *Formatter) formatAssistantSyncStates(val interface{}, _ interface{}, _ string) string {
	if states, ok := val.([]*ai.DocAssistantState); ok {
		return f.shareEnableState(states)
	}
	return ""
}

func (f *Formatter) transState(state ai.DocState, states []*ai.DocAssistantState, embeddingState ai.DocEmbeddingState) string {
	if state == ai.DocState_DOC_STATE_DELETING {
		return "删除中"
	}
	// if len(states) == 0 {
	// 	return ""
	// }
	// if state != ai.DocState_DOC_STATE_ENABLED && state != ai.DocState_DOC_STATE_DISABLED {
	// 	return ""
	// }
	if embeddingState == 0 {
		return ""
	}
	if embeddingState == ai.DocEmbeddingState_DOC_EMBEDDING_STATE_SYNCED {
		return "已完成"
	}
	return "转换中"
}

func (f *Formatter) formatCollectionShareState(val interface{}, item interface{}, _ string) string {
	IsReceiveShared := func(states []*ai.DocAssistantState) bool {
		if len(states) == 0 {
			return false
		}
		for _, state := range states {
			if state.IsShared != 2 {
				return false
			}
		}
		return true
	}
	if states, ok := val.([]*ai.DocAssistantState); ok {
		if IsReceiveShared(states) {
			return "收到分享"
		}
		return f.shareEnableState(states)
	}
	return ""
}

func (f *Formatter) formatAssistantTransState(_ interface{}, item interface{}, _ string) string {
	switch file := item.(type) {
	case *bffaipb.CollectionTextFile:
		return f.transState(file.State, file.States, file.EmbeddingState)
	case *bffaipb.CollectionQA:
		return f.transState(file.State, file.States, file.EmbeddingState)
	default:
		return ""
	}
}

// formatShareAssistants 获取分享至助手
func (f *Formatter) formatShareAssistants(_ interface{}, item interface{}, lang string) string {
	switch file := item.(type) {
	case *bffaipb.CollectionTextFile:
		return strings.Join(f.formatStateAssistantNames(file.SharedStates, file.Assistants, lang), ";")
	case *bffaipb.CollectionQA:
		return strings.Join(f.formatStateAssistantNames(file.SharedStates, file.Assistants, lang), ";")
	default:
		return ""
	}
}

func (f *Formatter) formatStateAssistantNames(states []*ai.DocAssistantState, assistants []*ai.Assistant, lang string) []string {
	var assistantNames []string
	aIds := make(map[uint64]struct{})
	for _, share := range states {
		aIds[share.AssistantId] = struct{}{}
	}
	if lang == "en" {
		for _, assistant := range assistants {
			if _, exists := aIds[assistant.Id]; exists {
				assistantNames = append(assistantNames, assistant.NameEn)
			}
		}
	} else {
		for _, assistant := range assistants {
			if _, exists := aIds[assistant.Id]; exists {
				assistantNames = append(assistantNames, assistant.Name)
			}
		}
	}

	return assistantNames
}

// formatStateAssistants 获取助手
func (f *Formatter) formatStateAssistants(_ interface{}, item interface{}, lang string) string {
	switch file := item.(type) {
	case *bffaipb.CollectionTextFile:
		return strings.Join(f.formatStateAssistantNames(file.States, file.Assistants, lang), ";")
	case *bffaipb.CollectionQA:
		return strings.Join(f.formatStateAssistantNames(file.States, file.Assistants, lang), ";")
	default:
		return ""
	}
}

func (f *Formatter) formatUintBoolString(v interface{}, _ interface{}, lang string) string {
	b, ok := v.(uint32)
	if lang == "en" {
		if !ok || b == 0 {
			return "No"
		}
		return "Yes"
	}

	if !ok || b == 0 {
		return "否"
	}
	return "是"

}

func (f *Formatter) formatReference(_ interface{}, item interface{}, _ string) string {
	extract := func(references []*ai.DocReference) string {
		var names []string
		for _, reference := range references {
			if reference.Name != "" {
				names = append(names, reference.Name)
			} else if reference.Text != "" {
				names = append(names, reference.Text)
			}
		}
		return strings.Join(names, "\n")
	}

	switch doc := item.(type) {
	case *bffaipb.CollectionTextFile:
		return extract(doc.Reference)
	case *bffaipb.CollectionQA:
		return extract(doc.Reference)
	default:
		return ""
	}
}

func (f *Formatter) formatLabel(id uint64, item interface{}) string {
	var valueStr string
	var valueSlice []string
	getTypeValue := func(value *ai.LabelValue) string {
		switch t := value.GetAnyValue().(type) {
		case *ai.LabelValue_IntValue:
			valueStr = fmt.Sprintf("%d", t.IntValue)
		case *ai.LabelValue_TimeValue:
			u := time.Unix(t.TimeValue, 0)
			valueStr = fmt.Sprintf("%02d:%02d", u.Hour(), u.Minute())
		case *ai.LabelValue_YValue:
			u := time.Unix(t.YValue, 0)
			valueStr = fmt.Sprintf("%d", u.Year())
		case *ai.LabelValue_YmValue:
			u := time.Unix(t.YmValue, 0)
			valueStr = fmt.Sprintf("%d-%02d", u.Year(), u.Month())
		case *ai.LabelValue_YmdValue:
			u := time.Unix(t.YmdValue, 0)
			valueStr = fmt.Sprintf("%d-%02d-%02d", u.Year(), u.Month(), u.Day())
		case *ai.LabelValue_DatetimeValue:
			u := time.Unix(t.DatetimeValue, 0)
			valueStr = fmt.Sprintf("%d-%02d-%02d %02d:%02d", u.Year(), u.Month(), u.Day(), u.Hour(), u.Minute())
		case *ai.LabelValue_UintValue:
			valueStr = fmt.Sprintf("%d", t.UintValue)
		case *ai.LabelValue_FloatValue:
			valueStr = fmt.Sprintf("%.2f", t.FloatValue)
		case *ai.LabelValue_EnumValue:
			valueSlice = append(valueSlice, t.EnumValue)
		case *ai.LabelValue_EnumMValue:
			valueSlice = append(valueSlice, t.EnumMValue)
		case *ai.LabelValue_TextValue:
			valueStr = t.TextValue
		}
		return ""
	}

	switch doc := item.(type) {
	case *bffaipb.CollectionTextFile:
		for _, label := range doc.Labels {
			if label.Id == id {
				getTypeValue(label.Value)
			}
		}
	case *bffaipb.CollectionQA:
		for _, label := range doc.Labels {
			if label.Id == id {
				getTypeValue(label.Value)
			}
		}
	case *bffaipb.ChatInfo:
		for _, label := range doc.Labels {
			if label.Id == id {
				getTypeValue(label.Value)
			}
		}
	default:
	}

	if len(valueSlice) > 0 {
		return strings.Join(valueSlice, ";")
	}

	return valueStr
}

func camelCase(s string) string {
	parts := strings.Split(s, "_")
	for i := range parts {
		parts[i] = strings.Title(parts[i])
	}
	return strings.Join(parts, "")
}

var ugcLabelMap = map[basepb.DataType]string{
	basepb.DataType_DATA_TYPE_RESOURCE: "资源",
	basepb.DataType_DATA_TYPE_GRAPH:    "图谱",
	basepb.DataType_DATA_TYPE_PRODUCT:  "产品",
	basepb.DataType_DATA_TYPE_TEAM:     "团队",
}

type ExportMessage struct {
	UserName         string `json:"user_name"`
	AssistantName    string `json:"assistant_name"`
	RegionCode       string `json:"region_code"`
	LiveAgentName    string `json:"live_agent_name"`
	QuestionText     string `json:"question_text"`
	AnswerText       string `json:"answer_text"`
	StartTime        string `json:"start_time"`
	EndTime          string `json:"end_time"`
	ChatDuration     string `json:"chat_duration"`
	DocTypeLabel     string `json:"doc_type_label"`
	SearchLinkLabel  string `json:"search_link_label"`
	UgcTypeLabel     string `json:"ugc_type_label"`
	RatingScaleLabel string `json:"rating_scale_label"`
}

func (f *Formatter) formatTime(val interface{}, _ interface{}, _ string) string {
	if date, ok := val.(*timestamppb.Timestamp); ok {
		return date.AsTime().Format("2006-01-02 15:04:05")
	}
	return ""
}

func (f *Formatter) formatChatState(_ interface{}, item interface{}, lang string) string {
	if chat, ok := item.(*bffaipb.ChatInfo); ok {
		if chat.ChatState == ai.ChatCurrentState_CHAT_CURRENT_STATE_UNFINISHED {
			if chat.SupportType == ai.ChatSupportType_CHAT_SUPPORT_TYPE_AI {
				return "AI"
			}
			if chat.SupportType == ai.ChatSupportType_CHAT_SUPPORT_TYPE_LIVE_AGENT {
				if lang == "en" {
					return "Manual"
				}
				return "人工"
			}
		} else {
			if lang == "en" {
				return "Ended"
			}
			return "已结束"
		}
	}
	return ""
}

func (f *Formatter) formatChatType(_ interface{}, item interface{}, lang string) string {
	if chat, ok := item.(*bffaipb.ChatInfo); ok {
		switch chat.ChatType {
		case ai.ChatType_CHAT_TYPE_WEB:
			if lang == "en" {
				return "TanLIVE Web"
			}
			return "碳LIVE Web"
		case ai.ChatType_CHAT_TYPE_WECHAT:
			if lang == "en" {
				return "TanLIVE Web"
			}
			return "WeChat"
		case ai.ChatType_CHAT_TYPE_WHATSAPP:
			return "WhatsApp"

		}
	}
	return ""
}

func (f *Formatter) formatRegion(val interface{}, _ interface{}, _ string) string {
	if val == "CN" {
		return "中国大陆"
	}
	return "其他区域"
}

func (f *Formatter) formatCreateBy(val interface{}, _ interface{}, _ string) string {
	if user, ok := val.(*iampb.UserInfo); ok {
		if IsWechatUnionID(user.Username) {
			return user.NickName
		}
		return user.Username
	}
	return ""
}

func (f *Formatter) formatRatingScale(val interface{}, _ interface{}, _ string) string {
	if rs, ok := val.(ai.RatingScale); ok {
		switch rs {
		case ai.RatingScale_RATING_SCALE_UNSPECIFIED:
			return "未评价"
		case ai.RatingScale_RATING_SCALE_SATISFIED:
			return "满意"
		case ai.RatingScale_RATING_SCALE_AVERAGE:
			return "一般"
		case ai.RatingScale_RATING_SCALE_DISSATISFIED:
			return "不满意"
		}
	}
	return ""
}

func (f *Formatter) formatAvgDuration(val interface{}, _ interface{}, _ string) string {
	if floatVal, ok := val.(float32); ok {
		return fmt.Sprintf("%.3fs", floatVal)
	}
	return ""
}

func (f *Formatter) formatDocHits(val interface{}, _ interface{}, _ string) string {
	if floatVal, ok := val.(float32); ok {
		return fmt.Sprintf("%.2f", floatVal*100) + "%"
	}
	return ""
}

func (f *Formatter) formatDlAsRef(val interface{}, _ interface{}, lang string) string {
	b, ok := val.(ai.DocFileDownloadAsRef)
	if !ok {
		return ""
	}
	if lang == "en" {
		switch b {
		case ai.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_SHOW_NAME:
			return "Display only the text name"
		case ai.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_SHOW_URL:
			return "Downloadable"
		case ai.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_SEND:
			return "Send directly"
		case ai.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_HIDDEN:
			return "Hide"
		}
	}

	switch b {
	case ai.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_SHOW_NAME:
		return "仅显示文件名"
	case ai.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_SHOW_URL:
		return "可下载"
	case ai.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_SEND:
		return "直接发送"
	case ai.DocFileDownloadAsRef_DOC_FILE_DOWNLOAD_AS_REF_HIDDEN:
		return "隐藏"
	}
	return ""
}

func (f *Formatter) formatParseMode(val interface{}, _ interface{}, lang string) string {
	b, ok := val.(ai.DocParseMode)
	if !ok {
		return ""
	}
	if lang == "en" {
		switch b {
		case ai.DocParseMode_DOC_PARSE_MODE_FILE:
			return "File parsing"
		case ai.DocParseMode_DOC_PARSE_MODE_IMAGE:
			return "Image recognition"
		case ai.DocParseMode_DOC_PARSE_MODE_TABLE:
			return "Table recognition"
		case ai.DocParseMode_DOC_PARSE_MODE_SMART:
			return "Smart parsing"
		default:
			return ""
		}
	}
	switch b {
	case ai.DocParseMode_DOC_PARSE_MODE_FILE:
		return "文件解析"
	case ai.DocParseMode_DOC_PARSE_MODE_IMAGE:
		return "图像识别"
	case ai.DocParseMode_DOC_PARSE_MODE_TABLE:
		return "表格识别"
	case ai.DocParseMode_DOC_PARSE_MODE_SMART:
		return "智能解析"
	default:
		return ""
	}
}

var QaMatchPatternStrMappingZh = [...]string{"", "大模型召回", "完全匹配", "忽略标点匹配", "未命中", "包含"}
var QaMatchPatternStrMappingEn = [...]string{"", "Large model recall", "Exact match", "Ignore punctuation matching", "No matching", "Contains"}

func (f *Formatter) formatQaMatchPattern(val interface{}, _ interface{}, lang string) string {
	if val == nil {
		return ""
	}
	s, ok := val.([]ai.DocMatchPattern)
	if !ok {
		return ""
	}

	ss := make([]string, 0, len(s))
	for _, v := range s {
		if int(v) >= len(QaMatchPatternStrMappingZh) {
			continue
		}
		str := ""
		if lang == "en" {
			str = QaMatchPatternStrMappingEn[v]
		} else {
			str = QaMatchPatternStrMappingZh[v]
		}
		if str != "" {
			ss = append(ss, str)
		}
	}
	return strings.Join(ss, ";")
}

// CreateExportTask 创建导出任务
func CreateExportTask(ctx context.Context, userID uint64, taskType ai.ExportTaskType, fields interface{}, filter interface{}) (*ai.ExportTask, error) {
	fieldsBytes, err := json.Marshal(fields)
	if err != nil {
		return nil, err
	}
	filterBytes, err := json.Marshal(filter)
	if err != nil {
		return nil, err
	}

	task, err := client.AiNational.CreateExportTask(ctx, &ai.ReqCreateExportTask{
		UserId:         userID,
		Type:           taskType,
		FieldsSnapshot: string(fieldsBytes),
		FilterSnapshot: string(filterBytes),
	})
	if err != nil {
		return nil, err
	}
	return task.Task, nil
}
