package ai

import (
	"context"
	"encoding/json"
	"reflect"
	"strings"
	"sync"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/tql"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/ai"
	terrors "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	mclient "github.com/asim/go-micro/v3/client"
	"golang.org/x/exp/slices"
	"google.golang.org/protobuf/types/known/fieldmaskpb"
)

type ImportDocTaskInfo struct {
	SucceedCount uint64                  `json:"succeed_count"`
	FailedInfo   []*ImportDocTaskErrInfo `json:"failed_info"`
}

type ImportDocTaskErrInfo struct {
	DocType aipb.DocType
	DocName string
	Err     string
}

var (
	ImportDocTaskErr = xerrors.InternalServerError("")
	ImportRpcTimeOut = func() time.Duration {
		d, err := time.ParseDuration(config.GetStringOr("export.task.doc.timeout", "10m"))
		if err == nil {
			return d
		}
		return time.Second * 60 * 30
	}
)

var ImportParallism = func() int {
	return config.GetIntOr("import.task.doc.parallism", 1)
}

type TextFileImportLogic struct {
	ctx context.Context
	req *bffaipb.ReqImportTextFiles

	toCreate      map[int]*aipb.TextFile
	toUpdate      map[int]*aipb.ReqUpdateTextFile
	toUpdateShare map[int]*bffaipb.ReqCreateAssistantShare

	contributor     *aipb.Contributor
	operator        *aipb.Operator
	scopedAssistant []uint64
	labelTenant     uint64
}

func NewTextFileImportLogic(ctx context.Context, contributor *aipb.Contributor,
	operator *aipb.Operator, scopedAssistant []uint64, req *bffaipb.ReqImportTextFiles,
) *TextFileImportLogic {
	return &TextFileImportLogic{
		ctx:             ctx,
		req:             req,
		contributor:     contributor,
		operator:        operator,
		scopedAssistant: scopedAssistant,
		toCreate:        make(map[int]*aipb.TextFile),
		toUpdate:        make(map[int]*aipb.ReqUpdateTextFile),
		toUpdateShare:   make(map[int]*bffaipb.ReqCreateAssistantShare),
	}
}

func (l *TextFileImportLogic) pbTextFileToPbUpdateShare(order int, id uint64, v *bffaipb.ReqImportTextFile) *bffaipb.ReqCreateAssistantShare {
	if slices.Contains(v.GetMask().GetPaths(), "share_assistant_id") {
		l.toUpdateShare[order] = &bffaipb.ReqCreateAssistantShare{
			AssistantId: v.ShareAssistantId,
			DocId:       id,
		}
	}
	return nil
}

func (l *TextFileImportLogic) pbTextFileToPbUpdate(id uint64, v *bffaipb.ReqImportTextFile) *aipb.ReqUpdateTextFile {
	item := &aipb.ReqUpdateTextFile{
		Id:                id,
		Name:              v.FileName,
		Text:              v.Text,
		Contributor:       v.Contributor,
		UpdateBy:          l.operator,
		Mask:              v.Mask,
		ShowContributor:   v.ShowContributor,
		ScopedAssistantId: l.scopedAssistant,
		Reference:         v.Reference,
		States: func() []*aipb.DocAssistantState {
			r := make([]*aipb.DocAssistantState, 0, len(v.AssistantId))
			for _, vv := range v.AssistantId {
				// 先把状态设置为unspecified,后续重写状态
				r = append(r, &aipb.DocAssistantState{State: aipb.DocState_DOC_STATE_DISABLED, AssistantId: vv})
			}
			return r
		}(),
		Labels:        v.Labels,
		LabelTenant:   l.labelTenant,
		DownloadAsRef: v.DownloadAsRef,
	}
	l.rewriteUpdateMask(item, item.Mask)
	return item
}

func (l *TextFileImportLogic) rewriteUpdateMask(req *aipb.ReqUpdateTextFile, mask *fieldmaskpb.FieldMask) *fieldmaskpb.FieldMask {
	if mask == nil {
		return nil
	}
	mapping := map[string]string{
		// assistant_id 会被填充为state字段
		"assistant_id": "states",
	}
	for i, path := range mask.GetPaths() {
		if v, ok := mapping[path]; ok {
			mask.Paths[i] = v
		}
	}

	// 如果没有管理的助手，那么无需修改助手关联关系
	if len(l.scopedAssistant) == 0 {
		mask.Paths = slices.DeleteFunc(mask.Paths, func(s string) bool {
			return s == "states"
		})
	}
	// 如果贡献者为空，不需要更新贡献者
	if len(req.Contributor) == 0 {
		mask.Paths = slices.DeleteFunc(mask.Paths, func(s string) bool {
			return s == "contributor"
		})
	}
	// 添加更新人 mask
	mask.Paths = append(mask.Paths, "update_by")
	return mask
}

func (l *TextFileImportLogic) pbTextFileToPbCreate(v *bffaipb.ReqImportTextFile) *aipb.TextFile {
	item := &aipb.TextFile{
		Name:        v.FileName,
		Text:        v.Text,
		Contributor: v.Contributor,
		Reference:   v.Reference,
		CreateBy:    l.operator,
		UpdateBy:    l.operator,
		// 只支持文本类型 excel 导入，文件类型需要页面上传
		Type:            uint32(aipb.DocType_DOCTYPE_TEXT),
		ShowContributor: v.ShowContributor,
		Labels:          v.Labels,
		States: func() []*aipb.DocAssistantState {
			r := make([]*aipb.DocAssistantState, 0, len(v.AssistantId))
			for _, vv := range v.AssistantId {
				// 导入创建时，默认时禁用状态
				r = append(r, &aipb.DocAssistantState{State: aipb.DocState_DOC_STATE_DISABLED, AssistantId: vv})
			}
			return r
		}(),
		DownloadAsRef: v.DownloadAsRef,
		DataSource:    aipb.DocDataSource_DOC_DATA_SOURCE_COLLECTION,
	}
	if len(item.Contributor) == 0 {
		item.Contributor = append(item.Contributor, l.contributor)
	}
	return item
}

// SplitCreateAndUpdate 区分出哪些是创建/哪些是覆盖
func (l *TextFileImportLogic) SplitCreateAndUpdate() error {
	labelTenantId, err := GetCustomLabelTenantId(l.ctx)
	if err != nil {
		return err
	}
	l.labelTenant = labelTenantId

	// 分离需要校验重复的和强制新建的
	var needValidateItems []*aipb.TextFile
	var needValidateIndexes []int

	for i, v := range l.req.Items {
		if v.ForceCreate {
			// 强制新建，直接创建，不进行重复校验
			l.toCreate[i] = l.pbTextFileToPbCreate(v)
		} else {
			// 需要进行重复校验
			needValidateItems = append(needValidateItems, &aipb.TextFile{
				Name: v.FileName,
			})
			needValidateIndexes = append(needValidateIndexes, i)
		}
	}

	// 如果有需要校验的项目，进行批量校验
	if len(needValidateItems) > 0 {
		validReq := &aipb.ReqValidateTextFileInBulk{
			Contributor:     l.contributor,
			ScopedAssistant: l.scopedAssistant,
			DataSource:      aipb.DocDataSource_DOC_DATA_SOURCE_COLLECTION,
			Items:           needValidateItems,
		}
		rsp, err := client.AiNational.ValidateTextFileInBulk(l.ctx, validReq)
		if err != nil {
			return err
		}

		// 处理校验结果
		for j, originalIndex := range needValidateIndexes {
			v := l.req.Items[originalIndex]
			if rsp.Errors[j].Error != terrors.AiError_AiNoError {
				// 存在重复，进行更新
				l.toUpdate[originalIndex] = l.pbTextFileToPbUpdate(rsp.Errors[j].Id, v)
				l.pbTextFileToPbUpdateShare(originalIndex, rsp.Errors[j].Id, v)
			} else {
				// 不存在重复，进行创建
				l.toCreate[originalIndex] = l.pbTextFileToPbCreate(v)
			}
		}
	}

	err = l.rewriteUpdateAssistantState()
	if err != nil {
		return err
	}

	return nil
}

// 目前，一个doc在多个助手中，只能存在一中状态
func (l *TextFileImportLogic) rewriteUpdateAssistantState() error {
	if len(l.toUpdate) != 0 {
		ids := make([]uint64, 0, len(l.toUpdate))
		for _, v := range l.toUpdate {
			ids = append(ids, v.Id)
		}

		fs, err := client.AiNational.ListTextFile(l.ctx, &aipb.ReqListTextFile{
			Id:   ids,
			Mask: &fieldmaskpb.FieldMask{Paths: []string{"id"}},
		})
		if err != nil {
			return err
		}
		for _, v := range l.toUpdate {
			for _, vv := range fs.Items {
				if vv.Doc.Id == v.Id && len(vv.Doc.States) != 0 {
					// 直接取第一个的状态
					for _, state := range v.States {
						state.State = vv.Doc.States[0].State
					}
				}
			}
		}
	}
	return nil
}

func (l *TextFileImportLogic) doCreate(req *aipb.TextFile, errHandler func(*ImportDocTaskErrInfo)) {
	creatQ := &aipb.ReqCreateTextFileInBulk{
		Items: []*aipb.TextFile{req},
	}
	// 创建时分享至助手
	for _, item := range l.req.Items {
		if item.FileName == req.Name {
			creatQ.SharedAssistant = append(creatQ.SharedAssistant, &aipb.ReqCreateTextFileInBulk_Slice{Id: item.ShareAssistantId})
			break
		}
	}
	_, err := client.AiNational.CreateTextFileInBulk(l.ctx, creatQ, mclient.WithRequestTimeout(ImportRpcTimeOut()))
	if err != nil {
		errHandler(&ImportDocTaskErrInfo{DocName: req.Name, Err: ImportDocTaskErr.Error()})
		return
	}
	return
}

func (l *TextFileImportLogic) doUpdate(req *aipb.ReqUpdateTextFile, errHandler func(*ImportDocTaskErrInfo)) {
	updateQ := req
	is, err := IsDocContributor(l.ctx, req.Id)
	if err != nil {
		errHandler(&ImportDocTaskErrInfo{DocName: req.Name, Err: ImportDocTaskErr.Error()})
		return
	}
	if !is {
		IgnoreDocNoEditableFields(req)
	}
	_, err = client.AiNational.UpdateTextFile(l.ctx, updateQ, mclient.WithRequestTimeout(ImportRpcTimeOut()))
	if err != nil {
		errHandler(&ImportDocTaskErrInfo{DocName: req.Name, Err: ImportDocTaskErr.Error()})
	}
	return
}

func (l *TextFileImportLogic) doUpdateShare(req *bffaipb.ReqCreateAssistantShare, errHandler func(*ImportDocTaskErrInfo)) {
	docName := ""
	for _, doc := range l.toUpdate {
		if doc.Id == req.DocId {
			docName = doc.Name
		}
	}

	is, err := IsDocContributor(l.ctx, req.DocId)
	if err != nil {
		errHandler(&ImportDocTaskErrInfo{DocName: docName, Err: ImportDocTaskErr.Error()})
		return
	}
	// 非贡献者直接返回
	if !is {
		return
	}
	err = CreateDocShare(l.ctx, req, &bffaipb.RspCreateAssistantShare{}, mclient.WithRequestTimeout(ImportRpcTimeOut()))
	if err != nil {
		errHandler(&ImportDocTaskErrInfo{DocName: docName, Err: ImportDocTaskErr.Error()})
	}
	return
}

func (l *TextFileImportLogic) Do() error {
	// 先创建任务
	user := xsession.UserFromContext[iampb.UserInfo](l.ctx)
	task, err := client.AiNational.CreateExportTask(l.ctx, &aipb.ReqCreateExportTask{
		UserId:        user.Id,
		Type:          aipb.ExportTaskType_EXPORT_TASK_TYPE_FILE,
		OperationType: aipb.TaskOperationType_TASK_OPERATION_TYPE_IMPORT,
	})
	if err != nil {
		return err
	}
	err = UpdateTaskState(l.ctx, task.Task.Id, aipb.ExportTaskState_EXPORT_TASK_STATE_RUNNING)
	if err != nil {
		return err
	}

	xsync.SafeGo(l.ctx, func(ctx context.Context) error {
		var taskErrInfo []*ImportDocTaskErrInfo
		mtx := &sync.Mutex{}
		recordTaskErrInfo := func(info *ImportDocTaskErrInfo) {
			mtx.Lock()
			defer mtx.Unlock()
			info.DocType = aipb.DocType_DOCTYPE_FILE
			for _, v := range taskErrInfo {
				if v.DocName == info.DocName && v.DocType == info.DocType {
					return
				}
			}
			taskErrInfo = append(taskErrInfo, info)
		}

		defer func() {
			var errInfo []byte
			var taskInfo ImportDocTaskInfo
			taskInfo.FailedInfo = taskErrInfo
			taskInfo.SucceedCount = uint64(len(l.req.Items) - len(taskErrInfo))
			errInfo, _ = json.Marshal(taskInfo)
			state := aipb.ExportTaskState_EXPORT_TASK_STATE_COMPLETED
			if err != nil {
				state = aipb.ExportTaskState_EXPORT_TASK_STATE_FAILED
			}
			err = UpdateTaskState(l.ctx, task.Task.Id, state, string(errInfo))
			if err != nil {
				log.Errorf("update import text file task status failed, %s", err.Error())
			}
		}()
		// 1. 先拆分创建和更新
		err = l.SplitCreateAndUpdate()
		if err != nil {
			return err
		}

		parallelism := ImportParallism()
		synG := xsync.NewGroup(ctx, xsync.GroupGoOption(boot.TraceGo(ctx)), xsync.GroupQuota(parallelism))
		items, groupSize := groupSlice(l.req.Items, parallelism)
		for groupIdx, group := range items {
			groupIdx := groupIdx
			group := group
			synG.SafeGo(func(ctx context.Context) error {
				for offset := range group {
					idx := groupSize*groupIdx + offset

					// 创建
					createReq := l.toCreate[idx]
					if createReq != nil {
						l.doCreate(createReq, recordTaskErrInfo)
					}

					// 更新
					updateReq := l.toUpdate[idx]
					if updateReq != nil {
						l.doUpdate(updateReq, recordTaskErrInfo)
					}

					// 更新分享至
					updateShareReq := l.toUpdateShare[idx]
					if updateShareReq != nil {
						l.doUpdateShare(updateShareReq, recordTaskErrInfo)
					}
				}
				return nil
			})
		}
		synG.Wait()
		return nil
	}, boot.TraceGo(l.ctx))

	return nil
}

type QAImportLogic struct {
	ctx context.Context
	req *bffaipb.ReqImportQAs

	toCreate      map[int]*aipb.QA
	toUpdate      map[int]*aipb.ReqUpdateQA
	toUpdateShare map[int]*bffaipb.ReqCreateAssistantShare

	contributor     *aipb.Contributor
	operator        *aipb.Operator
	scopedAssistant []uint64
	labelTenant     uint64
}

func NewQAImportLogic(ctx context.Context, contributor *aipb.Contributor,
	operator *aipb.Operator, scopedAssistant []uint64, req *bffaipb.ReqImportQAs,
) *QAImportLogic {
	return &QAImportLogic{
		ctx:             ctx,
		req:             req,
		contributor:     contributor,
		operator:        operator,
		scopedAssistant: scopedAssistant,
		toCreate:        make(map[int]*aipb.QA),
		toUpdate:        make(map[int]*aipb.ReqUpdateQA),
		toUpdateShare:   make(map[int]*bffaipb.ReqCreateAssistantShare, 0),
	}
}

func (l *QAImportLogic) pbTextFileToPbUpdateShare(order int, id uint64, v *bffaipb.ReqImportQA) *bffaipb.ReqCreateAssistantShare {
	if slices.Contains(v.GetMask().GetPaths(), "share_assistant_id") {
		l.toUpdateShare[order] = &bffaipb.ReqCreateAssistantShare{
			AssistantId: v.ShareAssistantId,
			DocId:       id,
		}
	}
	return nil
}

func (l *QAImportLogic) pbTextFileToPbUpdate(id uint64, v *bffaipb.ReqImportQA) *aipb.ReqUpdateQA {
	item := &aipb.ReqUpdateQA{
		Id:                id,
		Question:          v.Question,
		Answer:            v.Answer,
		Contributor:       v.Contributor,
		Operator:          l.operator,
		Mask:              v.Mask,
		ShowContributor:   v.ShowContributor,
		ScopedAssistantId: l.scopedAssistant,
		States: func() []*aipb.DocAssistantState {
			r := make([]*aipb.DocAssistantState, 0, len(v.AssistantId))
			for _, vv := range v.AssistantId {
				// 先把状态设置为禁用,后续重写状态
				r = append(r, &aipb.DocAssistantState{State: aipb.DocState_DOC_STATE_DISABLED, AssistantId: vv})
			}
			return r
		}(),
		Labels:        v.Labels,
		Reference:     v.Reference,
		LabelTenant:   l.labelTenant,
		MatchPatterns: v.MatchPatterns,
	}
	l.rewriteMask(item, item.Mask)
	return item
}

func (l *QAImportLogic) pbTextFileToPbCreate(v *bffaipb.ReqImportQA) *aipb.QA {
	item := &aipb.QA{
		Question:        v.Question,
		Answer:          v.Answer,
		Contributor:     v.Contributor,
		CreateBy:        l.operator,
		UpdateBy:        l.operator,
		ShowContributor: v.ShowContributor,
		Labels:          v.Labels,
		States: func() []*aipb.DocAssistantState {
			r := make([]*aipb.DocAssistantState, 0, len(v.AssistantId))
			for _, vv := range v.AssistantId {
				// 导入创建时，默认时禁用状态
				r = append(r, &aipb.DocAssistantState{State: aipb.DocState_DOC_STATE_DISABLED, AssistantId: vv})
			}
			return r
		}(),
		Reference:     v.Reference,
		MatchPatterns: v.MatchPatterns,
	}
	if len(item.Contributor) == 0 {
		item.Contributor = append(item.Contributor, l.contributor)
	}
	return item
}

func (l *QAImportLogic) rewriteMask(req *aipb.ReqUpdateQA, mask *fieldmaskpb.FieldMask) *fieldmaskpb.FieldMask {
	if mask == nil {
		return nil
	}
	mapping := map[string]string{
		// assistant_id 会被填充为states字段
		"assistant_id": "states",
	}
	for i, path := range mask.GetPaths() {
		if v, ok := mapping[path]; ok {
			mask.Paths[i] = v
		}
	}

	// 如果没有管理的助手，那么无需修改助手关联关系
	if len(l.scopedAssistant) == 0 {
		mask.Paths = slices.DeleteFunc(mask.Paths, func(s string) bool {
			return s == "states"
		})
	}
	// 如果贡献者为空，不需要更新贡献者
	if len(req.Contributor) == 0 {
		mask.Paths = slices.DeleteFunc(mask.Paths, func(s string) bool {
			return s == "contributor"
		})
	}
	// 添加更新人 mask
	mask.Paths = append(mask.Paths, "update_by")
	return mask
}

// SplitCreateAndUpdate 区分出哪些是创建/哪些是覆盖
func (l *QAImportLogic) SplitCreateAndUpdate() error {
	labelTenantId, err := GetCustomLabelTenantId(l.ctx)
	if err != nil {
		return err
	}
	l.labelTenant = labelTenantId

	// 分离需要校验重复的和强制新建的
	var needValidateItems []*aipb.QA
	var needValidateIndexes []int

	for i, v := range l.req.Items {
		if v.ForceCreate {
			// 强制新建，直接创建，不进行重复校验
			l.toCreate[i] = l.pbTextFileToPbCreate(v)
		} else {
			// 需要进行重复校验
			needValidateItems = append(needValidateItems, &aipb.QA{
				Question: v.Question,
			})
			needValidateIndexes = append(needValidateIndexes, i)
		}
	}

	// 如果有需要校验的项目，进行批量校验
	if len(needValidateItems) > 0 {
		validReq := &aipb.ReqValidateQAInBulk{
			Contributor:     l.contributor,
			ScopedAssistant: l.scopedAssistant,
			Items:           needValidateItems,
		}
		rsp, err := client.AiNational.ValidateQAInBulk(l.ctx, validReq)
		if err != nil {
			return err
		}

		// 处理校验结果
		for j, originalIndex := range needValidateIndexes {
			v := l.req.Items[originalIndex]
			if rsp.Errors[j].Error != terrors.AiError_AiNoError {
				// 存在重复，进行更新
				l.toUpdate[originalIndex] = l.pbTextFileToPbUpdate(rsp.Errors[j].Id, v)
				l.pbTextFileToPbUpdateShare(originalIndex, rsp.Errors[j].Id, v)
			} else {
				// 不存在重复，进行创建
				l.toCreate[originalIndex] = l.pbTextFileToPbCreate(v)
			}
		}
	}

	err = l.rewriteUpdateAssistantState()
	if err != nil {
		return err
	}

	return nil
}

// 目前，一个doc在多个助手中，只能存在一中状态
func (l *QAImportLogic) rewriteUpdateAssistantState() error {
	if len(l.toUpdate) != 0 {
		ids := make([]uint64, 0, len(l.toUpdate))
		for _, v := range l.toUpdate {
			ids = append(ids, v.Id)
		}

		fs, err := client.AiNational.ListQA(l.ctx, &aipb.ReqListQA{
			Id: ids,
		})
		if err != nil {
			return err
		}
		for _, v := range l.toUpdate {
			for _, vv := range fs.Items {
				if vv.Id == v.Id && len(vv.States) != 0 {
					// 直接取第一个的状态
					for _, state := range v.States {
						state.State = vv.States[0].State
					}
				}
			}
		}
	}
	return nil
}

func (l *QAImportLogic) doCreate(req *aipb.QA, errHandler func(*ImportDocTaskErrInfo)) {
	creatQ := &aipb.ReqCreateQAInBulk{
		Items: []*aipb.QA{req},
	}
	// 创建时分享至助手
	for _, item := range l.req.Items {
		if item.Question == req.Question {
			creatQ.SharedAssistant = append(creatQ.SharedAssistant, &aipb.ReqCreateQAInBulk_Slice{Id: item.ShareAssistantId})
			break
		}
	}
	_, err := client.AiNational.CreateQAInBulk(l.ctx, creatQ, mclient.WithRequestTimeout(ImportRpcTimeOut()))
	if err != nil {
		errHandler(&ImportDocTaskErrInfo{DocName: req.Question, Err: ImportDocTaskErr.Error()})
	}
	return
}

func (l *QAImportLogic) doUpdate(req *aipb.ReqUpdateQA, errHandler func(*ImportDocTaskErrInfo)) {
	is, err := IsDocContributor(l.ctx, req.Id)
	if err != nil {
		errHandler(&ImportDocTaskErrInfo{DocName: req.Question, Err: ImportDocTaskErr.Error()})
		return
	}
	if !is {
		IgnoreDocNoEditableFields(req)
	}
	updateQ := req
	_, err = client.AiNational.UpdateQA(l.ctx, updateQ, mclient.WithRequestTimeout(ImportRpcTimeOut()))
	if err != nil {
		errHandler(&ImportDocTaskErrInfo{DocName: req.Question, Err: ImportDocTaskErr.Error()})
		return
	}
	return
}

func (l *QAImportLogic) doUpdateShare(req *bffaipb.ReqCreateAssistantShare, errHandler func(*ImportDocTaskErrInfo)) {
	docName := ""
	for _, doc := range l.toUpdate {
		if doc.Id == req.DocId {
			docName = doc.Question
		}
	}

	is, err := IsDocContributor(l.ctx, req.DocId)
	if err != nil {
		errHandler(&ImportDocTaskErrInfo{DocName: docName, Err: ImportDocTaskErr.Error()})
		return
	}
	// 非贡献者直接返回
	if !is {
		return
	}
	err = CreateDocShare(l.ctx, req, &bffaipb.RspCreateAssistantShare{}, mclient.WithRequestTimeout(ImportRpcTimeOut()))
	if err != nil {
		errHandler(&ImportDocTaskErrInfo{DocName: docName, Err: ImportDocTaskErr.Error()})
	}
	return
}

func (l *QAImportLogic) Do() error {
	// 先创建任务
	user := xsession.UserFromContext[iampb.UserInfo](l.ctx)
	task, err := client.AiNational.CreateExportTask(l.ctx, &aipb.ReqCreateExportTask{
		UserId:        user.Id,
		Type:          aipb.ExportTaskType_EXPORT_TASK_TYPE_QA,
		OperationType: aipb.TaskOperationType_TASK_OPERATION_TYPE_IMPORT,
	})
	if err != nil {
		return err
	}
	err = UpdateTaskState(l.ctx, task.Task.Id, aipb.ExportTaskState_EXPORT_TASK_STATE_RUNNING)
	if err != nil {
		return err
	}

	xsync.SafeGo(l.ctx, func(ctx context.Context) error {
		var taskErrInfo []*ImportDocTaskErrInfo
		mtx := &sync.Mutex{}
		recordTaskErrInfo := func(info *ImportDocTaskErrInfo) {
			mtx.Lock()
			defer mtx.Unlock()
			info.DocType = aipb.DocType_DOCTYPE_QA
			for _, v := range taskErrInfo {
				if v.DocName == info.DocName && v.DocType == info.DocType {
					return
				}
			}
			taskErrInfo = append(taskErrInfo, info)
		}

		defer func() {
			var errInfo []byte
			var taskInfo ImportDocTaskInfo
			taskInfo.FailedInfo = taskErrInfo
			taskInfo.SucceedCount = uint64(len(l.req.Items) - len(taskErrInfo))
			errInfo, _ = json.Marshal(taskInfo)
			state := aipb.ExportTaskState_EXPORT_TASK_STATE_COMPLETED
			if err != nil {
				state = aipb.ExportTaskState_EXPORT_TASK_STATE_FAILED
			}
			err = UpdateTaskState(l.ctx, task.Task.Id, state, string(errInfo))
			if err != nil {
				log.Errorf("update import text file task status failed, %s", err.Error())
			}
		}()

		// 1. 先拆分创建和更新
		err = l.SplitCreateAndUpdate()
		if err != nil {
			return err
		}

		parallelism := ImportParallism()
		synG := xsync.NewGroup(ctx, xsync.GroupGoOption(boot.TraceGo(ctx)), xsync.GroupQuota(parallelism))
		items, groupSize := groupSlice(l.req.Items, parallelism)
		for groupIdx, group := range items {
			groupIdx := groupIdx
			group := group
			synG.SafeGo(func(ctx context.Context) error {
				for offset := range group {
					idx := groupSize*groupIdx + offset

					// 创建
					createReq := l.toCreate[idx]
					if createReq != nil {
						l.doCreate(createReq, recordTaskErrInfo)
					}

					// 更新
					updateReq := l.toUpdate[idx]
					if updateReq != nil {
						l.doUpdate(updateReq, recordTaskErrInfo)
					}

					// 更新分享至
					updateShareReq := l.toUpdateShare[idx]
					if updateShareReq != nil {
						l.doUpdateShare(updateShareReq, recordTaskErrInfo)
					}
				}
				return nil
			})
		}
		synG.Wait()
		return nil
	}, boot.TraceGo(l.ctx))

	return nil
}

// 泛型分组函数
func groupSlice[T any](slice []T, groupCount int) ([][]T, int) {
	if groupCount <= 0 {
		return nil, 0 // 如果分组数无效，返回空
	}

	result := make([][]T, 0, groupCount)                    // 创建结果切片
	groupSize := (len(slice) + groupCount - 1) / groupCount // 计算每组的平均大小

	for i := 0; i < len(slice); i += groupSize {
		end := i + groupSize
		if end > len(slice) {
			end = len(slice) // 防止越界
		}
		result = append(result, slice[i:end]) // 将分组结果追加到结果切片中
	}
	return result, groupSize
}

var (
	QAEditableFieldsOnlyContributor       = []string{"question", "answer", "reference", "contributor", "show_contributor", "match_patterns"}
	TextFileEditableFieldsOnlyContributor = []string{"name", "text", "url", "reference", "contributor", "show_contributor", "download_as_ref"}
)

// IgnoreDocNoEditableFields 更新知识时，忽略那些只有贡献者的字段
func IgnoreDocNoEditableFields(doc any) {
	var newPath []string
	var rules []string
	var mask *fieldmaskpb.FieldMask
	switch t := doc.(type) {
	case *aipb.ReqUpdateQA:
		mask = t.Mask
		rules = QAEditableFieldsOnlyContributor
	case *aipb.ReqUpdateTextFile:
		mask = t.Mask
		rules = TextFileEditableFieldsOnlyContributor
	}
	if mask != nil {
		for _, field := range mask.Paths {
			if slices.Contains(rules, field) {
				continue
			}
			newPath = append(newPath, field)
		}
		mask.Paths = newPath
	}
}

// MergeDocVersionLag 合并 doc 在多个助手下的同步版本差
// 基于文档绑定的助手（在scopedAssistants范围内）来判断向量状态：
// 1. 如果doc没有绑定到scopedAssistants中的任何助手，返回UNSPECIFIED
// 2. 如果doc绑定到scopedAssistants中的助手，显示对应的同步状态
// 3. 特殊处理：即使t_assistant_doc记录被删除，但t_doc_sync_version中还有对应助手的同步记录，仍显示同步状态
// 4. 历史数据处理：绑定但没有sync_version记录的doc会生成默认的已同步记录
func MergeDocVersionLag(bindAssistant []*aipb.DocAssistantState, scopedAssistants []uint64, versions []*aipb.EmbeddingVersion) ai.DocEmbeddingState {
	// 没有绑定助手
	if len(bindAssistant) == 0 && {
	}

	hasVersionInScope := false
	var ragVersionGt0 bool

	for _, version := range versions {
		// 只处理在scopedAssistants范围内的版本记录
		if slices.Contains(scopedAssistants, version.AssistantId) {
			hasVersionInScope = true

			// 如果有任何版本不一致，返回同步中
			if version.DocVersion != version.RagVersion {
				return ai.DocEmbeddingState_DOC_EMBEDDING_STATE_SYNCING
			}

			// 检查是否至少同步过一次
			if version.RagVersion > 0 {
				ragVersionGt0 = true
			}
		}
	}

	// 如果没有在scope范围内的版本记录，返回UNSPECIFIED
	if !hasVersionInScope {
		return ai.DocEmbeddingState_DOC_EMBEDDING_STATE_UNSPECIFIED
	}

	// 至少同步过一次
	if ragVersionGt0 {
		return ai.DocEmbeddingState_DOC_EMBEDDING_STATE_SYNCED
	}

	// 有版本记录但从未同步过，返回同步中
	return ai.DocEmbeddingState_DOC_EMBEDDING_STATE_SYNCING
}

type ListTextFilesOption struct {
	// 是否返回知识提示
	WithTips bool
}

// ListTextFiles 获取文本/文件列表
func ListTextFiles(ctx context.Context, req *bffaipb.ReqListTextFiles, rsp *bffaipb.RspListTextFiles, textExcerpt bool, opts ...*ListTextFilesOption) error {
	pbReq, managedAssistants, contributor, err := CreateAiListTextFile(ctx, req, textExcerpt, opts...)
	if err != nil {
		return err
	}

	pbRsp, err := client.AiNational.ListTextFile(ctx, pbReq, mclient.WithRequestTimeout(30*time.Second))
	if err != nil {
		log.WithContext(ctx).Errorf("list ai collection doc qa failed: %v", err)
		return err
	}
	var operators []*aipb.Operator
	for _, v := range pbRsp.Items {
		if v.Doc.CreateBy != nil {
			operators = append(operators, v.Doc.CreateBy)
		}
		if v.Doc.UpdateBy != nil {
			operators = append(operators, v.Doc.UpdateBy)
		}
	}
	operatorsToShow, err := GetAiDocOperatorsShowInfo(ctx, operators...)
	if err != nil {
		return err
	}
	var contributors []*aipb.Contributor
	for _, v := range pbRsp.Items {
		contributors = append(contributors, v.Doc.Contributor...)
	}
	_, err = GeAiDoctContributorShowInfo(ctx, contributors...)
	if err != nil {
		return err
	}
	contributor, err = GetUserAsContributor(ctx)
	if err != nil {
		return err
	}
	sharedReceivers := make([]*aipb.DocShareReceiver, 0, len(pbRsp.Items))
	for _, v := range pbRsp.Items {
		sharedReceivers = append(sharedReceivers, v.Doc.ShareReceivers...)
	}
	teamMap, userMap, err := GetDocShareTeamUserShowInfo(ctx, sharedReceivers)
	if err != nil {
		return err
	}

	for _, v := range pbRsp.Items {
		owned, shared, isContributor := SpiltDocAssistantsByShared(contributor, managedAssistants, v.Doc.Contributor, v.Doc.States, &v.Doc.State)
		team, user := BuildDocShareReceiversConditional(isContributor, v.Doc.ShareReceivers, teamMap, userMap)
		// 检查是否收到分享
		receivedShare := CheckReceivedShare(ctx, isContributor, contributor, managedAssistants, v.Doc.ShareReceivers, v.Doc.States)
		rsp.Items = append(rsp.Items, &bffaipb.CollectionTextFile{
			Id:                 v.Doc.Id,
			FileName:           v.Doc.Name,
			Text:               v.Doc.Text,
			Assistants:         v.Doc.Assistants,
			States:             owned,
			SharedStates:       shared,
			SharedTeams:        team,
			SharedUsers:        user,
			Contributor:        v.Doc.Contributor,
			Url:                v.Doc.Url,
			HitCount:           v.Doc.HitCount,
			UgcType:            v.Doc.UgcType,
			UgcId:              v.Doc.UgcId,
			UpdateDate:         v.Doc.UpdateDate,
			CreateDate:         v.Doc.CreateDate,
			CreateBy:           operatorsToShow[v.Doc.CreateBy],
			UpdateBy:           operatorsToShow[v.Doc.UpdateBy],
			EmbeddingState:     MergeDocVersionLag(managedAssistants, v.Doc.EmbeddingVersion),
			ParseProgress:      v.Doc.ParseProgress,
			ShowContributor:    v.Doc.ShowContributor,
			State:              v.Doc.State,
			Labels:             v.Doc.Labels,
			Reference:          v.Doc.Reference,
			DownloadAsRef:      v.Doc.DownloadAsRef,
			ParseMode:          v.Doc.ParseMode,
			HasOverSizedTables: v.Doc.HasOverSizedTables,
			HasRepeated:        v.Doc.HasRepeated,
			DataSourceState:    v.Doc.DataSourceState,
			ReceivedShare:      receivedShare,
		})
	}
	rsp.TotalCount = pbRsp.Total
	rsp.FailParseCount = pbRsp.FailParseCount
	return nil
}

// CreateAiListTextFile ...
func CreateAiListTextFile(ctx context.Context, req *bffaipb.ReqListTextFiles, textExcerpt bool,
	opts ...*ListTextFilesOption) (
	pbReq *aipb.ReqListTextFile, managedAssistants []uint64, contributor *aipb.Contributor, err error,
) {
	// 设置默认的数据源
	SetDefaultDataSource(req)

	managedAssistants, err = GetManagedAssistants(ctx)
	if err != nil {
		return
	}
	aids, err := RewriteAssistantIds(ctx, req.AssistantId...)
	if err != nil {
		return
	}
	assistantIdHasNull := slices.Contains(req.AssistantId, 0)
	req.AssistantId = slices.DeleteFunc(req.AssistantId, func(id uint64) bool {
		return id == 0
	})
	labelTenantId, err := GetCustomLabelTenantId(ctx)
	if err != nil {
		return
	}
	pbReq = &aipb.ReqListTextFile{
		State:   req.State,
		OrderBy: req.OrderBy,
		Page: &base.Paginator{
			Offset: req.Offset,
			Limit:  req.Limit,
		},
		Search: &aipb.ReqListTextFile_Search{
			Text:     req.GetSearch().GetText(),
			FileName: req.GetSearch().GetFileName(),
		},
		GroupRepeated:       req.GroupRepeated,
		ShowContributor:     req.ShowContributor,
		TextExcerpt:         textExcerpt, // true则只获取片段300字符
		Labels:              req.Labels,
		OrderByLabel:        req.OrderByLabel,
		LabelTenant:         labelTenantId,
		WithLabelDetailInfo: true,
		Id:                  req.Ids,
		DownloadAsRef:       req.DownloadAsRef,
		ParseState:          req.ParseState,
		ParseMode:           req.ParseMode,
		DataSource:          req.DataSource,
		DataSourceState:     req.DataSourceState,
		EmbeddingState:      req.EmbeddingState,
	}
	if len(req.ShareAssistantId) != 0 || len(req.ShareTeamId) != 0 || len(req.ShareUserId) != 0 {
		pbReq.SharedReceivers = &aipb.DocSharedReceiverFilter{
			AssistantId: req.ShareAssistantId,
			TeamId:      req.ShareTeamId,
			UserId:      req.ShareUserId,
		}
	}

	// 处理知识提示过滤条件
	if req.TipFilter != nil && req.TipFilter.Warning {
		pbReq.TipFilter = &aipb.ReqListTextFile_TipFilter{
			WarningGroup: &aipb.ReqListTextFile_TipFilter_WarningGroup{
				ParseFailed:   true, // 过滤解析失败的记录
				TableOversize: true, // 过滤表格过长的记录
			},
		}
	}

	for _, v := range req.UpdateBy {
		pbReq.UpdateBy = append(pbReq.UpdateBy, &aipb.OperatorFilter{
			Operator:      v,
			OnlyMatchType: v.Type == base.IdentityType_IDENTITY_TYPE_MGMT,
		})
	}
	for _, v := range req.CreateBy {
		pbReq.CreateBy = append(pbReq.CreateBy, &aipb.OperatorFilter{
			Operator:      v,
			OnlyMatchType: v.Type == base.IdentityType_IDENTITY_TYPE_MGMT,
		})
	}
	if len(req.AssistantId) != 0 {
		pbReq.AssistantId = aids
	}
	if assistantIdHasNull {
		pbReq.AssistantId = append(pbReq.AssistantId, 0)
	}
	if pbReq.TenantCond == nil {
		pbReq.TenantCond = &aipb.ReqListTextFile_TenantCond{}
	}
	pbReq.TenantCond.AssistantId = managedAssistants

	if len(req.ExcludedAssistantId) != 0 {
		pbReq.ExcludedAssistantId = req.ExcludedAssistantId
	}
	if len(req.Contributor) != 0 {
		for _, v := range req.Contributor {
			pbReq.Contributor = append(pbReq.Contributor, &aipb.ContributorFilter{
				Contributor:   v,
				OnlyMatchType: v.Type == base.IdentityType_IDENTITY_TYPE_MGMT,
			})
		}
	}
	contributor, err = GetUserAsContributor(ctx)
	if err != nil {
		return
	}
	if pbReq.TenantCond == nil {
		pbReq.TenantCond = &aipb.ReqListTextFile_TenantCond{}
	}
	pbReq.TenantCond.Contributor = []*aipb.ContributorFilter{{Contributor: contributor}}

	if len(req.SharedState) != 0 {
		contributor, err = GetUserAsContributor(ctx)
		if err != nil {
			return
		}
		c := &aipb.ContributorFilter{Contributor: contributor}
		pbReq.SharedState = &aipb.ListDocSharedFileter{
			Contributor: c,
			SharedState: req.SharedState,
		}
	}
	if len(req.ShareAssistantId) != 0 {
		contributor, err = GetUserAsContributor(ctx)
		if err != nil {
			return
		}
		c := &aipb.ContributorFilter{Contributor: contributor}
		pbReq.Contributor = append(pbReq.Contributor, c)
	}
	if len(req.TqlExpression) != 0 {
		var exp tql.Expression
		exp, err = ParseTQL(req.TqlExpression)
		if err != nil {
			return
		}
		err = UnHashTQLLabelId(exp)
		if err != nil {
			return
		}
		pbReq.Tql = exp.String()
	}

	if len(opts) != 0 && opts[0] != nil {
		opt := opts[0]
		if opt.WithTips {
			pbReq.WithTips = true
		}
	}
	return
}

// CreateAiListQA ...
func CreateAiListQA(ctx context.Context, req *bffaipb.ReqListQA) (pbReq *aipb.ReqListQA,
	managedAssistants []uint64, contributor *aipb.Contributor, err error,
) {
	managedAssistants, err = GetManagedAssistants(ctx)
	if err != nil {
		return
	}
	aids, err := RewriteAssistantIds(ctx, req.AssistantId...)
	if err != nil {
		return
	}
	labelTenantId, err := GetCustomLabelTenantId(ctx)
	if err != nil {
		return
	}
	assistantIdHasNull := slices.Contains(req.AssistantId, 0)
	req.AssistantId = slices.DeleteFunc(req.AssistantId, func(id uint64) bool {
		return id == 0
	})

	pbReq = &aipb.ReqListQA{
		State:   req.State,
		OrderBy: req.OrderBy,
		Page: &base.Paginator{
			Offset: req.Offset,
			Limit:  req.Limit,
		},
		Search:              &aipb.ReqListQA_Search{Qa: req.Search},
		GroupRepeated:       req.GroupRepeated,
		ShowContributor:     req.ShowContributor,
		Labels:              req.Labels,
		OrderByLabel:        req.OrderByLabel,
		LabelTenant:         labelTenantId,
		WithLabelDetailInfo: true,
		Id:                  req.Ids,
		MatchPatterns:       req.MatchPatterns,
		WithTips:            true,
		EmbeddingState:      req.EmbeddingState,
	}
	if len(req.ShareAssistantId) != 0 || len(req.ShareTeamId) != 0 || len(req.ShareUserId) != 0 {
		pbReq.SharedReceivers = &aipb.DocSharedReceiverFilter{
			AssistantId: req.ShareAssistantId,
			TeamId:      req.ShareTeamId,
			UserId:      req.ShareUserId,
		}
	}
	for _, v := range req.UpdateBy {
		pbReq.UpdateBy = append(pbReq.UpdateBy, &aipb.OperatorFilter{
			Operator:      v,
			OnlyMatchType: v.Type == base.IdentityType_IDENTITY_TYPE_MGMT,
		})
	}
	for _, v := range req.CreateBy {
		pbReq.CreateBy = append(pbReq.CreateBy, &aipb.OperatorFilter{
			Operator:      v,
			OnlyMatchType: v.Type == base.IdentityType_IDENTITY_TYPE_MGMT,
		})
	}
	if len(req.AssistantId) != 0 {
		pbReq.AssistantId = aids
	}
	if assistantIdHasNull {
		pbReq.AssistantId = append(pbReq.AssistantId, 0)
	}

	if pbReq.TenantCond == nil {
		pbReq.TenantCond = &aipb.ReqListQA_TenantCond{}
	}
	pbReq.TenantCond.AssistantId = managedAssistants

	if len(req.ExcludedAssistantId) != 0 {
		pbReq.ExcludedAssistantId = req.ExcludedAssistantId
	}
	if len(req.Contributor) != 0 {
		for _, v := range req.Contributor {
			pbReq.Contributor = append(pbReq.Contributor, &aipb.ContributorFilter{
				Contributor:   v,
				OnlyMatchType: v.Type == base.IdentityType_IDENTITY_TYPE_MGMT,
			})
		}
	}
	contributor, err = GetUserAsContributor(ctx)
	if err != nil {
		return
	}
	if pbReq.TenantCond == nil {
		pbReq.TenantCond = &aipb.ReqListQA_TenantCond{}
	}
	pbReq.TenantCond.Contributor = []*aipb.ContributorFilter{{Contributor: contributor}}

	if len(req.SharedState) != 0 {
		contributor, err = GetUserAsContributor(ctx)
		if err != nil {
			return
		}
		c := &aipb.ContributorFilter{Contributor: contributor}
		pbReq.SharedState = &aipb.ListDocSharedFileter{
			Contributor: c,
			SharedState: req.SharedState,
		}
	}
	if len(req.ShareAssistantId) != 0 {
		contributor, err = GetUserAsContributor(ctx)
		if err != nil {
			return
		}
		c := &aipb.ContributorFilter{Contributor: contributor}
		pbReq.Contributor = append(pbReq.Contributor, c)
	}

	if len(req.TqlExpression) != 0 {
		var exp tql.Expression
		exp, err = ParseTQL(req.TqlExpression)
		if err != nil {
			return
		}
		err = UnHashTQLLabelId(exp)
		if err != nil {
			return
		}
		pbReq.Tql = exp.String()
	}

	// 处理知识提示过滤条件
	if req.TipFilter != nil && req.TipFilter.Warning {
		pbReq.TipFilter = &aipb.ReqListQA_TipFilter{
			WarningGroup: &aipb.ReqListQA_TipFilter_WarningGroup{
				QuestionOversize: true, // 过滤问题过长的记录
			},
		}
	}
	return
}

// SetDefaultDataSource 为请求设置默认的数据源值
// 当请求中的DataSource为0时，设置为DOC_DATA_SOURCE_COLLECTION
// T可以是任何包含DataSource字段的请求类型
func SetDefaultDataSource[T any](req T) T {
	// 使用反射获取DataSource字段
	val := reflect.ValueOf(req)

	// 如果是指针，获取它指向的值
	if val.Kind() == reflect.Ptr {
		if val.IsNil() {
			return req
		}
		val = val.Elem()
	}

	// 只处理结构体类型
	if val.Kind() != reflect.Struct {
		return req
	}

	// 查找DataSource字段
	field := val.FieldByName("DataSource")
	if !field.IsValid() || !field.CanSet() {
		return req
	}

	// 获取字段类型，检查是否为DocDataSource类型
	fieldType := field.Type()

	// 检查字段类型名称是否包含DocDataSource
	if !strings.Contains(fieldType.String(), "DocDataSource") {
		return req
	}

	// 根据字段类型分别处理
	switch field.Kind() {
	case reflect.Int32:
		if field.Int() == 0 {
			field.SetInt(int64(aipb.DocDataSource_DOC_DATA_SOURCE_COLLECTION))
		}
	}

	return req
}
