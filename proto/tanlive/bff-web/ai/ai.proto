syntax = "proto3";

package tanlive.bff_web.ai;
option go_package = "e.coding.net/tencent-ssv/tanlive/services/proto/bff-web/ai";
import "google/protobuf/timestamp.proto";
import "tanlive/ai/ai.proto";
import "tanlive/base/base.proto";
import "tanlive/base/ugc.proto";
import "tanlive/iam/iam.proto";
import "tanlive/options.proto";

message Chat {
  uint64 id = 1;
  string title = 2;
  uint64 create_by = 3;
  google.protobuf.Timestamp create_date = 4;
  bool is_shared = 5;
}


// 对话管理端信息
message ChatInfo {
  uint64 id = 1;
  string title = 2;

  tanlive.iam.UserInfo create_by = 3;
  google.protobuf.Timestamp update_date = 4;
  google.protobuf.Timestamp create_date = 5;
  tanlive.ai.ChatType chat_type = 6;
  uint64 assistant_id = 7;
  string assistant_name = 8;
  tanlive.ai.ChatCurrentState chat_state = 9;
  // 当前服务状态
  tanlive.ai.ChatSupportType support_type = 10;
  // 对话中问题数量
  uint32 question_cnt = 11;
  // 自定义标签kv对
  repeated tanlive.ai.CustomLabel labels = 12;
  string region_code = 13;
  tanlive.ai.RatingScale  rating_scale = 14;
  float doc_hits = 15;
  float avg_duration = 16;
  uint32 reject_job_result = 17;
  // 是否转过人工服务
  int32 is_manual = 18;
}


message TencentDoc{
  string title = 1;
  string url = 2;
  string file_name = 3;
  string file_id = 4;
  string file_type = 5;
  string file_create_user = 6;
  string file_owner_name = 7;
  string file_create_time = 8;
  string file_modify_time = 9;
  string file_browse_time = 10;
  string file_url = 12;
}

message Assistant{
  uint64 id = 1;
  string name = 2;
  string name_en = 3;
  string website_route = 4;
  bool search_debug = 5;
  float threshold = 6;
  int32 top_n = 7;
  float text_weight = 8;
  // 关键词召回条数
  int32 text_recall_top_n = 9;
  bool clean_chunks = 10;
}

message ChatDetail {
  uint64 id = 1;
  string title = 2;
  repeated tanlive.ai.EventChatMessage messages = 3;
  // 地区
  //  tanlive.base.Region region = 4;

  tanlive.iam.UserInfo create_by = 5;
  google.protobuf.Timestamp finish_date = 6;
  google.protobuf.Timestamp create_date = 7;
  tanlive.ai.ChatType chat_type = 8;
  tanlive.ai.ChatCurrentState chat_state = 9;
  // 当前服务状态
  tanlive.ai.ChatSupportType support_type = 10;
  // 微信客服助手头像
  string assistant_avatar = 11;
  // 非web端时通过次字段返回消息详情
  repeated tanlive.ai.ChatSendRecordInfo records = 12;
  uint64 assistant_id = 13;
}

message DocShareTeamReceiver{
  uint64 id = 1;
  string name = 2;
  string name_en = 3;
}

message DocShareUserReceiver{
  uint64 id = 1;
  string name = 2;
}

message CollectionQA{
  uint64 id = 1;
  // 问题
  string question = 2;
  // 答案
  string answer = 3;
  // 用户端
  repeated tanlive.ai.Assistant assistants = 4;
  repeated tanlive.ai.DocAssistantState states = 5;
  // 贡献者
  repeated tanlive.ai.Contributor contributor = 6;
  // 参考资料
  repeated tanlive.ai.DocReference reference = 7;
  // 命中次数
  uint32 hit_count = 8;

  DocOperator create_by = 9;
  DocOperator  update_by = 10;
  google.protobuf.Timestamp update_date = 11;
  google.protobuf.Timestamp create_date = 12;
  tanlive.ai.DocEmbeddingState embedding_state = 13;
  // 分享的用户端
  repeated tanlive.ai.DocAssistantState shared_states = 14;
  // 分享的团队
  repeated DocShareTeamReceiver shared_teams = 15;
  // 分享的用户
  repeated DocShareUserReceiver shared_users = 16;
  // 是否显示贡献者
  uint32 show_contributor = 17;
  // 状态(不包含助手对应的状态)
  tanlive.ai.DocState state = 18;
  repeated tanlive.ai.CustomLabel labels = 19;
  // 匹配模式
  repeated tanlive.ai.DocMatchPattern match_patterns = 20;
  bool question_oversize = 21;
  bool has_repeated = 22;
  // 是否收到分享
  bool received_share = 23;
}

message DocOperator{
  base.IdentityType type = 1;
  // 用户账户: 用户id
  // 团队账户: 用户id
  // 运营端账户: 运营端用户id
  uint64 id = 2;
  // 用户名称
  string username = 3;
  // 用户所属团队名称
  string team_name = 4;
  // 用户id，只有为团队用户时，才需要
  uint64 user_id = 5;
}

message CollectionTextFile{
  uint64  id = 1;
  // 文件/文本名称
  string file_name = 2;
  // 文件/文本内容
  string text = 3;
  // 用户端
  repeated tanlive.ai.Assistant assistants = 4;
  // 状态(不包含助手对应的状态)
  tanlive.ai.DocState state = 5;
  // 贡献者
  repeated tanlive.ai.Contributor contributor = 6;
  // 文件url
  string url = 7;
  // 命中次数
  uint32 hit_count = 8;
  // ugc类型
  base.DataType ugc_type = 9;
  // ugc的id
  uint64 ugc_id = 10;

  DocOperator create_by = 11;
  DocOperator  update_by = 12;
  google.protobuf.Timestamp update_date = 13;
  google.protobuf.Timestamp create_date = 14;
  tanlive.ai.DocEmbeddingState embedding_state = 15;
  // 解析进度，0.5 = 50%
  float parse_progress = 16;
  // 是否显示贡献者
  uint32 show_contributor = 17;
  repeated tanlive.ai.DocAssistantState states = 18;
  repeated tanlive.ai.DocAssistantState shared_states = 19;
  repeated tanlive.ai.CustomLabel labels = 20;
  // 参考资料
  repeated tanlive.ai.DocReference reference = 21;
  // 是否可以作为参考资料下载
  tanlive.ai.DocFileDownloadAsRef download_as_ref = 22;
  tanlive.ai.DocParseMode parse_mode = 23;

  // 知识提示
  // 是否有超长标题表格
  bool has_over_sized_tables = 30;
  // 是否内容重复（租户内）
  bool has_repeated = 31;
  // 外部数据源信息
  uint32 data_source_state = 32;

  // 分享的团队
  repeated DocShareTeamReceiver shared_teams = 24;
  // 分享的用户
  repeated DocShareUserReceiver shared_users = 25;
  // 是否收到分享
  bool received_share = 26;
}

enum AiMessageDoType {
  AI_MESSAGE_DO_TYPE_UNSPECIFIED = 0;
  // 返回
  AI_MESSAGE_DO_TYPE_UNSPECIFIED_RETURN = 1;
  // 继续执行
  AI_MESSAGE_DO_TYPE_UNSPECIFIED_NEXT = 2;
  // 返回后执行，开启协程执行
  AI_MESSAGE_DO_TYPE_UNSPECIFIED_RETURN_NEXT = 3;
}

enum MiniProgramSourceType {
  SourceTypePC = 0;
  SourceTypeH5Scheme = 1;
  SourceTypeH5Url = 2;
}

message ExportField {
  string key = 1;
  uint64 id = 2;
  string example = 3;
  string label = 4;
  string rule = 5;
  string tips = 6;
}

// 反馈参考文献
message FeedbackReference {
  // 文献类型
  tanlive.ai.ReferenceType type = 1 [(validator) = "required"];
  // 跳转链接（type为1时必填）
  string url = 2 [(validator) = "required_if=type 1,omitempty,url"];
  // 文本（type为2时必填）
  string text = 3 [(validator) = "required_if=type 2"];
  // 文件路径（type为3时必填）
  string file_path = 4 [(validator) = "required_if=type 3"];
  // 文件名称（type为3时必填）
  string file_name = 5 [(validator) = "required_if=type 3"];
}

// 分享类型
enum ShareType {
  SHARE_TYPE_UNSPECIFIED = 0;
  // 链接分享
  SHARE_TYPE_LINK = 1;
  // 二维码分享
  SHARE_TYPE_QR_CODE = 2;
  // 小程序码分享
  SHARE_TYPE_MINI_PROGRAM = 3;
}

// 分享状态
enum ShareStatus {
  SHARE_STATUS_UNSPECIFIED = 0;
  // 有效
  SHARE_STATUS_ACTIVE = 1;
  // 已失效（手动失效）
  SHARE_STATUS_INACTIVE = 2;
  // 已过期
  SHARE_STATUS_EXPIRED = 3;
}

// 聊天分享信息
message ChatShare {
  // 分享记录ID
  uint64 id = 1;
  // 分享唯一标识
  string share_id = 2;
  // 原会话ID
  uint64 chat_id = 3;
  // 分享类型
  ShareType share_type = 4;
  // 分享状态
  ShareStatus share_status = 5;
  // 分享者ID
  uint64 shared_by = 6;
  // 访问次数
  int32 access_count = 7;
  // 分享创建时间
  google.protobuf.Timestamp share_date = 8;
  // 分享过期时间
  google.protobuf.Timestamp expire_date = 9;
  // 最后访问时间
  google.protobuf.Timestamp last_access_time = 10;
  // 助手ID
  uint64 assistant_id = 11;
  // 分享链接
  string share_url = 12;
}

// 分享访问记录
message ChatShareAccess {
  // 记录ID
  uint64 id = 1;
  // 分享ID
  string share_id = 2;
  // 访问者ID
  uint64 access_by = 3;
  // 访问时间
  google.protobuf.Timestamp access_date = 4;
  // 是否点击了"接着聊"
  bool is_continued = 7;
  // 新建的会话ID
  uint64 new_chat_id = 8;
}

// HTTP请求信息
message HttpRequestInfo {
  // 远程地址
  string remote_addr = 1;
  // User-Agent
  string user_agent = 2;
}

message ExternalSourceUser{
  // 用户ID
  string hash_user_id = 1;
  // 用户昵称
  string nickname = 2;
  // 用户头像
  string avatar = 3;
  // 用户状态
  tanlive.ai.ExternalSourceUserAuthState auth_state = 4;
  // 用户授权来源(xw qq)
  string auth_source = 5;
}