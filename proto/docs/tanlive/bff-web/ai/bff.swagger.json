{"swagger": "2.0", "info": {"title": "tanlive/bff-web/ai/bff.proto", "version": "version not set"}, "tags": [{"name": "AiBff"}, {"name": "AiGuestBff"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/ai/accept_feedback": {"post": {"summary": "采用用户反馈", "operationId": "AiBff_AcceptFeedback", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspAcceptFeedback"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqAcceptFeedback"}}], "tags": ["AiBff"]}}, "/ai/auto_chunk_doc": {"post": {"summary": "自动文档分段", "operationId": "AiBff_AutoChunkDoc", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspAutoChunkDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqAutoChunkDoc"}}], "tags": ["AiBff"]}}, "/ai/batch_user_assistant_limit": {"post": {"summary": "批量获取用户对应助手的限制情况", "operationId": "AiBff_BatchUserAssistantLimit", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspBatchUserAssistantLimit"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqBatchUserAssistantLimit"}}], "tags": ["AiBff"]}}, "/ai/bind_mini_program_normal_account": {"post": {"summary": "绑定用户小程序普通账号", "operationId": "AiBff_BindMiniProgramNormalAccount", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspBindMiniProgramNormalAccount"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqBindMiniProgramNormalAccount"}}], "tags": ["AiBff"]}}, "/ai/bind_mini_program_phone_account": {"post": {"summary": "绑定用户小程序手机号", "operationId": "AiBff_BindMiniProgramPhoneAccount", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspBindMiniProgramPhoneAccount"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqBindMiniProgramPhoneAccount"}}], "tags": ["AiBff"]}}, "/ai/bind_mini_program_uniid": {"post": {"summary": "绑定用户小程序unionid", "operationId": "AiBff_BindMiniProgramUniID", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqBindMiniProgramUniID"}}], "tags": ["AiBff"]}}, "/ai/bind_once_user_mini_program": {"post": {"summary": "一键绑定用户和小程序unionid", "operationId": "AiBff_BindOnceUserMiniProgram", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspBindOnceUserMiniProgram"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqBindOnceUserMiniProgram"}}], "tags": ["AiBff"]}}, "/ai/bind_unitoke_by_code": {"post": {"summary": "绑定用户小程序临时映射unionid", "operationId": "AiBff_BindUnitokenByCode", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqBindUnitokenByCode"}}], "tags": ["AiBff"]}}, "/ai/bind_user_phone_by_code": {"post": {"summary": "绑定手机号通过小程序code", "operationId": "AiBff_BindUserPhoneByCode", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspBindUserPhoneByCode"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqBindUserPhoneByCode"}}], "tags": ["AiBff"]}}, "/ai/chat_share_continue": {"post": {"summary": "从分享继续聊天", "operationId": "AiBff_ContinueChatFromShare", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspContinueChatFromShare"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqContinueChatFromShare"}}], "tags": ["AiBff"]}}, "/ai/chat_share_create": {"post": {"summary": "创建聊天分享", "operationId": "AiBff_CreateChatShare", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspCreateChatShare"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqCreateChatShare"}}], "tags": ["AiBff"]}}, "/ai/chat_share_get": {"post": {"summary": "获取公开分享详情", "operationId": "AiGuestBff_GetPublicChatShare", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetPublicChatShare"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetPublicChatShare"}}], "tags": ["AiGuestBff"]}}, "/ai/collection/auth_tencent_code": {"post": {"summary": "腾讯文档授权code处理", "operationId": "AiBff_AuthTencentCode", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspAuthTencentCode"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqAuthTencentCode"}}], "tags": ["AiBff"]}}, "/ai/collection/batch_update_docs": {"post": {"summary": "批量更新doc的特定字段值", "operationId": "AiBff_BatchUpdateDocAttr", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspBatchUpdateDocAttr"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqBatchUpdateDocAttr"}}], "tags": ["AiBff"]}}, "/ai/collection/clone_doc": {"post": {"summary": "克隆QA/文本/文件", "operationId": "AiBff_CloneDoc", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCloneDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCloneDoc"}}], "tags": ["AiBff"]}}, "/ai/collection/create_doc_query": {"post": {"operationId": "AiBff_CreateDocQuery", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspCreateDocQuery"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqCreateDocQuery"}}], "tags": ["AiBff"]}}, "/ai/collection/create_gtb_doc_text": {"post": {"summary": "批量导入绿技行", "operationId": "AiBff_CreateGTBDocText", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateGTBDocText"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateGTBDocText"}}], "tags": ["AiBff"]}}, "/ai/collection/create_qas": {"post": {"summary": "创建QA", "operationId": "AiBff_CreateQAs", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateQAs"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateQAs"}}], "tags": ["AiBff"]}}, "/ai/collection/create_tencent_doc_auth_url": {"post": {"summary": "创建腾讯文档授权链接", "operationId": "AiBff_CreateTencentDocAuthUrl", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspCreateTencentDocAuthUrl"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqCreateTencentDocAuthUrl"}}], "tags": ["AiBff"]}}, "/ai/collection/create_text_files": {"post": {"summary": "创建文本或文件", "operationId": "AiBff_CreateTextFiles", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateTextFiles"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateTextFiles"}}], "tags": ["AiBff"]}}, "/ai/collection/del_tencent_doc_auth": {"post": {"summary": "删除腾讯文档授权", "operationId": "AiBff_DelTencentDocAuth", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqDelTencentDocAuth"}}], "tags": ["AiBff"]}}, "/ai/collection/delete_docs": {"post": {"summary": "删除Doc，包括QA，文本或文件", "operationId": "AiBff_DeleteDocs", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspDeleteDocs"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDeleteDocs"}}], "tags": ["AiBff"]}}, "/ai/collection/describe_account_is_gtb": {"post": {"summary": "查询是否为绿技行用户", "operationId": "AiBff_DescribeAccountIsGTB", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspDescribeAccountIsGTB"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDescribeAccountIsGTB"}}], "tags": ["AiBff"]}}, "/ai/collection/describe_doc_list": {"post": {"summary": "获取腾讯文档列表", "operationId": "AiBff_DescribeDocList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspDescribeDocList"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDescribeDocList"}}], "tags": ["AiBff"]}}, "/ai/collection/describe_doc_tab": {"post": {"summary": "获取文档标签", "operationId": "AiBff_DescribeDocTab", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspDescribeDocTab"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqDescribeDocTab"}}], "tags": ["AiBff"]}}, "/ai/collection/describe_my_doc": {"post": {"summary": "查询选中的腾讯文档file_id", "operationId": "AiBff_DescribeMyDoc", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspDescribeMyDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqDescribeMyDoc"}}], "tags": ["AiBff"]}}, "/ai/collection/describe_tencent_doc_task": {"post": {"summary": "查询腾讯文档任务状态", "operationId": "AiBff_DescribeTencentDocTask", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspDescribeTencentDocTask"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqDescribeTencentDocTask"}}], "tags": ["AiBff"]}}, "/ai/collection/describe_tencent_token": {"post": {"summary": "查询腾讯文档缓存是否为空", "operationId": "AiBff_DescribeTencentToken", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspDescribeTencentToken"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDescribeTencentToken"}}], "tags": ["AiBff"]}}, "/ai/collection/get_qa_tip": {"post": {"summary": "查询QA的知识提示（问题超长，内容重复）等信息", "operationId": "AiBff_GetQaTip", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspGetQaTip"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqGetQaTip"}}], "tags": ["AiBff"]}}, "/ai/collection/get_text_file": {"post": {"summary": "id查询文本/文件详情", "operationId": "AiBff_GetTextFile", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetTextFile"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetTextFile"}}], "tags": ["AiBff"]}}, "/ai/collection/get_text_file_tip": {"post": {"summary": "查询文本文件的知识提示（解析失败，文件名重复，表头过长）等信息", "operationId": "AiBff_GetTextFileTip", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspGetTextFileTip"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqGetTextFileTip"}}], "tags": ["AiBff"]}}, "/ai/collection/import_qas": {"post": {"summary": "导入文本/文件", "operationId": "AiBff_ImportQAs", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspImportQAs"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqImportQAs"}}], "tags": ["AiBff"]}}, "/ai/collection/import_tencent_doc": {"post": {"summary": "腾讯文档导入", "operationId": "AiBff_ImportTencentDoc", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspImportTencentDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqImportTencentDoc"}}], "tags": ["AiBff"]}}, "/ai/collection/import_tencent_doc_webclip": {"post": {"summary": "导入腾讯文档网页剪辑", "operationId": "AiBff_ImportTencentDocWebClip", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspImportTencentDocWebClip"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqImportTencentDocWebClip"}}], "tags": ["AiBff"]}}, "/ai/collection/import_text_files": {"post": {"summary": "导入文本/文件", "operationId": "AiBff_ImportTextFiles", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspImportTextFiles"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqImportTextFiles"}}], "tags": ["AiBff"]}}, "/ai/collection/list_contributor": {"post": {"summary": "查询贡献者列表", "operationId": "AiBff_ListContributor", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListContributor"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListContributor"}}], "tags": ["AiBff"]}}, "/ai/collection/list_external_source_user": {"post": {"summary": "查询腾讯文档授权列表", "operationId": "AiBff_ListExternalSourceUser", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListExternalSourceUser"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListExternalSourceUser"}}], "tags": ["AiBff"]}}, "/ai/collection/list_filename": {"post": {"summary": "查询文件列表", "operationId": "AiBff_ListCollectionFileName", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListCollectionFileName"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListCollectionFileName"}}], "tags": ["AiBff"]}}, "/ai/collection/list_operator": {"post": {"summary": "查询更新人列表", "operationId": "AiBff_ListOperator", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListOperator"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListOperator"}}], "tags": ["AiBff"]}}, "/ai/collection/list_qa": {"post": {"summary": "查询QA列表", "operationId": "AiBff_ListQA", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListQA"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListQA"}}], "tags": ["AiBff"]}}, "/ai/collection/list_shared_assistant": {"post": {"summary": "查询已分享的助手列表，用于表头筛选", "operationId": "AiBff_ListSharedAssistant", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListSharedAssistant"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListSharedAssistant"}}], "tags": ["AiBff"]}}, "/ai/collection/list_shared_team": {"post": {"summary": "查询已分享的团队列表，用于表头筛选", "operationId": "AiBff_ListSharedTeam", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListSharedTeam"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListSharedTeam"}}], "tags": ["AiBff"]}}, "/ai/collection/list_shared_user": {"post": {"summary": "查询已分享的用户列表，用于表头筛选", "operationId": "AiBff_ListSharedUser", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListSharedUser"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListSharedUser"}}], "tags": ["AiBff"]}}, "/ai/collection/list_text_files": {"post": {"summary": "查询文本/文件列表", "operationId": "AiBff_ListTextFiles", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListTextFiles"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListTextFiles"}}], "tags": ["AiBff"]}}, "/ai/collection/modify_doc_tab": {"post": {"summary": "创建文档标签", "operationId": "AiBff_ModifyDocTab", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspModifyDocTab"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqModifyDocTab"}}], "tags": ["AiBff"]}}, "/ai/collection/onoff_docs": {"post": {"summary": "启用/禁用doc", "operationId": "AiBff_OnOffDocs", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspOnOffDocs"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqOnOffDocs"}}], "tags": ["AiBff"]}}, "/ai/collection/reimport_tencent_doc": {"post": {"summary": "重新导入腾讯文档", "operationId": "AiBff_ReimportTencentDoc", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspReimportTencentDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqReimportTencentDoc"}}], "tags": ["AiBff"]}}, "/ai/collection/reparse_text_files": {"post": {"summary": "重新解析文件", "operationId": "AiBff_ReparseTextFiles", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspReparseTextFiles"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqReparseTextFiles"}}], "tags": ["AiBff"]}}, "/ai/collection/search_collection": {"post": {"summary": "collection向量查询", "operationId": "AiBff_SearchCollection", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspSearchCollection"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqSearchCollection"}}], "tags": ["AiBff"]}}, "/ai/collection/update_qas": {"post": {"summary": "更新QA", "operationId": "AiBff_UpdateQAs", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqUpdateQAs"}}], "tags": ["AiBff"]}}, "/ai/collection/update_text_files": {"post": {"summary": "更新文本或文件", "operationId": "AiBff_UpdateTextFiles", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqUpdateTextFiles"}}], "tags": ["AiBff"]}}, "/ai/collection/validate_qas": {"post": {"summary": "校验待创建QA", "operationId": "AiBff_ValidateQAs", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspValidateQAs"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqValidateQAs"}}], "tags": ["AiBff"]}}, "/ai/collection/validate_text_files": {"post": {"summary": "校验待创建文本文件", "operationId": "AiBff_ValidateTextFiles", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspValidateTextFiles"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqValidateTextFiles"}}], "tags": ["AiBff"]}}, "/ai/confirm_ai_service_terms": {"post": {"summary": "确认AI服务协议", "operationId": "AiBff_ConfirmAiServiceTerms", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqConfirmAiServiceTerms"}}], "tags": ["AiBff"]}}, "/ai/convert_custom_label": {"post": {"summary": "转换标签", "operationId": "AiBff_ConvertCustomLabel", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspConvertCustomLabel"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqConvertCustomLabel"}}], "tags": ["AiBff"]}}, "/ai/create_assistant_receiver": {"post": {"summary": "创建助手接收方设置", "operationId": "AiBff_CreateDocShareConfigReceiverAssistant", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateDocShareConfigReceiverAssistant"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqCreateDocShareConfigReceiverAssistant"}}], "tags": ["AiBff"]}}, "/ai/create_assistant_sender": {"post": {"summary": "创建助手发送方设置", "operationId": "AiBff_CreateDocShareConfigSender", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateDocShareConfigSender"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqCreateDocShareConfigSender"}}], "tags": ["AiBff"]}}, "/ai/create_assistant_share": {"post": {"summary": "创建助手分享", "operationId": "AiBff_CreateAssistantShare", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateAssistantShare"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateAssistantShare"}}], "tags": ["AiBff"]}}, "/ai/create_chat": {"post": {"summary": "创建会话", "operationId": "AiBff_CreateChat", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateChat"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateChat"}}], "tags": ["AiBff"]}}, "/ai/create_chat_export_task": {"post": {"summary": "导出会话", "operationId": "AiBff_CreateChatExportTask", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspExportTask"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateChatExportTask"}}], "tags": ["AiBff"]}}, "/ai/create_chat_question": {"post": {"summary": "创建会话question", "operationId": "AiBff_CreateChatQuestion", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateChatQuestion"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateChatQuestion"}}], "tags": ["AiBff"]}}, "/ai/create_feedback": {"post": {"summary": "创建用户反馈（助手维度）", "operationId": "AiBff_CreateFeedback", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateFeedback"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateFeedback"}}], "tags": ["AiBff"]}}, "/ai/create_file_export_task": {"post": {"summary": "导出文本文件", "operationId": "AiBff_CreateFileExportTask", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspExportTask"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateFileExportTask"}}], "tags": ["AiBff"]}}, "/ai/create_message_export_task": {"post": {"summary": "导出会话消息", "operationId": "AiBff_CreateChatMessageExportTask", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspExportTask"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateChatMessageExportTask"}}], "tags": ["AiBff"]}}, "/ai/create_qa_export_task": {"post": {"summary": "导出qa", "operationId": "AiBff_CreateQaExportTask", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspExportTask"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateQaExportTask"}}], "tags": ["AiBff"]}}, "/ai/create_user_team_receiver": {"post": {"summary": "创建个人/团队接收方设置", "operationId": "AiBff_CreateDocShareConfigReceiverUserTeam", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateDocShareConfigReceiverUserTeam"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqCreateDocShareConfigReceiverUserTeam"}}], "tags": ["AiBff"]}}, "/ai/delete_chat": {"post": {"summary": "删除会话", "operationId": "AiBff_DeleteChat", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDeleteChat"}}], "tags": ["AiBff"]}}, "/ai/delete_custom_labels": {"post": {"summary": "删除自定义标签", "operationId": "AiBff_DeleteCustomLabels", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqDeleteCustomLabels"}}], "tags": ["AiBff"]}}, "/ai/describe_chat_messages": {"post": {"summary": "获取会话消息列表", "operationId": "AiBff_DescribeChatMessages", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspDescribeChatMessages"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDescribeChatMessages"}}], "tags": ["AiBff"]}}, "/ai/describe_chat_region_code": {"post": {"summary": "获取所有会话的地区编码", "operationId": "AiBff_DescribeChatRegionCode", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspDescribeChatRegionCode"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqDescribeChatRegionCode"}}], "tags": ["AiBff"]}}, "/ai/describe_chats": {"post": {"summary": "获取会话列表", "operationId": "AiBff_DescribeChats", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspDescribeChats"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDescribeChats"}}], "tags": ["AiBff"]}}, "/ai/describe_export_tasks": {"post": {"summary": "查询导出任务", "operationId": "AiBff_DescribeExportTasks", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspDescribeExportTasks"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqDescribeExportTasks"}}], "tags": ["AiBff"]}}, "/ai/describe_feedback_region_code": {"post": {"summary": "获取所有教学反馈的地区编码", "operationId": "AiBff_DescribeFeedbackRegionCode", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspDescribeFeedbackRegionCode"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqDescribeFeedbackRegionCode"}}], "tags": ["AiBff"]}}, "/ai/describe_message_file_state": {"post": {"summary": "查询message附件状态", "operationId": "AiBff_DescribeMessageFileState", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspDescribeMessageFileState"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDescribeMessageFileState"}}], "tags": ["AiBff"]}}, "/ai/find_feedback": {"post": {"summary": "查询用户反馈详情", "operationId": "AiBff_FindFeedback", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspFindFeedback"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqFindFeedback"}}], "tags": ["AiBff"]}}, "/ai/get_account_once_bind_status": {"post": {"summary": "获取当前用户是否一键绑定提示", "operationId": "AiBff_GetAccountOnceBindStatus", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetAccountOnceBindStatus"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetAccountOnceBindStatus"}}], "tags": ["AiBff"]}}, "/ai/get_account_unionid_status": {"post": {"summary": "获取用户账号union_id绑定状态", "operationId": "AiBff_GetAccountUnionIdStatus", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetAccountUnionIdStatus"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetAccountUnionIdStatus"}}], "tags": ["AiBff"]}}, "/ai/get_assistant_chunk_config": {"post": {"summary": "获取助手分段配置", "operationId": "AiBff_GetAssistantChunkConfig", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetAssistantChunkConfig"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetAssistantChunkConfig"}}], "tags": ["AiBff"]}}, "/ai/get_assistant_config": {"post": {"summary": "获取助手配置", "operationId": "AiGuestBff_GetAssistantConfig", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetAssistantConfig"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetAssistantConfig"}}], "tags": ["AiGuestBff"]}}, "/ai/get_assistant_options": {"post": {"summary": "获取助手下拉选项", "operationId": "AiGuestBff_GetAssistantOptions", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspGetAssistantOptions"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {}}}], "tags": ["AiGuestBff"]}}, "/ai/get_assistants_miniprogram": {"post": {"summary": "小程序获取助手配置", "operationId": "AiBff_GetAssistantsMiniprogram", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetAssistantsMiniprogram"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetAssistantsMiniprogram"}}], "tags": ["AiBff"]}}, "/ai/get_chat_detail": {"post": {"summary": "AI对话详情", "operationId": "AiBff_GetChatDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspGetChatDetail"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqGetChatDetail"}}], "tags": ["AiBff"]}}, "/ai/get_chat_message_detail": {"post": {"summary": "获取消息详情", "operationId": "AiBff_GetChatMessageDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetChatMessageDetail"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetChatMessageDetail"}}], "tags": ["AiBff"]}}, "/ai/get_chunk_doc_tasks": {"post": {"summary": "查询文档分段任务列表", "operationId": "AiBff_GetChunkDocTasks", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspGetChunkDocTasks"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqGetChunkDocTasks"}}], "tags": ["AiBff"]}}, "/ai/get_custom_label_value_topn": {"post": {"summary": "获取标签topn值,默认top10", "operationId": "AiBff_GetCustomLabelValueTopN", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspGetCustomLabelValueTopN"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqGetCustomLabelValueTopN"}}], "tags": ["AiBff"]}}, "/ai/get_custom_labels": {"post": {"summary": "获取自定义标签列表", "operationId": "AiBff_ListCustomLabel", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListCustomLabel"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListCustomLabel"}}], "tags": ["AiBff"]}}, "/ai/get_doc_chunks": {"post": {"summary": "查询文档分段信息", "operationId": "AiBff_GetDocChunks", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspGetDocChunks"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqGetDocChunks"}}], "tags": ["AiBff"]}}, "/ai/get_doc_embedding_models": {"post": {"summary": "查询文档的向量化模型", "operationId": "AiBff_GetDocEmbeddingModels", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspGetDocEmbeddingModels"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqGetDocEmbeddingModels"}}], "tags": ["AiBff"]}}, "/ai/get_feedback_logs": {"post": {"summary": "查询用户反馈日志列表", "operationId": "AiBff_GetFeedbackLogs", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspGetFeedbackLogs"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqGetFeedbackLogs"}}], "tags": ["AiBff"]}}, "/ai/get_feedbacks": {"post": {"summary": "查询用户反馈列表", "operationId": "AiBff_GetFeedbacks", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspGetFeedbacks"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqGetFeedbacks"}}], "tags": ["AiBff"]}}, "/ai/get_mini_program_assistant_limit": {"post": {"summary": "获取小程序助手限制情况", "operationId": "AiBff_GetMiniProgramAssistantLimit", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetMiniProgramAssistantLimit"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetMiniProgramAssistantLimit"}}], "tags": ["AiBff"]}}, "/ai/get_mini_program_auth": {"post": {"summary": "获取小程序文档相关权限", "operationId": "AiBff_GetMiniProgramAuth", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetMiniProgramAuth"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetMiniProgramAuth"}}], "tags": ["AiBff"]}}, "/ai/get_mini_program_login_url": {"post": {"summary": "获取小程序登录url", "operationId": "AiBff_GetMiniProgramLoginURL", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetMiniProgramLoginURL"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetMiniProgramLoginURL"}}], "tags": ["AiBff"]}}, "/ai/get_mini_program_user_info": {"post": {"summary": "通过选择账号绑定小程序，替换临时账号", "operationId": "AiBff_BindSelectedAccount", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetMiniProgramUserInfo"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetMiniProgramUserInfo"}}], "tags": ["AiBff"]}}, "/ai/get_my_ai_service_terms_confirmation": {"post": {"summary": "获取我的AI服务协议确认情况", "operationId": "AiBff_GetMyAiServiceTermsConfirmation", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetMyAiServiceTermsConfirmation"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {}}}], "tags": ["AiBff"]}}, "/ai/get_my_assistants": {"post": {"summary": "获取我的助手列表", "operationId": "AiBff_GetMyAssistants", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetMyAssistants"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetMyAssistants"}}], "tags": ["AiBff"]}}, "/ai/get_my_assistants_team": {"post": {"summary": "获取我团队的助手列表", "operationId": "AiBff_GetMyTeamAssistants", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetMyAssistants"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetMyAssistants"}}], "tags": ["AiBff"]}}, "/ai/get_webview_to_mini_program_token": {"post": {"summary": "获取webview 迁移至小程序token", "operationId": "AiBff_GetWebViewToMiniProgramToken", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetWebViewToMiniProgramToken"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetWebViewToMiniProgramToken"}}], "tags": ["AiBff"]}}, "/ai/list_assistant": {"post": {"summary": "获取管理的ai助手列表", "operationId": "AiBff_ListAssistant", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListAssistant"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListAssistant"}}], "tags": ["AiBff"]}}, "/ai/list_assistant_receiver": {"post": {"summary": "查询助手接收方设置", "operationId": "AiBff_ListDocShareConfigReceiverAssistant", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListDocShareConfigReceiverAssistant"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListDocShareConfigReceiverAssistant"}}], "tags": ["AiBff"]}}, "/ai/list_assistant_sender": {"post": {"summary": "查询助手发送方设置", "operationId": "AiBff_ListeDocShareConfigSender", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListeDocShareConfigSender"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListeDocShareConfigSender"}}], "tags": ["AiBff"]}}, "/ai/list_assistant_to_share_doc": {"post": {"summary": "查询可分享的助手列表", "operationId": "AiBff_ListAssistantCanShareDoc", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListAssistantCanShareDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListAssistantCanShareDoc"}}], "tags": ["AiBff"]}}, "/ai/list_chat": {"post": {"summary": "AI对话管理列表", "operationId": "AiBff_ListChat", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListChat"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListChat"}}], "tags": ["AiBff"]}}, "/ai/list_chat_live_agent": {"post": {"summary": "获取人工坐席列表", "operationId": "AiBff_ListChatLiveAgent", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListChatLiveAgent"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListChatLiveAgent"}}], "tags": ["AiBff"]}}, "/ai/list_my_assistant_ids": {"post": {"summary": "查询已经设置并开启知识库接收的助手", "operationId": "AiBff_ListMyAssistantIds", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListMyAssistantIds"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListMyAssistantIds"}}], "tags": ["AiBff"]}}, "/ai/list_team_to_share_doc": {"post": {"summary": "查询可分享的团队列表", "operationId": "AiBff_ListTeamCanShareDoc", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListTeamCanShareDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListTeamCanShareDoc"}}], "tags": ["AiBff"]}}, "/ai/list_user_team_receiver": {"post": {"summary": "查询个人/团队接收方设置", "operationId": "AiBff_ListDocShareConfigReceiverUserTeam", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListDocShareConfigReceiverUserTeam"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListDocShareConfigReceiverUserTeam"}}], "tags": ["AiBff"]}}, "/ai/list_user_to_share_doc": {"post": {"summary": "查询可分享的个人列表", "operationId": "AiBff_ListUserCanShareDoc", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListUserCanShareDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListUserCanShareDoc"}}], "tags": ["AiBff"]}}, "/ai/manual_chunk_doc": {"post": {"summary": "手动文档分段", "operationId": "AiBff_ManualChunkDoc", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspManualChunkDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqManualChunkDoc"}}], "tags": ["AiBff"]}}, "/ai/modify_custom_labels": {"post": {"summary": "插入或更新自定义标签", "operationId": "AiBff_ModifyCustomLabels", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspModifyCustomLabels"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqModifyCustomLabels"}}], "tags": ["AiBff"]}}, "/ai/nebula/create_task": {"post": {"summary": "创建星云任务", "operationId": "AiBff_CreateNebulaTask", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspCreateNebulaTask"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqCreateNebulaTask"}}], "tags": ["AiBff"]}}, "/ai/nebula/describe_medata": {"post": {"summary": "查看投影元数据", "operationId": "AiBff_DescribeNebulaData", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspDescribeNebulaData"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqDescribeNebulaData"}}], "tags": ["AiBff"]}}, "/ai/nebula/describe_projection": {"post": {"summary": "查看投影坐标", "operationId": "AiBff_DescribeNebulaProjection", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspDescribeNebulaProjection"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqDescribeNebulaProjection"}}], "tags": ["AiBff"]}}, "/ai/nebula/describe_task": {"post": {"summary": "查看星云任务详情", "operationId": "AiBff_DescribeNebulaTask", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspDescribeNebulaTask"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqDescribeNebulaTask"}}], "tags": ["AiBff"]}}, "/ai/nebula/describe_task_list": {"post": {"summary": "查看星云任务列表", "operationId": "AiBff_DescribeNebulaTaskList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspDescribeNebulaTaskList"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqDescribeNebulaTaskList"}}], "tags": ["AiBff"]}}, "/ai/nebula/list_assistants": {"post": {"summary": "知识星云，获取助手列表", "operationId": "AiBff_ListNebulaAssistants", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListNebulaAssistants"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListNebulaAssistants"}}], "tags": ["AiBff"]}}, "/ai/nebula/list_contributors": {"post": {"summary": "知识星云，获取助手下的贡献者筛选项", "operationId": "AiBff_ListNebulaContributors", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListNebulaContributors"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListNebulaContributors"}}], "tags": ["AiBff"]}}, "/ai/proxy_chat_url": {"post": {"summary": "获取助手url网页title", "operationId": "AiBff_ProxyChatHtmlUrl", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspProxyChatHtmlUrl"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqProxyChatHtmlUrl"}}], "tags": ["AiBff"]}}, "/ai/rate_ai_answer": {"post": {"summary": "评价AI回答", "operationId": "AiBff_RateAiAnswer", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqRateAiAnswer"}}], "tags": ["AiBff"]}}, "/ai/receive_chat_message": {"post": {"summary": "发送消息", "operationId": "AiBff_ReceiveChatMessage", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspReceiveChatMessage"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqReceiveChatMessage"}}], "tags": ["AiBff"]}}, "/ai/resend_chat_message": {"post": {"summary": "重发会话消息", "operationId": "AiBff_ResendChatMessage", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqResendChatMessage"}}], "tags": ["AiBff"]}}, "/ai/restart_export_task": {"post": {"summary": "重新开始导出任务", "operationId": "AiBff_RestartExportTask", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqRestartExportTask"}}], "tags": ["AiBff"]}}, "/ai/save_op_feedback": {"post": {"summary": "保存运营反馈", "operationId": "AiBff_SaveOpFeedback", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateFeedback"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqSaveOpFeedback"}}], "tags": ["AiBff"]}}, "/ai/save_user_feedback_by_question": {"post": {"summary": "保存用户反馈（问题维度）", "operationId": "AiBff_SaveUserFeedbackByQuestion", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateFeedback"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqSaveUserFeedbackByQuestion"}}], "tags": ["AiBff"]}}, "/ai/search_chat": {"post": {"summary": "搜索chat", "operationId": "AiBff_SearchChat", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspSearchChat"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqSearchChat"}}], "tags": ["AiBff"]}}, "/ai/search_chat_users": {"post": {"summary": "搜索AI对话的用户列表", "operationId": "AiBff_SearchChatUsers", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspSearchChatUsers"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqSearchChatUsers"}}], "tags": ["AiBff"]}}, "/ai/stop_reply": {"post": {"summary": "停止消息发送", "operationId": "AiBff_StopQuestionReply", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspStopQuestionReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqStopQuestionReply"}}], "tags": ["AiBff"]}}, "/ai/switch_chat_live_agent": {"post": {"summary": "切换人工坐席", "operationId": "AiBff_SwitchChatLiveAgent", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspSwitchChatLiveAgent"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqSwitchChatLiveAgent"}}], "tags": ["AiBff"]}}, "/ai/update_my_assistant": {"post": {"summary": "更新我的助手", "operationId": "AiBff_UpdateMyAssistant", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqUpdateMyAssistant"}}], "tags": ["AiBff"]}}, "/ai/update_my_assistant_team": {"post": {"summary": "更新我团队的助手", "operationId": "AiBff_UpdateMyTeamAssistant", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqUpdateMyAssistant"}}], "tags": ["AiBff"]}}, "/ai/update_object_custom_labels": {"post": {"summary": "更新对象的自定义标签", "operationId": "AiBff_UpdateObjectCustomLabels", "responses": {"200": {"description": "A successful response.", "schema": {"type": "object", "properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqUpdateObjectCustomLabels"}}], "tags": ["AiBff"]}}}, "definitions": {"AssistantKefuReplyCustom": {"type": "object", "properties": {"url": {"type": "string", "title": "url地址"}, "text": {"type": "string", "title": "显示文本"}}}, "AssistantKefuReplyReply": {"type": "object", "properties": {"reply_message": {"type": "string", "title": "回复消息"}, "img_url": {"type": "string", "title": "图片地址"}}}, "ChatLiveAgentInfoLiveAgentInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "nickname": {"type": "string"}}}, "EventChatHashMessageEventContributor": {"type": "object", "properties": {"type": {"$ref": "#/definitions/baseIdentityType"}, "text": {"type": "string"}, "id": {"type": "string"}, "level": {"$ref": "#/definitions/teamTeamLevel", "title": "共创等级"}, "is_published": {"type": "boolean", "title": "是否已发布"}, "full_name": {"type": "string", "title": "team full name"}}}, "EventChatHashMessageEventMessageDoc": {"type": "object", "properties": {"ugc_id": {"type": "string", "format": "uint64"}, "ugc_type": {"$ref": "#/definitions/baseDataType"}, "id": {"type": "string", "format": "uint64"}, "rag_filename": {"type": "string"}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/EventChatHashMessageEventContributor"}}, "data_type": {"type": "integer", "format": "int64"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "file_name": {"type": "string"}}}, "EventChatHashMessageEventMessageFilter": {"type": "object", "properties": {"field": {"type": "string"}, "value": {"type": "string"}, "tags": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/EventChatHashMessageEventMessageTag"}}}}, "EventChatHashMessageEventMessageTag": {"type": "object", "properties": {"taggable_type": {"type": "integer", "format": "int32"}, "id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "integer", "format": "int32"}}}, "EventChatHashMessageEventMessageUgc": {"type": "object", "properties": {"ugc_ids": {"type": "array", "items": {"type": "string"}}, "ugc_type": {"$ref": "#/definitions/baseDataType"}, "filter": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/EventChatHashMessageEventMessageFilter"}}, "cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/EventChatHashMessageEventMessageUgcCard"}}, "is_ugc_link": {"type": "boolean"}}}, "EventChatHashMessageEventMessageUgcCard": {"type": "object", "properties": {"name": {"type": "string"}, "logo_url": {"type": "string"}, "id": {"type": "string"}, "tags": {"type": "string"}}}, "FeedbackCommentFile": {"type": "object", "properties": {"file_path": {"type": "string", "title": "路径"}, "file_name": {"type": "string", "title": "文件名称"}}, "title": "文件"}, "RspAcceptFeedbackResult": {"type": "object", "properties": {"feedback_id": {"type": "string", "format": "uint64", "title": "反馈ID"}, "code": {"type": "integer", "format": "int32", "title": "错误码"}}}, "RspBatchUserAssistantLimitUserLimit": {"type": "object", "properties": {"token": {"type": "string"}, "empty_phone": {"type": "boolean", "title": "如果限制，需要检测手机号，true 空手机， false 不空；"}, "can_in": {"type": "boolean", "title": "是否可以进入 true 可， false 不可"}}}, "RspListSharedTeamSharedTeam": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}}}, "RspListSharedUserSharedUser": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}}}, "RspListeDocShareConfigSenderSharedUserTeam": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "is_selected": {"type": "boolean"}}}, "RspProxyChatHtmlUrlContent": {"type": "object", "properties": {"url": {"type": "string"}, "title": {"type": "string"}}}, "aiAiRecordType": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "title": "- 1: 用户咨询\n - 2: 助手回答文本消息\n - 3: 助手回答菜单消息\n - 4: 助手回答建议问题菜单消息\n - 5: 助手回答贡献知识的小程序\n - 6: 发送转人工菜单信息\n - 7: 助手回答图片\n - 8: 助手回答音频\n - 9: 助手回答视频\n - 10: 助手回答文件"}, "aiAskSuggestionMode": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4], "description": "- 1: 模式1：根据历史问答生成问题建议\n - 2: 模式2：根据历史问答生成，并仅显示知识库中有相关知识的问题建议\n - 3: 模式3：根据问题在知识库中已命中的知识，生成问题建议\n - 4: 模式4：根据问题在知识库中尚未命中但排名靠前的知识，生成问题建议", "title": "问题建议模式"}, "aiAssistantAllowlistConfig": {"type": "object", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "type": {"$ref": "#/definitions/aiAssistantAllowlistType", "title": "白名单类型"}, "phones": {"type": "array", "items": {"type": "string"}, "title": "手机列表"}}, "title": "助手白名单配置"}, "aiAssistantAllowlistType": {"type": "integer", "format": "int32", "enum": [1, 2], "description": "- 1: 手机号码\n - 2: 微信昵称", "title": "助手白名单类型"}, "aiAssistantAskSuggestionConfig": {"type": "object", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "prompt": {"type": "string", "title": "提示词"}, "count": {"type": "integer", "format": "int32", "title": "问题建议数量"}, "mode": {"$ref": "#/definitions/aiAskSuggestionMode", "title": "问题建议模式"}, "times": {"type": "integer", "format": "int64", "title": "问题建议倍数"}, "model": {"type": "string", "title": "模型"}}, "title": "问题建议配置"}, "aiAssistantChannel": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5, 6], "description": "- 1: 碳LIVE-微信\n - 2: 碳LIVE-Web\n - 3: 碳LIVE-应用\n - 4: 碳LIVE-WhatsApp\n - 5: 第三方机构-微信\n - 6: 碳LIVE-小程序", "title": "助手渠道"}, "aiAssistantChatOrSqlConfig": {"type": "object", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "model": {"type": "string", "title": "模型"}}, "title": "ChatOrSql配置"}, "aiAssistantChunkConfig": {"type": "object", "properties": {"min_char_count": {"type": "integer", "format": "int32", "title": "最小字符数"}, "min_char_lang": {"type": "string", "title": "最小字符语言"}, "max_char_count": {"type": "integer", "format": "int32", "title": "最大字符数"}, "max_char_lang": {"type": "string", "title": "最大字符语言"}, "overlap_count": {"type": "integer", "format": "int32", "title": "重合字符数"}, "overlap_lang": {"type": "string", "title": "重合字符语言"}}, "title": "分段配置"}, "aiAssistantChunks": {"type": "object", "properties": {"assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "助手ID"}, "chunks": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChunkItem"}, "title": "分段列表"}}, "title": "助手的分段信息"}, "aiAssistantConfig": {"type": "object", "properties": {"search_debug": {"type": "boolean", "title": "搜索测试"}, "visible_chain_config": {"$ref": "#/definitions/aiAssistantVisibleChainConfig", "title": "链路查询"}, "field_manage_config": {"$ref": "#/definitions/aiAssistantFieldManageConfig", "title": "参数管理权限"}, "name": {"type": "string", "title": "AI助手名"}, "name_en": {"type": "string", "title": "AI助手名（英文）"}, "channel": {"$ref": "#/definitions/aiAssistantChannel", "title": "渠道"}, "enabled": {"type": "boolean", "title": "是否启用"}, "admins": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/baseIdentity"}, "title": "助手管理员"}, "collection_lang": {"type": "string", "title": "知识库语言"}, "collection_name": {"type": "string", "title": "知识库名称（自动生成）"}, "prompt_prefix": {"type": "string", "title": "提示词"}, "model": {"type": "string", "title": "对话模型"}, "threshold": {"type": "number", "format": "float", "title": "对话阈值"}, "history_rounds": {"type": "integer", "format": "int32", "title": "对话轮数"}, "search_engine": {"type": "string", "title": "搜索引擎"}, "doc_top_n": {"type": "integer", "format": "int32", "title": "知识库Top_N"}, "search_top_n": {"type": "integer", "format": "int32", "title": "互联网Top_N"}, "chat_or_sql_config": {"$ref": "#/definitions/aiAssistantChatOrSqlConfig", "title": "ChatOrSql配置"}, "ask_suggestion_config": {"$ref": "#/definitions/aiAssistantAskSuggestionConfig", "title": "问题建议配置"}, "weixin_channel_config": {"$ref": "#/definitions/aiAssistantWeixinChannelConfig", "title": "微信渠道配置"}, "tanlive_web_channel_config": {"$ref": "#/definitions/aiAssistantTanliveWebChannelConfig", "title": "碳LIVE Web渠道配置"}, "tanlive_app_channel_config": {"$ref": "#/definitions/aiAssistantTanliveAppChannelConfig", "title": "Web渠道配置"}, "whatsapp_channel_config": {"$ref": "#/definitions/aiAssistantWhatsappChannelConfig", "title": "WhatsApp渠道配置"}, "miniprogram_channel_config": {"$ref": "#/definitions/aiAssistantMiniprogramChannelConfig", "title": "小程序渠道配置"}, "use_region_code": {"type": "string", "title": "使用地区（空不限制）"}, "close_search": {"type": "boolean", "title": "是否关闭搜索增强"}, "text_weight": {"type": "number", "format": "float", "title": "关键词搜索权重"}, "miss_reply": {"type": "string", "title": "未命中知识库自动回复"}, "brief_intro": {"type": "string", "title": "一句话介绍"}, "detail_intro": {"type": "string", "title": "助手介绍"}, "chunk_config": {"$ref": "#/definitions/aiAssistantChunkConfig", "title": "分段配置"}, "show_think": {"type": "boolean", "title": "是否展示思考过程"}, "text_recall_top_n": {"type": "integer", "format": "int32", "title": "关键词召回条数"}, "temperature": {"type": "number", "format": "float", "title": "温度"}, "question_type_config": {"$ref": "#/definitions/aiAssistantQuestionTypeConfig", "title": "问题分类配置"}, "show_in_list": {"type": "boolean", "title": "是否在助手列表展示"}, "allowlist_config": {"$ref": "#/definitions/aiAssistantAllowlistConfig", "title": "白名单配置"}, "clean_chunks": {"type": "boolean", "title": "自动过滤"}, "text_recall_query": {"$ref": "#/definitions/aiTextRecallQuery", "title": "QA关键词召回目标"}, "text_recall_pattern": {"$ref": "#/definitions/aiTextRecallPattern", "title": "关键词召回模式"}, "text_recall_slop": {"type": "integer", "format": "int32", "title": "允许平移距离"}, "user_label_config": {"$ref": "#/definitions/aiAssistantUserLabelConfig", "title": "助手用户打标配置"}}, "title": "助手配置"}, "aiAssistantFieldManageConfig": {"type": "object", "properties": {"enabled": {"type": "boolean", "title": "是否启用（该字段暂不使用）"}, "readable": {"type": "array", "items": {"type": "string"}, "title": "可读字段（为空全部可读）"}, "writable": {"type": "array", "items": {"type": "string"}, "title": "可写字段（为空全部不可写）"}}, "title": "字段管理配置"}, "aiAssistantGraphParseConfig": {"type": "object", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "model": {"type": "string", "title": "模型"}}, "title": "图谱解析配置"}, "aiAssistantInteractiveCode": {"type": "object", "properties": {"lang": {"type": "string", "title": "语言"}, "interactive_code": {"$ref": "#/definitions/aiInteractiveCode", "title": "互动暗号"}, "content": {"type": "string", "title": "内容"}, "show_in_welcome": {"type": "boolean", "title": "是否在欢迎语里显示"}}, "title": "互动暗号详情"}, "aiAssistantInteractiveCodeConfig": {"type": "object", "properties": {"codes": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiAssistantInteractiveCode"}, "title": "暗号配置"}, "send_interactive_code": {"type": "boolean", "title": "开启回复发送暗号"}}, "title": "互动暗号配置"}, "aiAssistantKefuConfig": {"type": "object", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "staffs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiAssistantKefuStaff"}, "title": "员工信息"}, "after_remind_message": {"type": "string", "title": "转人工后的提示语"}, "before_remind_enabled": {"type": "boolean", "title": "是否启用转人工前的提示语"}, "before_remind_message": {"type": "string", "title": "转人工前的提示语"}, "reply": {"$ref": "#/definitions/aiAssistantKefuReply", "title": "客服自动回复配置"}}, "title": "人工客服配置"}, "aiAssistantKefuReply": {"type": "object", "properties": {"mainland_zh": {"$ref": "#/definitions/AssistantKefuReplyReply", "title": "大陆中文"}, "mainland_en": {"$ref": "#/definitions/AssistantKefuReplyReply", "title": "大陆英文"}, "non_mainland_zh": {"$ref": "#/definitions/AssistantKefuReplyReply", "title": "非中国大陆中文"}, "non_mainland_en": {"$ref": "#/definitions/AssistantKefuReplyReply", "title": "非中国大陆英文"}, "enable_custom": {"type": "boolean", "title": "是否启用自定义配置"}, "custom": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/AssistantKefuReplyCustom"}, "title": "自定义配置列表"}}, "title": "助手客服自动回复"}, "aiAssistantKefuStaff": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "主键"}, "username": {"type": "string", "title": "账号"}, "nickname": {"type": "string", "title": "昵称"}}, "title": "人工客服信息"}, "aiAssistantMiniprogramChannelConfig": {"type": "object", "properties": {"nickname": {"type": "string", "title": "助手昵称"}, "nickname_en": {"type": "string", "title": "助手昵称（英文）"}, "avatar_url": {"type": "string", "title": "头像（默认头像留空）"}, "miniprogram_config": {"$ref": "#/definitions/aiAssistantMiniprogramConfig", "title": "小程序配置"}, "welcome_message_config": {"$ref": "#/definitions/aiAssistantWelcomeMessageConfig", "title": "欢迎语配置"}, "preset_question_config": {"$ref": "#/definitions/aiAssistantPresetQuestionConfig", "title": "预设问题配置"}, "rating_scale_reply_config": {"$ref": "#/definitions/aiAssistantRatingScaleReplyConfig", "title": "满意度回复配置"}, "interactive_code_config": {"$ref": "#/definitions/aiAssistantInteractiveCodeConfig", "title": "互动暗号配置"}, "feedback_enabled": {"type": "boolean", "title": "用户教学反馈"}, "graph_parse_config": {"$ref": "#/definitions/aiAssistantGraphParseConfig", "title": "图谱解析配置"}, "assistant_lang": {"type": "string", "title": "助手语言（已废弃，请使用system_languages）"}, "kefu_config": {"$ref": "#/definitions/aiAssistantKefuConfig", "title": "人工客服"}, "system_languages": {"type": "array", "items": {"type": "string"}, "title": "系统语言"}}, "title": "助手小程序渠道配置"}, "aiAssistantMiniprogramConfig": {"type": "object", "properties": {"share_title": {"type": "string", "title": "分享标题"}, "share_image": {"type": "string", "title": "分享图片"}, "url": {"type": "string", "title": "二维码URL"}, "schema": {"type": "string", "title": "小程序schema"}}, "title": "小程序配置"}, "aiAssistantPresetQuestion": {"type": "object", "properties": {"lang": {"type": "string", "title": "语言"}, "content": {"type": "string", "title": "内容"}}, "title": "预设问题详情"}, "aiAssistantPresetQuestionConfig": {"type": "object", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "questions": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiAssistantPresetQuestion"}, "title": "配置列表"}}, "title": "预设问题配置"}, "aiAssistantQuestionTypeConfig": {"type": "object", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "chat_model": {"type": "string", "title": "聊天问题的模型"}, "simple_model": {"type": "string", "title": "简单问题的模型"}, "complex_model": {"type": "string", "title": "复杂问题的模型"}, "prompt": {"type": "string", "title": "简单、复杂问题提示词"}}, "title": "问题类型配置"}, "aiAssistantRatingScaleReply": {"type": "object", "properties": {"lang": {"type": "string", "title": "语言"}, "rating_scale": {"$ref": "#/definitions/aiRatingScale", "title": "评价等级"}, "content": {"type": "string", "title": "内容"}}, "title": "满意度回复详情"}, "aiAssistantRatingScaleReplyConfig": {"type": "object", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "replies": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiAssistantRatingScaleReply"}, "title": "满意度回复配置"}}, "title": "满意度回复配置"}, "aiAssistantTanliveAppChannelConfig": {"type": "object", "properties": {"nickname": {"type": "string", "title": "助手昵称"}, "nickname_en": {"type": "string", "title": "助手昵称（英文）"}, "avatar_url": {"type": "string", "title": "头像（默认头像留空）"}, "welcome_message_config": {"$ref": "#/definitions/aiAssistantWelcomeMessageConfig", "title": "欢迎语配置"}, "preset_question_config": {"$ref": "#/definitions/aiAssistantPresetQuestionConfig", "title": "预设问题配置"}, "rating_scale_reply_config": {"$ref": "#/definitions/aiAssistantRatingScaleReplyConfig", "title": "满意度回复配置"}, "interactive_code_config": {"$ref": "#/definitions/aiAssistantInteractiveCodeConfig", "title": "互动暗号配置"}, "feedback_enabled": {"type": "boolean", "title": "用户教学反馈"}, "graph_parse_config": {"$ref": "#/definitions/aiAssistantGraphParseConfig", "title": "图谱解析配置"}, "assistant_lang": {"type": "string", "title": "助手语言（已废弃，请使用system_languages）"}, "kefu_config": {"$ref": "#/definitions/aiAssistantKefuConfig", "title": "人工客服"}, "system_languages": {"type": "array", "items": {"type": "string"}, "title": "系统语言"}, "app_id": {"type": "string", "title": "应用ID"}}, "title": "碳LIVE应用助手渠道配置"}, "aiAssistantTanliveWebChannelConfig": {"type": "object", "properties": {"nickname": {"type": "string", "title": "助手昵称"}, "nickname_en": {"type": "string", "title": "助手昵称（英文）"}, "avatar_url": {"type": "string", "title": "头像（默认头像留空）"}, "website_config": {"$ref": "#/definitions/aiAssistantWebsiteConfig", "title": "网站配置"}, "welcome_message_config": {"$ref": "#/definitions/aiAssistantWelcomeMessageConfig", "title": "欢迎语配置"}, "preset_question_config": {"$ref": "#/definitions/aiAssistantPresetQuestionConfig", "title": "预设问题配置"}, "rating_scale_reply_config": {"$ref": "#/definitions/aiAssistantRatingScaleReplyConfig", "title": "满意度回复配置"}, "interactive_code_config": {"$ref": "#/definitions/aiAssistantInteractiveCodeConfig", "title": "互动暗号配置"}, "feedback_enabled": {"type": "boolean", "title": "用户教学反馈"}, "graph_parse_config": {"$ref": "#/definitions/aiAssistantGraphParseConfig", "title": "图谱解析配置"}, "assistant_lang": {"type": "string", "title": "助手语言（已废弃，请使用system_languages）"}, "switch_assistant_id": {"type": "string", "format": "uint64", "title": "切换助手ID"}, "kefu_config": {"$ref": "#/definitions/aiAssistantKefuConfig", "title": "人工客服"}, "system_languages": {"type": "array", "items": {"type": "string"}, "title": "系统语言"}}, "title": "碳LIVE Web助手渠道配置"}, "aiAssistantUserLabelConfig": {"type": "object", "properties": {"tag_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "绑定标签id"}, "tag_names": {"type": "array", "items": {"type": "string"}, "title": "绑定标签的名称"}}, "title": "助手用户打标配置"}, "aiAssistantV2": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "主键"}, "create_by": {"$ref": "#/definitions/baseIdentity", "title": "创建人"}, "update_by": {"$ref": "#/definitions/baseIdentity", "title": "更新人"}, "create_date": {"type": "string", "format": "date-time", "title": "创建时间"}, "update_date": {"type": "string", "format": "date-time", "title": "更新时间"}, "config": {"$ref": "#/definitions/aiAssistantConfig", "title": "配置详情"}, "is_draft": {"type": "boolean", "title": "是否为草稿"}, "batch_no": {"type": "string", "title": "批次号"}}, "title": "助手信息"}, "aiAssistantVisibleChainConfig": {"type": "object", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "visible": {"type": "array", "items": {"type": "string"}, "title": "可见字段"}}, "title": "链路查询"}, "aiAssistantWebsiteConfig": {"type": "object", "properties": {"route_path": {"type": "string", "title": "路由路径"}, "title": {"type": "string", "title": "标题"}}, "title": "网站配置"}, "aiAssistantWeixinChannelConfig": {"type": "object", "properties": {"nickname": {"type": "string", "title": "助手昵称"}, "avatar_url": {"type": "string", "title": "头像（默认头像留空）"}, "system_lang": {"type": "string", "title": "系统语言（已废弃，请使用system_languages）"}, "weixin_develop_config": {"$ref": "#/definitions/aiAssistantWeixinDevelopConfig", "title": "微信开发配置"}, "welcome_message_config": {"$ref": "#/definitions/aiAssistantWelcomeMessageConfig", "title": "欢迎语配置"}, "preset_question_config": {"$ref": "#/definitions/aiAssistantPresetQuestionConfig", "title": "预设问题配置"}, "kefu_config": {"$ref": "#/definitions/aiAssistantKefuConfig", "title": "人工客服配置"}, "rating_scale_reply_config": {"$ref": "#/definitions/aiAssistantRatingScaleReplyConfig", "title": "满意度回复配置"}, "interactive_code_config": {"$ref": "#/definitions/aiAssistantInteractiveCodeConfig", "title": "互动暗号配置"}, "chat_idle_duration": {"type": "integer", "format": "int32", "title": "会话闲置超时时间（分钟）"}, "system_languages": {"type": "array", "items": {"type": "string"}, "title": "系统语言"}}, "title": "微信渠道配置"}, "aiAssistantWeixinDevelopConfig": {"type": "object", "properties": {"corp_id": {"type": "string", "title": "企业ID"}, "open_kfid": {"type": "string", "title": "客服账号ID"}, "kf_url": {"type": "string", "title": "客服URL"}}, "title": "微信开发配置"}, "aiAssistantWelcomeMessage": {"type": "object", "properties": {"lang": {"type": "string", "title": "语言"}, "content": {"type": "string", "title": "内容"}}, "title": "欢迎语详情"}, "aiAssistantWelcomeMessageConfig": {"type": "object", "properties": {"messages": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiAssistantWelcomeMessage"}, "title": "配置列表"}}, "title": "欢迎语配置"}, "aiAssistantWhatsappChannelConfig": {"type": "object", "properties": {"system_lang": {"type": "string", "title": "系统语言（已废弃，请使用system_languages）"}, "whatsapp_develop_config": {"$ref": "#/definitions/aiAssistantWhatsappDevelopConfig", "title": "WhatsApp开发配置"}, "welcome_message_config": {"$ref": "#/definitions/aiAssistantWelcomeMessageConfig", "title": "欢迎语配置"}, "preset_question_config": {"$ref": "#/definitions/aiAssistantPresetQuestionConfig", "title": "预设问题配置"}, "rating_scale_reply_config": {"$ref": "#/definitions/aiAssistantRatingScaleReplyConfig", "title": "满意度回复配置"}, "chat_idle_duration": {"type": "integer", "format": "int32", "title": "会话闲置超时时间（分钟）"}, "nickname": {"type": "string", "title": "昵称"}, "avatar_url": {"type": "string", "title": "头像"}, "system_languages": {"type": "array", "items": {"type": "string"}, "title": "系统语言"}}, "title": "Whatsapp助手渠道配置"}, "aiAssistantWhatsappDevelopConfig": {"type": "object", "properties": {"business_number": {"type": "string", "title": "Business number"}}, "title": "WhatsApp开发配置"}, "aiAutoChunkPara": {"type": "object", "properties": {"assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "助手ID"}, "chunk_config": {"$ref": "#/definitions/aiAssistantChunkConfig", "title": "分段配置"}, "dry_run": {"type": "boolean", "title": "仅预览"}}, "title": "自动分段参数"}, "aiChatCurrentState": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "title": "- 1: 当前会话未结束\n - 2: 当前会话已经被其他会话替代-已结束\n - 3: 当前会话已经超过可聊天规定时限-已结束"}, "aiChatInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "title": {"type": "string"}, "create_by": {"$ref": "#/definitions/iamUserInfo"}, "update_date": {"type": "string", "format": "date-time"}, "create_date": {"type": "string", "format": "date-time"}, "chat_type": {"$ref": "#/definitions/aiChatType"}, "assistant_id": {"type": "string", "format": "uint64"}, "assistant_name": {"type": "string"}, "chat_state": {"$ref": "#/definitions/aiChatCurrentState"}, "support_type": {"$ref": "#/definitions/aiChatSupportType", "title": "当前服务状态"}, "question_cnt": {"type": "integer", "format": "int64", "title": "对话中问题数量"}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}, "title": "自定义标签kv对"}, "region_code": {"type": "string"}, "rating_scale": {"$ref": "#/definitions/aiRatingScale"}, "doc_hits": {"type": "number", "format": "float"}, "avg_duration": {"type": "number", "format": "float"}, "reject_job_result": {"type": "integer", "format": "int64"}, "is_manual": {"type": "integer", "format": "int32", "title": "是否转过人工服务"}}, "title": "对话管理端信息"}, "aiChatLiveAgentInfo": {"type": "object", "properties": {"live_agents": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/ChatLiveAgentInfoLiveAgentInfo"}, "title": "人工客服列表"}, "current_live_agent": {"$ref": "#/definitions/ChatLiveAgentInfoLiveAgentInfo", "title": "当前正在会话中的客服"}, "chat_id": {"type": "string", "format": "uint64", "title": "会话ID"}}}, "aiChatMessage": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "chat_id": {"type": "string", "format": "uint64"}, "text": {"type": "string"}, "create_date": {"type": "string", "format": "date-time"}, "type": {"$ref": "#/definitions/aiChatMessageType"}, "rating_scale": {"$ref": "#/definitions/aiRatingScale"}, "link": {"type": "string"}, "question_id": {"type": "string", "format": "uint64"}, "sql_query": {"type": "string"}, "ugcs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiMessageUgc"}}, "create_by": {"type": "string", "format": "uint64"}, "reject_reason": {"type": "string"}, "state": {"$ref": "#/definitions/aiChatMessageState"}, "doc_names": {"type": "array", "items": {"type": "string"}}, "assistant_id": {"type": "string", "format": "uint64"}, "lang": {"type": "string"}, "collection_snapshot": {"$ref": "#/definitions/aiMessageCollectionSnapshot"}, "ask_type": {"$ref": "#/definitions/aiQuestionAskType"}, "start_time": {"type": "string", "format": "date-time"}, "end_time": {"type": "string", "format": "date-time"}, "suggest_question": {"type": "array", "items": {"type": "string"}}, "final_query": {"type": "string"}, "live_agent_name": {"type": "string"}, "prompt_prefix": {"type": "string"}, "image_url": {"type": "array", "items": {"type": "string"}}, "show_type": {"type": "integer", "format": "int32"}, "final_search_query": {"type": "string"}, "doc_match_pattern": {"$ref": "#/definitions/aiDocMatchPattern"}, "suggestion_mode": {"$ref": "#/definitions/aiAskSuggestionMode"}, "logs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageLog"}}, "ref_file_names": {"type": "array", "items": {"type": "string"}}, "think": {"type": "string", "title": "深度思考"}, "publish_hash_id": {"type": "string", "title": "推送消息hash id"}, "history_ignore_id": {"type": "string", "format": "uint64"}, "wait_answer": {"type": "boolean"}, "think_duration": {"type": "integer", "format": "int32"}, "answer_index": {"type": "integer", "format": "int32", "title": "多任务索引"}, "prompt_type": {"type": "string", "title": "问题类型"}, "feedback_id": {"type": "string", "format": "uint64", "title": "教学反馈ID"}, "has_user_feedback": {"type": "boolean", "title": "是否有用户反馈"}, "last_operation_type": {"$ref": "#/definitions/aiChatOperationType", "title": "最后一次操作"}, "answer_draft_id": {"type": "string", "format": "uint64", "title": "answer草稿id"}, "task": {"$ref": "#/definitions/aiChatMessageTask", "title": "当前所执行的任务"}, "is_file_ready": {"type": "boolean", "title": "文件解析成功"}, "files": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageFile"}, "title": "问题附件"}, "last_operator": {"$ref": "#/definitions/aiChatMessageOperator", "title": "最后一次操作"}, "doc_snapshot": {"$ref": "#/definitions/aiMessageDocSnapshot", "title": "doc 快照"}}}, "aiChatMessageContentFilterItem": {"type": "object", "properties": {"field": {"type": "string"}, "value": {"type": "string"}, "tags": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiMessageTag"}}}}, "aiChatMessageDoc": {"type": "object", "properties": {"ugc_id": {"type": "string", "format": "uint64"}, "ugc_type": {"$ref": "#/definitions/baseDataType"}, "id": {"type": "string", "format": "uint64"}, "rag_filename": {"type": "string"}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "data_type": {"type": "integer", "format": "int64"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "file_name": {"type": "string"}, "index_text": {"type": "string"}, "text": {"type": "string"}, "doc_match_pattern": {"$ref": "#/definitions/aiDocMatchPattern"}, "url": {"type": "string"}, "update_by": {"$ref": "#/definitions/aiOperator"}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}}}, "aiChatMessageFile": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "文件ID"}, "url": {"type": "string", "title": "文件URL"}, "state": {"$ref": "#/definitions/aiChatMessageFileState"}, "parsed_url": {"type": "string", "title": "解析后的URL"}}, "title": "文件信息"}, "aiChatMessageFileState": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "title": "- 1: 文件解析中\n - 2: 文件解析成功\n - 3: 文件解析失败"}, "aiChatMessageLog": {"type": "object", "properties": {"message_id": {"type": "string", "format": "uint64"}, "sql_query": {"type": "string"}, "enhancement": {"type": "string"}, "gpt": {"type": "string"}, "ref": {"type": "string"}, "code": {"type": "string", "format": "uint64"}, "create_date": {"type": "string", "format": "date-time"}, "start_time": {"type": "string", "format": "date-time"}, "end_time": {"type": "string", "format": "date-time"}, "config_snapshot": {"type": "string"}, "type": {"$ref": "#/definitions/aiChatMessageType"}, "request_text": {"type": "string"}, "fetch_resp_time": {"type": "string", "format": "date-time"}}}, "aiChatMessageOperator": {"type": "object", "properties": {"operation_type": {"$ref": "#/definitions/aiChatOperationType"}, "stop_text": {"type": "string"}, "stop_think": {"type": "string"}, "stop_chunk_state": {"type": "integer", "format": "int32"}, "operation_params": {"type": "string"}, "hash_id": {"type": "string"}}}, "aiChatMessageState": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13], "title": "- 1: 消息未发送标识\n - 2: 消息已经发送标识\n - 3: 默认消息已经发送标识\n - 4: 努力思考\n - 5: 整理答案\n - 6: 停止回答\n - 7: 回答推流中\n - 8: 推流全部完成\n - 9: 思考过程推流\n - 10: 切片推流完成\n - 12: 参考资料消息\n - 13: 建议问题"}, "aiChatMessageTask": {"type": "object", "properties": {"task_id": {"type": "string", "format": "uint64"}, "pipeline_id": {"type": "string", "format": "uint64"}, "state": {"$ref": "#/definitions/aiPipelineTaskState"}, "order": {"type": "integer", "format": "int32"}}}, "aiChatMessageType": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "title": "- 1: 用户消息\n - 2: 数据库查询\n - 3: collection查询\n - 4: 搜索引擎查询\n - 5: 系统错误\n - 6: 敏感信息错误\n - 7: 超时错误\n - 8: 取消回复\n - 9: 预定会话聊天回复\n - 10: 人工客服消息\n - 11: 多模态消息\n - 12: 清空上下文\n - 13: 建议问题\n - 14: 转人工二维码\n - 15: 回答草稿"}, "aiChatModelOption": {"type": "object", "properties": {"model": {"type": "string", "title": "模型"}, "disable_in_console": {"type": "boolean", "title": "是否在用户后台禁用"}, "support_think": {"type": "boolean", "title": "是否支持思考"}, "only_non_stream": {"type": "boolean", "title": "仅支持非流式"}}, "title": "聊天模型选项"}, "aiChatOperationType": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4], "description": "- 1: 正常回答\n - 2: 停止回答\n - 3: 停止思考\n - 4: 重新回答", "title": "会话操作类型"}, "aiChatSendRecordInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "记录id"}, "message_id": {"type": "string", "format": "uint64", "title": "消息id"}, "message_type": {"$ref": "#/definitions/aiChatMessageType", "title": "消息类型"}, "content": {"type": "string", "title": "记录内容"}, "send_date": {"type": "string", "format": "date-time", "title": "发送时间"}, "record_type": {"$ref": "#/definitions/aiAiRecordType", "title": "类型 1 用户询问 2 助手回答-text 3 助手回答-menu 4 助手回答建议问题菜单消息"}, "message_rating_scale": {"$ref": "#/definitions/aiRatingScale", "title": "记录对应的消息的评价信息"}, "show_type": {"type": "integer", "format": "int32", "title": "显示状态"}, "reject_reason": {"type": "string"}, "suggest_questions": {"type": "array", "items": {"type": "string"}}, "image_url": {"type": "array", "items": {"type": "string"}}, "doc_match_pattern": {"$ref": "#/definitions/aiDocMatchPattern"}, "suggestion_mode": {"$ref": "#/definitions/aiAskSuggestionMode"}}}, "aiChatSupportType": {"type": "integer", "format": "int32", "enum": [1, 2], "title": "- 1: ai聊天\n - 2: 客服聊天"}, "aiChatType": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4], "description": "- 1: web\n - 2: 微信公众号\n - 3: whatsapp\n - 4: 小程序", "title": "后续会改为使用助手表的channel，如果是新功能就不要使用这个枚举了"}, "aiChunkItem": {"type": "object", "properties": {"start": {"type": "integer", "format": "int64", "title": "起始索引位置"}, "len": {"type": "integer", "format": "int64", "title": "内容长度"}, "content": {"type": "string", "title": "分段内容"}}, "title": "文档分段信息"}, "aiCollection": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}}}, "aiCollectionQA": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "question": {"type": "string", "title": "问题"}, "answer": {"type": "string", "title": "答案"}, "assistants": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveaiAssistant"}, "title": "用户端"}, "states": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocAssistantState"}}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}, "title": "贡献者"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}, "title": "参考资料"}, "hit_count": {"type": "integer", "format": "int64", "title": "命中次数"}, "create_by": {"$ref": "#/definitions/aiDocOperator"}, "update_by": {"$ref": "#/definitions/aiDocOperator"}, "update_date": {"type": "string", "format": "date-time"}, "create_date": {"type": "string", "format": "date-time"}, "embedding_state": {"$ref": "#/definitions/aiDocEmbeddingState"}, "shared_states": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocAssistantState"}, "title": "分享的用户端"}, "shared_teams": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocShareTeamReceiver"}, "title": "分享的团队"}, "shared_users": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocShareUserReceiver"}, "title": "分享的用户"}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}, "state": {"$ref": "#/definitions/tanliveaiDocState", "title": "状态(不包含助手对应的状态)"}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "match_patterns": {"type": "array", "items": {"$ref": "#/definitions/aiDocMatchPattern"}, "title": "匹配模式"}, "question_oversize": {"type": "boolean"}, "has_repeated": {"type": "boolean"}, "received_share": {"type": "boolean", "title": "是否收到分享"}}}, "aiCollectionTextFile": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "file_name": {"type": "string", "title": "文件/文本名称"}, "text": {"type": "string", "title": "文件/文本内容"}, "assistants": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveaiAssistant"}, "title": "用户端"}, "state": {"$ref": "#/definitions/tanliveaiDocState", "title": "状态(不包含助手对应的状态)"}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}, "title": "贡献者"}, "url": {"type": "string", "title": "文件url"}, "hit_count": {"type": "integer", "format": "int64", "title": "命中次数"}, "ugc_type": {"$ref": "#/definitions/baseDataType", "title": "ugc类型"}, "ugc_id": {"type": "string", "format": "uint64", "title": "ugc的id"}, "create_by": {"$ref": "#/definitions/aiDocOperator"}, "update_by": {"$ref": "#/definitions/aiDocOperator"}, "update_date": {"type": "string", "format": "date-time"}, "create_date": {"type": "string", "format": "date-time"}, "embedding_state": {"$ref": "#/definitions/aiDocEmbeddingState"}, "parse_progress": {"type": "number", "format": "float", "title": "解析进度，0.5 = 50%"}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}, "states": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocAssistantState"}}, "shared_states": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocAssistantState"}}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}, "title": "参考资料"}, "download_as_ref": {"$ref": "#/definitions/aiDocFileDownloadAsRef", "title": "是否可以作为参考资料下载"}, "parse_mode": {"$ref": "#/definitions/aiDocParseMode"}, "has_over_sized_tables": {"type": "boolean", "title": "知识提示\n是否有超长标题表格"}, "has_repeated": {"type": "boolean", "title": "是否内容重复（租户内）"}, "data_source_state": {"type": "integer", "format": "int64", "title": "外部数据源信息"}, "shared_teams": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocShareTeamReceiver"}, "title": "分享的团队"}, "shared_users": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocShareUserReceiver"}, "title": "分享的用户"}, "received_share": {"type": "boolean", "title": "是否收到分享"}}}, "aiContributor": {"type": "object", "properties": {"type": {"$ref": "#/definitions/baseIdentityType"}, "text": {"type": "string", "title": "自定义纯文本 或者 个人/团队/运营端账户名称"}, "id": {"type": "string", "format": "uint64", "title": "账户id\n个人账户: 个人账户id\n团队账户: 团队账户id\n运营端: 运营端账户id"}, "level": {"$ref": "#/definitions/teamTeamLevel", "title": "共创等级"}, "is_published": {"type": "boolean", "title": "是否已发布"}, "full_name": {"type": "string", "title": "team full name"}, "assistant_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "贡献者所在的助手"}}}, "aiCustomLabel": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "标签id"}, "type": {"$ref": "#/definitions/aiCustomLabelType", "title": "标签类型"}, "key": {"type": "string", "title": "标签key"}, "value": {"$ref": "#/definitions/aiLabelValue"}, "create_by": {"type": "string", "format": "uint64", "title": "创建人"}, "update_by": {"type": "string", "format": "uint64", "title": "更新人"}, "next_label_name": {"type": "string"}, "next_label_id": {"type": "string", "format": "uint64"}}, "title": "AI对话的标签"}, "aiCustomLabelObjectType": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5], "description": "- 1: AI 对话\n - 2: 知识库文档文本与文件\n - 3: 知识库文档QA\n - 4: 腾讯云文档导入的文本文件\n - 5: SQL数据导入的文本文件", "title": "标签对象类型"}, "aiCustomLabelType": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], "description": "- 1: 任意纯文本\n - 2: 字符串枚举(单选)\n - 3: 字符串枚举(多选)\n - 4: int\n - 5: uint\n - 6: float\n - 7: 年\n - 8: 年月\n - 9: 年月日\n - 10: 日期时间(年月日和时间)\n - 11: 时间", "title": "标签类型"}, "aiDocAssistantState": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64"}, "state": {"$ref": "#/definitions/tanliveaiDocState"}, "is_shared": {"type": "integer", "format": "int64", "title": "1否 2是"}}}, "aiDocChunkTask": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "任务ID"}, "state": {"$ref": "#/definitions/aiDocChunkTaskState", "title": "任务状态"}}, "title": "文段分段任务"}, "aiDocChunkTaskState": {"type": "integer", "format": "int32", "enum": [1, 2], "description": "- 1: 执行中\n - 2: 已完成", "title": "文档分段任务状态"}, "aiDocDataSource": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4], "description": "- 1: tanlive ugc数据\n - 2: tanlive 知识库数据\n - 3: 腾讯云文档\n - 4: SQL 数据", "title": "知识的数据来源"}, "aiDocEmbeddingState": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "title": "- 1: 同步中\n - 2: 同步完成\n - 3: 删除中"}, "aiDocFileDownloadAsRef": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4], "description": "- 1: 可下载\n - 2: 仅显示文件名\n - 3: 直接发送\n - 4: 隐藏", "title": "文本文件是否能作为参考资料下载"}, "aiDocMatchPattern": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5], "description": "- 1: 大模型召回\n - 2: 完全匹配\n - 3: 忽略标点匹配\n - 4: 未命中\n - 5: 包含关键字", "title": "匹配模式"}, "aiDocOperator": {"type": "object", "properties": {"type": {"$ref": "#/definitions/baseIdentityType"}, "id": {"type": "string", "format": "uint64", "title": "用户账户: 用户id\n团队账户: 用户id\n运营端账户: 运营端用户id"}, "username": {"type": "string", "title": "用户名称"}, "team_name": {"type": "string", "title": "用户所属团队名称"}, "user_id": {"type": "string", "format": "uint64", "title": "用户id，只有为团队用户时，才需要"}}}, "aiDocParseMode": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4], "description": "- 1: 智能解析\n - 2: 文件解析\n - 3: 图像解析\n - 4: 表格解析", "title": "文档解析模式"}, "aiDocReference": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "引用doc的参考文献"}, "name": {"type": "string"}, "url": {"type": "string"}, "text": {"type": "string", "title": "纯文本参考文献"}, "show_type": {"$ref": "#/definitions/aiDocFileDownloadAsRef", "title": "仅用于控制对话展示"}}, "title": "参考文献"}, "aiDocShareAcceptState": {"type": "integer", "format": "int32", "enum": [1, 2], "description": "- 1: 接收分享\n - 2: 不接收分享", "title": "是否接收知识库分享"}, "aiDocShareState": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "description": "- 1: 启用\n - 2: 禁用\n - 3: 不接收", "title": "知识库分享状态"}, "aiDocShareTeamReceiver": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "name_en": {"type": "string"}}}, "aiDocShareUserReceiver": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}}}, "aiDocSharedState": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "description": "- 1: 启用\n - 2: 禁用\n - 3: 收到分享", "title": "知识库文档收到分享状态"}, "aiDocType": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "description": "- 1: QA\n - 2: 文本\n - 3: 文件", "title": "文档类型"}, "aiEmbeddingModelCount": {"type": "object", "properties": {"collection_lang": {"type": "string", "title": "向量化模型"}, "count": {"type": "integer", "format": "int64", "title": "数量"}, "embedding_model_name": {"type": "string", "title": "向量化模型名称"}}, "title": "向量化模型计数"}, "aiEmbeddingModelOption": {"type": "object", "properties": {"model": {"type": "string", "title": "模型名称"}, "tech_seg_min_tokens": {"type": "integer", "format": "int32", "title": "技术最小分段长度（token）"}, "tech_seg_max_tokens": {"type": "integer", "format": "int32", "title": "技术最大分段长度（token）"}, "tech_overlap_min_tokens": {"type": "integer", "format": "int32", "title": "技术overlap最小长度（token）"}, "tech_overlap_max_tokens": {"type": "integer", "format": "int32", "title": "技术overlap最大长度（token）"}, "user_seg_min_tokens": {"type": "integer", "format": "int32", "title": "用户最小分段长度默认值（token）"}, "user_seg_max_tokens": {"type": "integer", "format": "int32", "title": "用户最大分段长度默认值（token）"}, "user_overlap_tokens": {"type": "integer", "format": "int32", "title": "用户overlap长度默认值（token）"}, "zh_min_tokens_per_char": {"type": "number", "format": "float", "title": "1个中文字符约对应多少个token（下限）"}, "zh_max_tokens_per_char": {"type": "number", "format": "float", "title": "1个中文字符约对应多少个token（上限）"}, "en_min_tokens_per_char": {"type": "number", "format": "float", "title": "1个英文字符约对应多少个token（下限）"}, "en_max_tokens_per_char": {"type": "number", "format": "float", "title": "1个英文字符约对应多少个token（上限）"}, "embedding_vector_length": {"type": "integer", "format": "int32", "title": "embedding向量长度"}, "recommended": {"type": "boolean", "title": "是否推荐"}, "name": {"type": "string", "title": "名称"}, "zh_default": {"$ref": "#/definitions/aiAssistantChunkConfig", "title": "中文默认配置"}, "en_default": {"$ref": "#/definitions/aiAssistantChunkConfig", "title": "英文默认配置"}}, "title": "向量化模型选项"}, "aiEventChatHashMessage": {"type": "object", "properties": {"id": {"type": "string"}, "chat_id": {"type": "string"}, "text": {"type": "string"}, "create_date": {"type": "string", "format": "date-time"}, "type": {"$ref": "#/definitions/aiChatMessageType"}, "rating_scale": {"$ref": "#/definitions/aiRatingScale"}, "docs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/EventChatHashMessageEventMessageDoc"}}, "link": {"type": "string"}, "question_id": {"type": "string"}, "ugcs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/EventChatHashMessageEventMessageUgc"}}, "state": {"type": "integer", "format": "int32"}, "lang": {"type": "string"}, "assistant_id": {"type": "string"}, "reject_reason": {"type": "string"}, "process_time": {"$ref": "#/definitions/baseTimeRange"}, "custom_question_id": {"type": "string", "format": "uint64"}, "sql_query": {"type": "string"}, "doc_final_query": {"type": "string"}, "suggest_count": {"type": "integer", "format": "int32"}, "suggest_questions": {"type": "array", "items": {"type": "string"}}, "show_type": {"type": "integer", "format": "int32"}, "doc_match_pattern": {"$ref": "#/definitions/aiDocMatchPattern"}, "suggestion_mode": {"$ref": "#/definitions/aiAskSuggestionMode"}, "think": {"type": "string"}, "wait_answer": {"type": "boolean"}, "think_duration": {"type": "integer", "format": "int32"}, "answer_index": {"type": "integer", "format": "int32", "title": "多任务索引"}, "prompt_type": {"type": "string", "title": "问题类型"}, "last_operation_type": {"$ref": "#/definitions/aiChatOperationType", "title": "最后一次操作"}}}, "aiEventChatMessage": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "chat_id": {"type": "string", "format": "uint64"}, "text": {"type": "string"}, "create_date": {"type": "string", "format": "date-time"}, "type": {"$ref": "#/definitions/aiChatMessageType"}, "rating_scale": {"$ref": "#/definitions/aiRatingScale"}, "docs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageDoc"}}, "link": {"type": "string"}, "question_id": {"type": "string", "format": "uint64"}, "sql_query": {"type": "string"}, "ugcs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiMessageUgc"}}, "create_by": {"type": "string", "format": "uint64"}, "reject_reason": {"type": "string"}, "state": {"$ref": "#/definitions/aiChatMessageState"}, "process_time": {"$ref": "#/definitions/baseTimeRange"}, "lang": {"type": "string"}, "assistant_id": {"type": "string", "format": "uint64"}, "doc_final_query": {"type": "string"}, "ask_type": {"$ref": "#/definitions/aiQuestionAskType"}, "suggest_questions": {"type": "array", "items": {"type": "string"}}, "suggest_count": {"type": "integer", "format": "int32"}, "start_time": {"type": "string", "format": "date-time"}, "end_time": {"type": "string", "format": "date-time"}, "image_url": {"type": "array", "items": {"type": "string"}}, "show_type": {"type": "integer", "format": "int32"}, "doc_match_pattern": {"$ref": "#/definitions/aiDocMatchPattern"}, "suggestion_mode": {"$ref": "#/definitions/aiAskSuggestionMode"}, "think": {"type": "string"}, "wait_answer": {"type": "boolean"}, "think_duration": {"type": "integer", "format": "int32"}, "answer_index": {"type": "integer", "format": "int32", "title": "多任务索引"}, "prompt_type": {"type": "string", "title": "问题类型"}, "answers": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiEventChatMessage"}, "title": "关联的回答"}, "last_operation_type": {"$ref": "#/definitions/aiChatOperationType", "title": "最后一次操作"}, "feedback_id": {"type": "string", "format": "uint64", "title": "教学反馈ID"}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}, "has_user_feedback": {"type": "boolean", "title": "是否有用户反馈"}, "is_file_ready": {"type": "boolean", "title": "文件解析成功"}, "files": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageFile"}}, "is_agent_command": {"type": "boolean", "title": "是否是agent回答"}}}, "aiExportField": {"type": "object", "properties": {"key": {"type": "string"}, "id": {"type": "string", "format": "uint64"}, "example": {"type": "string"}, "label": {"type": "string"}, "rule": {"type": "string"}, "tips": {"type": "string"}}}, "aiExportTask": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "state": {"$ref": "#/definitions/aiExportTaskState"}, "type": {"$ref": "#/definitions/aiExportTaskType"}, "url": {"type": "string"}, "create_by": {"type": "string", "format": "uint64"}, "create_date": {"type": "string", "format": "date-time"}, "last_update_date": {"type": "string", "format": "date-time"}, "operation_type": {"$ref": "#/definitions/aiTaskOperationType"}, "extra_info": {"type": "string"}, "paths": {"type": "array", "items": {"type": "string"}}, "max_batch_size": {"type": "integer", "format": "int32", "title": "单次请求最大数量"}, "max_response_threshold": {"type": "integer", "format": "int32", "title": "返回数据最大阈值"}, "filter_snapshot": {"type": "string"}, "fields_snapshot": {"type": "string"}}}, "aiExportTaskState": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "title": "- 1: 导出中\n - 2: 已完成\n - 3: 失败"}, "aiExportTaskType": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4], "title": "- 1: QA1\n - 2: 文本文件\n - 3: 会话消息\n - 4: 会话"}, "aiExternalSourceUserAuthState": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4], "title": "- 1: 已授权\n - 2: 未授权\n - 3: 已失效\n - 4: 已取消"}, "aiFeedback": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "反馈ID"}, "chat_id": {"type": "string", "format": "uint64", "title": "对话ID"}, "message_id": {"type": "string", "format": "uint64", "title": "消息ID"}, "question": {"type": "string", "title": "问题"}, "answer": {"type": "string", "title": "答案"}, "state": {"$ref": "#/definitions/aiFeedbackState", "title": "状态"}, "create_date": {"type": "string", "format": "date-time", "title": "创建时间"}, "update_date": {"type": "string", "format": "date-time", "title": "更新时间"}, "assistant_id": {"type": "string", "format": "uint64", "title": "助手id"}, "assistant_name": {"type": "string", "title": "助手名称"}, "question_id": {"type": "string", "format": "uint64", "title": "原始问题ID"}, "answer_rating": {"$ref": "#/definitions/aiFeedbackAnswerRating", "title": "回答评价"}, "hit_expected_doc": {"type": "boolean", "title": "是否命中预期知识"}, "op_comment": {"$ref": "#/definitions/aiFeedbackComment", "title": "分析备注"}, "mgmt_feedback": {"$ref": "#/definitions/aiFeedbackComment", "title": "碳LIVE反馈"}, "mgmt_comment": {"$ref": "#/definitions/aiFeedbackComment", "title": "碳LIVE备注"}, "has_user_feedback": {"type": "boolean", "title": "是否有用户反馈"}, "has_op_feedback": {"type": "boolean", "title": "是否有运营反馈"}, "has_mgmt_feedback": {"type": "boolean", "title": "是否有碳LIVE反馈"}, "user_feedback_by": {"$ref": "#/definitions/baseIdentity", "title": "用户反馈人"}, "op_feedback_by": {"$ref": "#/definitions/baseIdentity", "title": "运营反馈人"}, "mgmt_feedback_by": {"$ref": "#/definitions/baseIdentity", "title": "碳LIVE反馈人"}, "user_feedback_at": {"type": "string", "format": "date-time", "title": "用户反馈时间"}, "op_feedback_at": {"type": "string", "format": "date-time", "title": "运营反馈时间"}, "mgmt_feedback_at": {"type": "string", "format": "date-time", "title": "碳LIVE反馈时间"}, "create_identity": {"$ref": "#/definitions/baseIdentity", "title": "创建人身份"}, "update_identity": {"$ref": "#/definitions/baseIdentity", "title": "更新人身份"}, "answer_id": {"type": "string", "format": "uint64", "title": "原始答案ID"}}, "title": "用户反馈"}, "aiFeedbackAction": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5, 6, 7, 8], "description": "- 1: 已读（已废弃）\n - 2: 采用\n - 3: 创建用户反馈\n - 4: 创建运营反馈\n - 5: 创建碳LIVE反馈\n - 6: 更新用户反馈\n - 7: 更新运营反馈\n - 8: 更新碳LIVE反馈", "title": "反馈操作"}, "aiFeedbackAnswerRating": {"type": "integer", "format": "int32", "enum": [1, 2], "description": "- 1: 好\n - 2: 坏", "title": "回答评价"}, "aiFeedbackComment": {"type": "object", "properties": {"content": {"type": "string", "title": "内容"}, "files": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/FeedbackCommentFile"}, "title": "文件列表"}}, "title": "反馈备注"}, "aiFeedbackLog": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "主键"}, "feedback_id": {"type": "string", "format": "uint64", "title": "反馈ID"}, "action": {"$ref": "#/definitions/aiFeedbackAction", "title": "操作"}, "create_identity": {"$ref": "#/definitions/baseIdentity", "title": "操作人"}, "create_date": {"type": "string", "format": "date-time", "title": "操作时间"}}, "title": "反馈操作日志"}, "aiFeedbackState": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "description": "- 1: 未读\n - 2: 已读\n - 3: 已采用", "title": "反馈状态"}, "aiFullAssistant": {"type": "object", "properties": {"assistant": {"$ref": "#/definitions/aiAssistantV2", "title": "助手详情"}, "terms_confirmed": {"type": "boolean", "title": "是否确认协议"}}, "title": "完整助手信息"}, "aiFullFeedbackLog": {"type": "object", "properties": {"log": {"$ref": "#/definitions/aiFeedbackLog", "title": "日志"}, "feedback": {"$ref": "#/definitions/aiFeedback", "title": "反馈"}, "original_question": {"$ref": "#/definitions/aiChatMessage", "title": "原始问题"}}, "title": "完整反馈"}, "aiInteractiveCode": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5, 6], "description": "- 1: 人工\n - 2: 重新回答\n - 3: 清空上下文\n - 4: 读配料表\n - 5: 读检测报告\n - 6: 贡献知识库", "title": "互动暗号"}, "aiInteractiveCodeOption": {"type": "object", "properties": {"code": {"$ref": "#/definitions/aiInteractiveCode", "title": "编号"}, "text": {"type": "string", "title": "文本"}, "default_zh": {"type": "string", "title": "默认中文值"}, "default_en": {"type": "string", "title": "默认英文值"}, "deletable": {"type": "boolean", "title": "是否可删除"}, "default_pre_zh": {"type": "string", "title": "默认中文前缀"}, "default_pre_en": {"type": "string", "title": "默认英文前缀"}}, "title": "互动暗号选项"}, "aiLabelFilter": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "标签id"}, "eq": {"$ref": "#/definitions/aiLabelValue"}, "gte": {"$ref": "#/definitions/aiLabelValue"}, "lte": {"$ref": "#/definitions/aiLabelValue"}, "in": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiLabelValue"}}, "like": {"$ref": "#/definitions/aiLabelValue"}, "op": {"$ref": "#/definitions/aiLabelFilterOp"}}}, "aiLabelFilterOp": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5], "title": "- 1: 等于\n - 2: IN\n - 3: 大于等于\n - 4: 小于等于\n - 5: LIKE模糊搜索"}, "aiLabelValue": {"type": "object", "properties": {"int_value": {"type": "string", "format": "int64", "title": "整型值"}, "uint_value": {"type": "string", "format": "uint64", "title": "无符号整形"}, "float_value": {"type": "number", "format": "double", "title": "浮点型"}, "text_value": {"type": "string", "title": "任意纯文本"}, "enum_value": {"type": "string", "title": "字符串枚举(单选)"}, "enum_m_value": {"type": "string", "title": "字符串枚举(多选)"}, "y_value": {"type": "string", "format": "int64", "title": "年"}, "ym_value": {"type": "string", "format": "int64", "title": "年月"}, "ymd_value": {"type": "string", "format": "int64", "title": "年月日"}, "datetime_value": {"type": "string", "format": "int64", "title": "日期时间(年月日和时间)"}, "time_value": {"type": "string", "format": "int64", "title": "时间"}}, "title": "标签取值"}, "aiListDocFilterType": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "title": "- 1: qa\n - 2: 文本/文件\n - 3: 系统数据"}, "aiManualChunkPara": {"type": "object", "properties": {"assistant_chunks": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiAssistantChunks"}, "title": "新分段列表"}}, "title": "文档手动分段参数"}, "aiMessageCollectionSnapshot": {"type": "object", "properties": {"start_time": {"type": "string", "format": "date-time"}, "end_time": {"type": "string", "format": "date-time"}, "items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveaiSearchCollectionItem"}}, "clean_chunks": {"type": "boolean"}, "message_id": {"type": "string", "format": "uint64"}}}, "aiMessageDocSnapshot": {"type": "object", "properties": {"message_id": {"type": "string", "format": "uint64"}, "docs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageDoc"}}}}, "aiMessageTag": {"type": "object", "properties": {"taggable_type": {"type": "integer", "format": "int32"}, "id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "taggable_id": {"type": "string", "format": "uint64"}, "type": {"type": "integer", "format": "int32"}, "data_type": {"type": "integer", "format": "int32"}}}, "aiMessageUgc": {"type": "object", "properties": {"ugc_type": {"$ref": "#/definitions/baseDataType"}, "filter": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageContentFilterItem"}}, "cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiMessageUgcCard"}}, "is_ugc_link": {"type": "boolean"}, "ugc_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "aiMessageUgcCard": {"type": "object", "properties": {"name": {"type": "string"}, "logo_url": {"type": "string"}, "id": {"type": "string", "format": "uint64"}, "tags": {"type": "string"}}}, "aiOperator": {"type": "object", "properties": {"type": {"$ref": "#/definitions/baseIdentityType"}, "id": {"type": "string", "format": "uint64", "title": "用户账户: 用户id\n团队账户: 用户id\n运营端账户: 运营端用户id"}, "user_id": {"type": "string", "format": "uint64", "title": "type为团队用户时，可选的传入个人的 id"}}}, "aiOrderByLabel": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "desc": {"type": "boolean"}}}, "aiPipelineTaskState": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "title": "- 1: 进行中\n - 2: 已完成\n - 3: 失败"}, "aiQuestionAskType": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "title": "- 1: 正常问答\n - 2: 重新回答(包括了用户输入的\"重新回答\"或者，用户输入了同样的问题)\n - 3: 继续回答(包括被错误识别为了转人工之后确认为继续回答，或者 发送条数到达上限后的继续回答)\n - 4: 预设问答\n - 5: 预设隐藏回答\n - 6: 文件问答\n - 7: 语音问答\n - 8: 图片问答\n - 9: 撤回问答（用户在微信端撤回消息）\n - 10: 匹配到QA的问答"}, "aiQuickAction": {"type": "object", "properties": {"assistants": {"type": "string", "title": "assistantIds"}, "content": {"type": "string", "title": "命令"}, "type": {"type": "integer", "format": "int32", "title": "类型"}, "next_msg_file_forbidden": {"type": "boolean", "title": "下一条问题不能携带image"}}, "title": "助手urgent配置"}, "aiRatingScale": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "description": "- 1: 满意\n - 2: 一般\n - 3: 不满意", "title": "评价等级"}, "aiReferenceType": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "description": "- 1: URL\n - 2: 文本\n - 3: 文件", "title": "参考文献类型"}, "aiReqAutoChunkDoc": {"type": "object", "properties": {"doc_id": {"type": "string", "format": "uint64", "title": "文档ID"}, "new_text": {"type": "string", "title": "新文本（如果文本未改动不需要传值）"}, "auto_para": {"$ref": "#/definitions/aiAutoChunkPara", "title": "自动分段参数"}}}, "aiReqBatchUpdateDocAttr": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "mask": {"type": "string"}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}, "title": "贡献者"}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}, "download_as_ref": {"$ref": "#/definitions/aiDocFileDownloadAsRef", "title": "参考资料下载方式"}, "assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "修改关联的助手，增加的助手启用/禁用状态和已有的助手状态一致"}, "match_patterns": {"type": "array", "items": {"$ref": "#/definitions/aiDocMatchPattern"}, "title": "匹配模式"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}, "title": "参考资料"}, "query_id": {"type": "string", "format": "uint64"}}}, "aiReqBatchUserAssistantLimit": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64"}, "token": {"type": "array", "items": {"type": "string"}}}}, "aiReqBindMiniProgramNormalAccount": {"type": "object", "properties": {"token": {"type": "string"}, "webview_uni_token": {"type": "string"}}}, "aiReqBindMiniProgramPhoneAccount": {"type": "object", "properties": {"code": {"type": "string"}}}, "aiReqBindMiniProgramUniID": {"type": "object", "properties": {"uin_token": {"type": "string"}}}, "aiReqBindOnceUserMiniProgram": {"type": "object", "properties": {"code": {"type": "string"}}}, "aiReqBindUnitokenByCode": {"type": "object", "properties": {"code": {"type": "string"}}}, "aiReqBindUserPhoneByCode": {"type": "object", "properties": {"code": {"type": "string"}, "assistant_id": {"type": "string", "format": "uint64"}}}, "aiReqCloneDoc": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "query_id": {"type": "string", "format": "uint64"}}}, "aiReqConfirmAiServiceTerms": {"type": "object", "properties": {"is_agreed": {"type": "boolean", "title": "是否同意"}}}, "aiReqCreateAssistantShare": {"type": "object", "properties": {"assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "doc_id": {"type": "string", "format": "uint64"}, "doc_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "query_id": {"type": "string", "format": "uint64"}, "user_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "分享给个人的ID列表"}, "team_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "分享给团队的ID列表"}}}, "aiReqCreateChat": {"type": "object", "properties": {"title": {"type": "string", "title": "会话标题"}, "assistant_id": {"type": "string", "format": "uint64", "title": "助手id"}, "image_urls": {"type": "array", "items": {"type": "string"}, "title": "图片"}, "mini_program": {"type": "boolean", "title": "是否小程序"}, "publish_hash_id": {"type": "string", "title": "hash_id"}, "show_answer": {"type": "boolean", "title": "是否返回answer"}, "lang": {"type": "string", "title": "语言 zh en"}}}, "aiReqCreateChatExportTask": {"type": "object", "properties": {"filter": {"$ref": "#/definitions/tanlivebff_webaiReqListChat"}, "fields": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiExportField"}}}}, "aiReqCreateChatMessageExportTask": {"type": "object", "properties": {"filter": {"$ref": "#/definitions/tanlivebff_webaiReqListChat"}, "fields": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiExportField"}}}}, "aiReqCreateChatQuestion": {"type": "object", "properties": {"chat_id": {"type": "string", "format": "uint64"}, "text": {"type": "string", "title": "问题"}, "assistant_id": {"type": "string", "format": "uint64", "title": "助手id"}, "file_urls": {"type": "array", "items": {"type": "string"}, "title": "文件urls"}, "is_agent_command": {"type": "boolean", "title": "是否是agent暗号"}, "lang": {"type": "string", "title": "语言 zh en"}}}, "aiReqCreateDocShareConfigReceiverAssistantUserShare": {"type": "object", "properties": {"user_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "team_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "state": {"$ref": "#/definitions/aiDocShareState"}}}, "aiReqCreateFeedback": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64", "title": "助手ID"}, "question": {"type": "string", "title": "问题"}, "answer": {"type": "string", "title": "答案"}, "references": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiFeedbackReference"}, "title": "参考文献"}}}, "aiReqCreateFileExportTask": {"type": "object", "properties": {"filter": {"$ref": "#/definitions/aiReqListTextFiles"}, "fields": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiExportField"}}}}, "aiReqCreateGTBDocText": {"type": "object", "properties": {"is_pull_all": {"type": "boolean"}}}, "aiReqCreateQAs": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiReqCreateQA"}}}}, "aiReqCreateQaExportTask": {"type": "object", "properties": {"filter": {"$ref": "#/definitions/tanlivebff_webaiReqListQA"}, "fields": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiExportField"}}}}, "aiReqCreateTextFile": {"type": "object", "properties": {"file_name": {"type": "string", "title": "文件/文本名称"}, "text": {"type": "string", "title": "文件/文本内容"}, "assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "助手"}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}, "title": "贡献者"}, "url": {"type": "string", "title": "文件url"}, "ugc_type": {"$ref": "#/definitions/baseDataType", "title": "ugc类型"}, "ugc_id": {"type": "string", "format": "uint64", "title": "ugc的id"}, "parsed_url": {"type": "string", "title": "文件解析后地址"}, "state": {"$ref": "#/definitions/tanliveaiDocState", "title": "状态 1: 启用 2: 停用 状态设置只对文本有效"}, "type": {"type": "integer", "format": "int64", "title": "类型 2:文本 3:文件"}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}, "share_assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "download_as_ref": {"$ref": "#/definitions/aiDocFileDownloadAsRef"}, "parse_mode": {"$ref": "#/definitions/aiDocParseMode"}, "data_source": {"$ref": "#/definitions/aiDocDataSource", "title": "数据源"}}}, "aiReqCreateTextFiles": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiReqCreateTextFile"}, "title": "解析模式"}}}, "aiReqDeleteChat": {"type": "object", "properties": {"chat_id": {"type": "string", "format": "uint64"}}}, "aiReqDeleteDocs": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "query_id": {"type": "string", "format": "uint64"}}}, "aiReqDescribeAccountIsGTB": {"type": "object"}, "aiReqDescribeChatMessages": {"type": "object", "properties": {"chat_id": {"type": "string", "format": "uint64"}, "offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "without_docs": {"type": "boolean"}, "question_id": {"type": "string", "format": "uint64"}}}, "aiReqDescribeChats": {"type": "object", "properties": {"offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "assistant_id": {"type": "string", "format": "uint64", "title": "助手id"}}}, "aiReqDescribeDocList": {"type": "object", "properties": {"state": {"type": "string"}, "search": {"type": "string"}, "start": {"type": "integer", "format": "int64"}, "folder_id": {"type": "string"}, "hash_user_id": {"type": "string"}, "title_name_sort": {"type": "string", "title": "asc desc"}}}, "aiReqDescribeMessageFileState": {"type": "object", "properties": {"message_id": {"type": "string", "format": "uint64"}}}, "aiReqDescribeTencentToken": {"type": "object", "properties": {"hash_user_id": {"type": "string"}}}, "aiReqFindFeedback": {"type": "object", "properties": {"feedback_id": {"type": "string", "format": "uint64", "title": "用户反馈ID"}}}, "aiReqGetAccountOnceBindStatus": {"type": "object", "properties": {"code": {"type": "string"}}}, "aiReqGetAccountUnionIdStatus": {"type": "object", "properties": {"code": {"type": "string"}}}, "aiReqGetAssistantChunkConfig": {"type": "object", "properties": {"offset": {"type": "integer", "format": "int64", "title": "分页偏移量"}, "limit": {"type": "integer", "format": "int64", "title": "分页大小"}, "doc_id": {"type": "string", "format": "uint64", "title": "文档ID"}, "collection_lang": {"type": "string", "title": "向量化模型"}}}, "aiReqGetAssistantConfig": {"type": "object", "properties": {"only_tanlive_app": {"type": "boolean", "title": "仅查询碳LIVE应用助手"}, "route_path": {"type": "string", "title": "通过路由查询"}, "assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "助手ID"}, "app_id": {"type": "string", "title": "应用ID"}, "only_tanlive_miniprogram": {"type": "boolean", "title": "仅查询碳LIVE小程序助手"}}}, "aiReqGetAssistantsMiniprogram": {"type": "object", "properties": {"only_recently_use": {"type": "boolean", "title": "true 只查看最近使用的助手，如果没有则用默认配置填充"}, "offset": {"type": "integer", "format": "int64", "title": "分页偏移量"}, "limit": {"type": "integer", "format": "int64", "title": "分页大小"}, "search": {"type": "string", "title": "搜索助手名称或机构名称"}}}, "aiReqGetChatMessageDetail": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}}}, "aiReqGetMiniProgramAssistantLimit": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64"}}}, "aiReqGetMiniProgramAuth": {"type": "object", "properties": {"auth_id": {"type": "string"}, "ref_auth_id": {"type": "string", "title": "pc、h5来源"}}}, "aiReqGetMiniProgramLoginURL": {"type": "object", "properties": {"type": {"type": "integer", "format": "int64", "title": "0 pc 1 h5"}}}, "aiReqGetMiniProgramUserInfo": {"type": "object", "properties": {"select_token": {"type": "string"}}}, "aiReqGetMyAssistants": {"type": "object", "properties": {"offset": {"type": "integer", "format": "int64", "title": "分页偏移量"}, "limit": {"type": "integer", "format": "int64", "title": "分页大小"}, "assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "助手ID"}}}, "aiReqGetPublicChatShare": {"type": "object", "properties": {"share_id": {"type": "string", "title": "分享ID"}}, "title": "获取公开分享详情请求（无需登录）"}, "aiReqGetTextFile": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}}}, "aiReqGetWebViewToMiniProgramToken": {"type": "object"}, "aiReqImportQA": {"type": "object", "properties": {"question": {"type": "string"}, "answer": {"type": "string"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}, "title": "贡献者"}, "assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}, "share_assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "mask": {"type": "string", "title": "用于指定哪些字段需要更新"}, "match_patterns": {"type": "array", "items": {"$ref": "#/definitions/aiDocMatchPattern"}, "title": "匹配模式"}, "force_create": {"type": "boolean", "title": "是否强制新建，如果为true则不进行重复校验，直接创建新的QA"}}}, "aiReqImportQAs": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiReqImportQA"}}}}, "aiReqImportTextFile": {"type": "object", "properties": {"file_name": {"type": "string", "title": "文件/文本名称"}, "text": {"type": "string", "title": "文件/文本内容"}, "assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "助手"}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}, "title": "贡献者"}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}, "share_assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "mask": {"type": "string", "title": "用于指定哪些字段需要更新"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "download_as_ref": {"$ref": "#/definitions/aiDocFileDownloadAsRef", "title": "是否可以作为参考资料下载"}, "force_create": {"type": "boolean", "title": "是否强制新建，如果为true则不进行重复校验，直接创建新的文本文件"}}}, "aiReqImportTextFiles": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiReqImportTextFile"}}}}, "aiReqListCollectionFileName": {"type": "object", "properties": {"search": {"type": "string", "title": "文件名模糊搜索匹配"}, "offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "full_search": {"type": "array", "items": {"type": "string"}, "title": "文件名精确匹配搜索"}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}}}, "aiReqListNebulaAssistants": {"type": "object", "properties": {"search": {"type": "string"}, "offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}}}, "aiReqListNebulaContributors": {"type": "object", "properties": {"assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "aiReqListOperator": {"type": "object", "properties": {"search": {"type": "string"}, "type": {"$ref": "#/definitions/aiListDocFilterType"}, "creator": {"type": "boolean", "title": "是否为创建人，false代表更新人，true代表创建人"}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}}}, "aiReqListTextFiles": {"type": "object", "properties": {"assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "state": {"$ref": "#/definitions/tanliveaiDocState"}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "update_by": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiOperator"}}, "order_by": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/baseOrderBy"}}, "offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "search": {"$ref": "#/definitions/aiReqListTextFilesSearch"}, "id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "excluded_assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "不在助手中"}, "group_repeated": {"type": "boolean", "title": "重复 doc 紧凑排序"}, "show_contributor": {"type": "integer", "format": "int64"}, "shared_state": {"type": "array", "items": {"$ref": "#/definitions/aiDocSharedState"}}, "share_assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "分享的助手"}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiLabelFilter"}}, "order_by_label": {"$ref": "#/definitions/aiOrderByLabel", "title": "自定义标签排序，只能当个标签排序"}, "ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "tql_expression": {"type": "string", "title": "TQL表达式（高级搜索）"}, "download_as_ref": {"$ref": "#/definitions/aiDocFileDownloadAsRef"}, "create_by": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiOperator"}}, "parse_mode": {"type": "array", "items": {"$ref": "#/definitions/aiDocParseMode"}}, "parse_state": {"$ref": "#/definitions/tanliveaiDocState", "title": "查询解析失败的数据"}, "tip_filter": {"$ref": "#/definitions/aiReqListTextFilesTipFilter"}, "data_source": {"$ref": "#/definitions/aiDocDataSource", "title": "数据源过滤"}, "data_source_state": {"type": "integer", "format": "int64", "title": "数据源同步状态"}, "share_team_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "分享的团队id"}, "share_user_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "分享的用户id"}, "embedding_state": {"$ref": "#/definitions/aiDocEmbeddingState", "title": "向量状态"}}}, "aiReqListTextFilesSearch": {"type": "object", "properties": {"text": {"type": "string", "title": "文本内容搜索"}, "file_name": {"type": "string", "title": "文件名搜索"}}}, "aiReqListTextFilesTipFilter": {"type": "object", "properties": {"warning": {"type": "boolean", "title": "警告条件组"}}, "title": "知识提示过滤条件"}, "aiReqManualChunkDoc": {"type": "object", "properties": {"doc_id": {"type": "string", "format": "uint64", "title": "文档ID"}, "new_text": {"type": "string", "title": "新文本（如果文本未改动不需要传值）"}, "manual_para": {"$ref": "#/definitions/aiManualChunkPara", "title": "手动分段参数"}}}, "aiReqModifyCustomLabels": {"type": "object", "properties": {"labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "object_type": {"$ref": "#/definitions/aiCustomLabelObjectType"}}}, "aiReqOnOffDocs": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "state": {"$ref": "#/definitions/tanliveaiDocState"}, "query_id": {"type": "string", "format": "uint64"}}}, "aiReqProxyChatHtmlUrl": {"type": "object", "properties": {"urls": {"type": "array", "items": {"type": "string"}}, "assistant_id": {"type": "string", "format": "uint64", "title": "助手id"}}}, "aiReqReceiveChatMessage": {"type": "object", "properties": {"chat_id": {"type": "string", "format": "uint64"}, "text": {"type": "string", "title": "问题"}, "assistant_id": {"type": "string", "format": "uint64", "title": "助手id"}, "image_urls": {"type": "array", "items": {"type": "string"}, "title": "图片"}, "mini_program": {"type": "boolean", "title": "是否小程序"}, "publish_hash_id": {"type": "string", "title": "hash id"}, "question_id": {"type": "string", "format": "uint64", "title": "重新回答的问题id"}, "show_answer": {"type": "boolean", "title": "是否返回answer"}, "lang": {"type": "string", "title": "语言 zh en"}}}, "aiReqResendChatMessage": {"type": "object", "properties": {"message_id": {"type": "string", "format": "uint64", "title": "问题的id"}, "assistant_id": {"type": "string", "format": "uint64", "title": "助手id"}, "publish_hash_id": {"type": "string", "title": "hash id"}}}, "aiReqRestartExportTask": {"type": "object", "properties": {"task_id": {"type": "string", "format": "uint64"}}}, "aiReqSaveOpFeedback": {"type": "object", "properties": {"answer_id": {"type": "string", "format": "uint64", "title": "答案ID"}, "answer_rating": {"$ref": "#/definitions/aiFeedbackAnswerRating", "title": "AI回答评价"}, "hit_expected_doc": {"type": "boolean", "title": "是否命中预期知识"}, "doc_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "预期命中的知识"}, "op_comment": {"$ref": "#/definitions/aiFeedbackComment", "title": "分析备注"}, "feedback_id": {"type": "string", "format": "uint64", "title": "答案ID"}}}, "aiReqSaveUserFeedbackByQuestion": {"type": "object", "properties": {"answer_id": {"type": "string", "format": "uint64", "title": "答案ID"}, "answer": {"type": "string", "title": "答案"}, "references": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiFeedbackReference"}, "title": "参考文献"}}}, "aiReqSearchChat": {"type": "object", "properties": {"text": {"type": "string"}, "question_id": {"type": "string", "format": "uint64"}, "assistant_id": {"type": "string", "format": "uint64", "title": "助手id"}, "top_n": {"type": "integer", "format": "int64", "title": "topN"}, "threshold": {"type": "number", "format": "float", "title": "阈值"}, "text_recall_top_n": {"type": "integer", "format": "int32", "title": "关键词召回条数"}, "temperature": {"type": "number", "format": "float", "title": "温度"}, "clean_chunks": {"type": "boolean"}, "text_recall_query": {"$ref": "#/definitions/aiTextRecallQuery", "title": "关键词召回匹配目标"}, "text_recall_pattern": {"$ref": "#/definitions/aiTextRecallPattern", "title": "关键词召回模式"}, "text_recall_slop": {"type": "integer", "format": "int32", "title": "关键词召回允许平移距离允许平移距离"}}}, "aiReqSearchChatUsers": {"type": "object", "properties": {"offset": {"type": "integer", "format": "int64", "title": "分页偏移量"}, "limit": {"type": "integer", "format": "int64", "title": "分页大小"}, "assistant_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "助手id"}, "keyword": {"type": "string", "title": "搜索关键词"}, "region": {"$ref": "#/definitions/baseRegion", "title": "地区"}, "user_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "aiReqUpdateMyAssistant": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64", "title": "助手ID"}, "config": {"$ref": "#/definitions/aiAssistantConfig", "title": "配置详情"}, "mask": {"type": "string", "title": "更新字段列表，例如：\"name,prompt_prefix,search_engine\""}}}, "aiReqUpdateObjectCustomLabels": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "打标签的对象 id"}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}, "title": "自定义标签kv对"}, "object_type": {"$ref": "#/definitions/aiCustomLabelObjectType", "title": "打标签的对象类型"}}}, "aiReqUpdateQAs": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiReqUpdateQA"}}}}, "aiReqUpdateTextFiles": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiReqUpdateTextFile"}}}}, "aiReqValidateQAs": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiReqValidateQAsItem"}}}}, "aiReqValidateQAsItem": {"type": "object", "properties": {"question": {"type": "string"}, "answer": {"type": "string"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}, "title": "贡献者"}, "state": {"$ref": "#/definitions/tanliveaiDocState"}}}, "aiReqValidateTextFiles": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiReqValidateTextFilesItem"}}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}}}, "aiReqValidateTextFilesItem": {"type": "object", "properties": {"file_name": {"type": "string"}, "text": {"type": "string"}}}, "aiRspAcceptFeedback": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspAcceptFeedbackResult"}, "title": "结果"}}}, "aiRspAutoChunkDoc": {"type": "object", "properties": {"chunks": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChunkItem"}, "title": "分段列表"}}}, "aiRspBatchUpdateDocAttr": {"type": "object", "properties": {"async": {"type": "boolean"}}}, "aiRspBatchUserAssistantLimit": {"type": "object", "properties": {"user_limits": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspBatchUserAssistantLimitUserLimit"}}}}, "aiRspBindMiniProgramNormalAccount": {"type": "object", "properties": {"token": {"type": "string"}}}, "aiRspBindMiniProgramPhoneAccount": {"type": "object", "properties": {"token": {"type": "string"}, "is_have_phone": {"type": "boolean"}, "user_name": {"type": "string"}, "phone": {"type": "string"}}}, "aiRspBindOnceUserMiniProgram": {"type": "object", "properties": {"token": {"type": "string"}}}, "aiRspBindUserPhoneByCode": {"type": "object", "properties": {"can_in": {"type": "boolean"}, "bind_other_account": {"type": "boolean"}, "is_have_phone": {"type": "boolean"}, "user_name": {"type": "string"}}}, "aiRspCloneDoc": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "aiRspCreateAssistantShare": {"type": "object", "properties": {"async": {"type": "boolean"}}}, "aiRspCreateChat": {"type": "object", "properties": {"chat_id": {"type": "string", "format": "uint64", "title": "会话id"}, "message_id": {"type": "string", "format": "uint64", "title": "第一个问题的id"}, "answer_id": {"type": "string", "format": "uint64"}, "answer": {"$ref": "#/definitions/aiChatMessage"}}}, "aiRspCreateChatQuestion": {"type": "object", "properties": {"chat_id": {"type": "string", "format": "uint64"}, "question_id": {"type": "string", "format": "uint64"}, "lang": {"type": "string"}, "is_file_ready": {"type": "boolean"}}}, "aiRspCreateDocShareConfigReceiverAssistant": {"type": "object"}, "aiRspCreateDocShareConfigReceiverUserTeam": {"type": "object"}, "aiRspCreateDocShareConfigSender": {"type": "object"}, "aiRspCreateFeedback": {"type": "object", "properties": {"feedback_id": {"type": "string", "format": "uint64", "title": "反馈ID"}}}, "aiRspCreateGTBDocText": {"type": "object", "properties": {"uuid": {"type": "string"}}}, "aiRspCreateQAs": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "aiRspCreateTextFiles": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "aiRspDeleteDocs": {"type": "object", "properties": {"async": {"type": "boolean"}}}, "aiRspDescribeAccountIsGTB": {"type": "object", "properties": {"is_gtb": {"type": "boolean"}}}, "aiRspDescribeChatMessages": {"type": "object", "properties": {"chat_messages": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiEventChatMessage"}}, "total_count": {"type": "integer", "format": "int64"}}}, "aiRspDescribeChats": {"type": "object", "properties": {"chats": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiChat"}}, "total_count": {"type": "integer", "format": "int64"}}}, "aiRspDescribeDocList": {"type": "object", "properties": {"docs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiTencentDoc"}}, "next": {"type": "integer", "format": "int64"}}}, "aiRspDescribeMessageFileState": {"type": "object", "properties": {"files": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageFile"}}, "is_file_ready": {"type": "boolean"}}}, "aiRspDescribeTencentToken": {"type": "object", "properties": {"is_empty": {"type": "boolean"}}}, "aiRspExportTask": {"type": "object", "properties": {"task_id": {"type": "string", "format": "uint64"}}}, "aiRspFindFeedback": {"type": "object", "properties": {"feedback": {"$ref": "#/definitions/aiFeedback", "title": "反馈详情"}, "references": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveaiFeedbackReference"}, "title": "参考文献"}, "create_by": {"$ref": "#/definitions/iamUserInfo", "title": "上传用户"}, "original_question": {"$ref": "#/definitions/aiChatMessage", "title": "原始问题"}, "original_answer": {"$ref": "#/definitions/aiChatMessage", "title": "原始回答"}, "expected_docs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageDoc"}, "title": "预期命中的知识"}, "user_cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/iamUserCard"}, "title": "用户卡片列表"}, "team_cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/teamTeamCard"}, "title": "团队卡片列表"}, "expected_mgmt_docs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageDoc"}, "title": "预期命中的知识（碳LIVE运营）"}}}, "aiRspGetAccountOnceBindStatus": {"type": "object", "properties": {"has_once_bind": {"type": "boolean"}}}, "aiRspGetAccountUnionIdStatus": {"type": "object", "properties": {"bind_account_status": {"type": "boolean"}}}, "aiRspGetAssistantChunkConfig": {"type": "object", "properties": {"assistants": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiFullAssistant"}, "title": "助手列表"}, "total_count": {"type": "integer", "format": "int64", "title": "总数"}, "collection_lang_count": {"type": "integer", "format": "int64", "title": "向量化模型数量"}}}, "aiRspGetAssistantConfig": {"type": "object", "properties": {"assistants": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiFullAssistant"}, "title": "助手列表"}, "user_cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/iamUserCard"}, "title": "用户卡片列表"}, "team_cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/teamTeamCard"}, "title": "团队卡片列表"}}}, "aiRspGetAssistantsMiniprogram": {"type": "object", "properties": {"assistants": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiFullAssistant"}, "title": "助手列表"}, "user_cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/iamUserCard"}, "title": "用户卡片列表"}, "team_cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/teamTeamCard"}, "title": "团队卡片列表"}, "total_count": {"type": "integer", "format": "int64"}}}, "aiRspGetChatMessageDetail": {"type": "object", "properties": {"message": {"$ref": "#/definitions/aiEventChatHashMessage"}, "collection_items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiSearchCollectionItem"}}, "logs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageLog"}}, "collection_time": {"$ref": "#/definitions/baseTimeRange"}, "clean_chunks": {"type": "boolean"}}}, "aiRspGetFeedbacksItem": {"type": "object", "properties": {"feedback": {"$ref": "#/definitions/aiFeedback", "title": "反馈详情"}, "references": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveaiFeedbackReference"}, "title": "参考文献"}, "original_question": {"$ref": "#/definitions/aiChatMessage", "title": "原始问题"}, "original_answer": {"$ref": "#/definitions/aiChatMessage", "title": "原始回答"}, "expected_docs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageDoc"}, "title": "预期命中的知识"}, "expected_mgmt_docs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageDoc"}, "title": "预期命中的知识（碳LIVE运营）"}}}, "aiRspGetMiniProgramAssistantLimit": {"type": "object", "properties": {"empty_phone": {"type": "boolean", "title": "如果限制，需要检测手机号，true 空手机， false 不空；"}, "can_in": {"type": "boolean", "title": "是否可以进入 true 可， false 不可"}}}, "aiRspGetMiniProgramAuth": {"type": "object", "properties": {"has_team": {"type": "boolean"}, "has_doc_auth": {"type": "boolean"}, "has_assistant": {"type": "boolean"}, "has_bind_wx": {"type": "boolean", "title": "是否还在绑定微信中"}, "uin_token": {"type": "string"}, "has_doc_auth_read": {"type": "boolean"}, "has_doc_auth_write": {"type": "boolean"}}}, "aiRspGetMiniProgramLoginURL": {"type": "object", "properties": {"url": {"type": "string"}}}, "aiRspGetMiniProgramUserInfo": {"type": "object", "properties": {"token": {"type": "string"}}}, "aiRspGetMyAiServiceTermsConfirmation": {"type": "object", "properties": {"assistant_identity": {"type": "integer", "format": "int32", "title": "开通助手身份：0未开通；1个人开通；2团队开通"}, "popup": {"type": "boolean", "title": "是否已弹窗"}, "is_agreed": {"type": "boolean", "title": "是否已同意"}, "terms_type": {"$ref": "#/definitions/cmsTermsType", "title": "协议类型"}, "terms_doc": {"type": "string", "title": "协议文档路径"}, "is_verified": {"type": "boolean", "title": "团队是否已认证"}}}, "aiRspGetMyAssistants": {"type": "object", "properties": {"assistants": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiFullAssistant"}, "title": "助手列表"}, "total_count": {"type": "integer", "format": "int64", "title": "总数"}}}, "aiRspGetPublicChatShare": {"type": "object", "properties": {"share_id": {"type": "string", "title": "分享ID"}, "chat_id": {"type": "string", "format": "uint64", "title": "原会话ID"}, "assistant_id": {"type": "string", "format": "uint64", "title": "助手ID"}, "share_type": {"$ref": "#/definitions/tanlivebff_webaiShareType", "title": "分享类型"}, "share_status": {"$ref": "#/definitions/tanlivebff_webaiShareStatus", "title": "分享状态"}, "shared_by": {"type": "string", "format": "uint64", "title": "分享者ID"}, "access_count": {"type": "integer", "format": "int32", "title": "访问次数"}, "share_date": {"type": "string", "format": "date-time", "title": "分享创建时间"}, "expire_date": {"type": "string", "format": "date-time", "title": "分享过期时间"}, "last_access_time": {"type": "string", "format": "date-time", "title": "最后访问时间"}, "messages": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiEventChatMessage"}, "title": "分享的消息列表"}}, "title": "获取公开分享详情响应"}, "aiRspGetTextFile": {"type": "object", "properties": {"item": {"$ref": "#/definitions/aiCollectionTextFile"}}}, "aiRspGetWebViewToMiniProgramToken": {"type": "object", "properties": {"token": {"type": "string"}, "webview_uni_token": {"type": "string"}}}, "aiRspImportQAs": {"type": "object"}, "aiRspImportTextFiles": {"type": "object"}, "aiRspListCollectionFileName": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspListCollectionFileNameItem"}}}}, "aiRspListCollectionFileNameItem": {"type": "object", "properties": {"url": {"type": "string", "title": "文件绑定的url/path"}, "name": {"type": "string", "title": "文件名称"}, "id": {"type": "string", "format": "uint64", "title": "文件id"}}}, "aiRspListDocShareConfigReceiverAssistantMembers": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}}}, "aiRspListDocShareConfigReceiverUserTeamMembers": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}}}, "aiRspListNebulaAssistants": {"type": "object", "properties": {"assistants": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiAssistantV2"}}, "total_count": {"type": "integer", "format": "int64"}}}, "aiRspListNebulaContributors": {"type": "object", "properties": {"contributors": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}}}, "aiRspListOperator": {"type": "object", "properties": {"operators": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocOperator"}}}}, "aiRspListTextFiles": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCollectionTextFile"}}, "fail_parse_count": {"type": "integer", "format": "int64"}}}, "aiRspListeDocShareConfigSenderSharedAssistant": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "name_en": {"type": "string"}, "is_selected": {"type": "boolean"}}}, "aiRspManualChunkDoc": {"type": "object", "properties": {"task_id": {"type": "string", "format": "uint64", "title": "任务ID"}}}, "aiRspModifyCustomLabels": {"type": "object"}, "aiRspModifyDocTab": {"type": "object", "properties": {"admin_type": {"type": "string", "format": "uint64"}, "account_id": {"type": "string", "format": "uint64"}}}, "aiRspOnOffDocs": {"type": "object", "properties": {"pre_repeat_collections": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspOnOffDocsRepeatCollection"}}, "repeat_collections": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspOnOffDocsRepeatCollection"}}, "qa_num_exceed": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspOnOffDocsQaContainsMatchCount"}}, "async": {"type": "boolean"}}}, "aiRspOnOffDocsQaContainsMatchCount": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64"}, "cnt": {"type": "string", "format": "uint64"}}}, "aiRspOnOffDocsRepeatCollection": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "file_name": {"type": "object", "additionalProperties": {"type": "string"}}}}, "aiRspProxyChatHtmlUrl": {"type": "object", "properties": {"contents": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspProxyChatHtmlUrlContent"}}}}, "aiRspReceiveChatMessage": {"type": "object", "properties": {"message_id": {"type": "string", "format": "uint64", "title": "问题的id"}, "answer_id": {"type": "string", "format": "uint64"}, "answer": {"$ref": "#/definitions/aiChatMessage"}}}, "aiRspSearchChat": {"type": "object", "properties": {"message": {"$ref": "#/definitions/aiEventChatMessage"}, "user_id": {"type": "string", "format": "uint64"}, "is_op": {"type": "boolean", "title": "是否是推送运营端"}, "is_only_search": {"type": "boolean", "title": "是否仅搜索"}}}, "aiRspSearchChatUsers": {"type": "object", "properties": {"users": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/iamUserInfo"}, "title": "用户列表"}, "total_count": {"type": "integer", "format": "int64", "title": "总数"}}}, "aiRspSwitchChatLiveAgent": {"type": "object", "properties": {"state": {"$ref": "#/definitions/aiSwitchChatState", "title": "切换结果"}}}, "aiRspValidateQAs": {"type": "object", "properties": {"errors": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspValidateQAsErr"}}}}, "aiRspValidateQAsErr": {"type": "object", "properties": {"code": {"$ref": "#/definitions/errorsAiError"}, "message": {"type": "string"}, "id": {"type": "string", "format": "uint64"}}}, "aiRspValidateTextFiles": {"type": "object", "properties": {"errors": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspValidateTextFilesErr"}}}}, "aiRspValidateTextFilesErr": {"type": "object", "properties": {"code": {"$ref": "#/definitions/errorsAiError"}, "message": {"type": "string"}, "id": {"type": "string", "format": "uint64"}}}, "aiSearchCollectionType": {"type": "integer", "format": "int32", "enum": [1, 2], "title": "- 1: 向量搜索\n - 2: 文本搜索"}, "aiSearchEngineOption": {"type": "object", "properties": {"value": {"type": "string", "title": "值"}, "name": {"type": "string", "title": "名称"}, "name_en": {"type": "string", "title": "英文名称"}}, "title": "搜索引擎选项"}, "aiSwitchChatState": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5], "title": "- 1: 切换成功\n - 2: 会话已结束\n - 3: 人工坐席离线\n - 4: 会话信息错误（需要重新开启一个会话或者再问一个问题）\n - 5: 人工坐席不存在"}, "aiTaskOperationType": {"type": "integer", "format": "int32", "enum": [1, 2], "title": "- 1: 导出\n - 2: 导入"}, "aiTextFileTipTableOverSize": {"type": "object", "properties": {"header": {"type": "string", "title": "表头"}, "assistant_id": {"type": "string", "format": "uint64", "title": "助手 id"}, "assistant_name": {"type": "string", "title": "助手中文名称"}, "assistant_name_en": {"type": "string", "title": "助手英文名称"}, "table_title": {"type": "string", "title": "表格标题"}}}, "aiTextRecallPattern": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "description": "- 1: 短语匹配\n - 2: 字匹配\n - 3: 英文模糊匹配", "title": "关键词召回模式"}, "aiTextRecallQuery": {"type": "integer", "format": "int32", "enum": [1, 2], "description": "- 1: 在知识库的\"QA\"中召回\n - 2: 仅在知识库的\"Q\"中召回", "title": "QA关键词召回目标"}, "aiVisibleChainOption": {"type": "object", "properties": {"field": {"type": "string", "title": "字段"}, "name": {"type": "string", "title": "名称"}, "name_en": {"type": "string", "title": "英文名称"}, "uncheck": {"type": "boolean", "title": "是否默认不选中"}}, "title": "链路查询选项"}, "baseDataType": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9], "description": "- 1: 团队\n - 2: 产品\n - 3: 资源\n - 4: 图谱\n - 5: 定向推送\n - 6: 用户个人\n - 7: 图谱AI\n - 8: 帮助中心文档\n - 9: AI助手", "title": "数据类型"}, "baseIdentity": {"type": "object", "properties": {"identity_type": {"$ref": "#/definitions/baseIdentityType", "title": "身份类型"}, "identity_id": {"type": "string", "format": "uint64", "title": "身份ID"}, "name": {"type": "string", "title": "名字"}, "extra_id": {"type": "string", "format": "uint64", "title": "额外ID（团队类型表示用户ID）"}}, "title": "身份"}, "baseIdentityType": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4], "description": "- 1: 门户端用户\n - 2: 团队\n - 3: 运营端用户\n - 4: 自定义", "title": "身份类型"}, "baseOrderBy": {"type": "object", "properties": {"column": {"type": "string", "title": "列名"}, "desc": {"type": "boolean", "title": "是否倒序"}}, "title": "排序"}, "baseRegion": {"type": "integer", "format": "int32", "enum": [1, 2], "description": "- 1: 国内\n - 2: 海外", "title": "地域"}, "baseTimeRange": {"type": "object", "properties": {"start": {"type": "string", "format": "date-time", "title": "开始时间"}, "end": {"type": "string", "format": "date-time", "title": "结束时间"}}, "title": "时间范围"}, "cmsTermsType": {"type": "integer", "format": "int32", "enum": [1, 2], "description": "- 1: AI服务协议（国内）\n - 2: AI服务协议（海外）", "title": "协议类型"}, "errorsAiError": {"type": "integer", "format": "int32", "enum": [16001, 16002, 16003, 16004, 16005, 16006, 16007, 16008, 16009, 16010, 16011, 16012, 16013, 16014, 16015, 16016, 16017, 16018, 16019, 16020, 16021, 16022, 16023, 16024], "description": "- 16001: QA中的问题已经存在\n - 16002: 用户反馈已被采用\n - 16003: 用户反馈已标记已读\n - 16004: 用户反馈状态不允许被采用\n - 16005: 用户chat不存在\n - 16006: 非法的文档状态转换\n - 16007: 问题审核失败\n - 16008: 非法的AI租户\n - 16009: 非法的文档内容状态\n - 16010: doc中的文本/文件已经存在\n - 16011: 非法的自定义列转换\n - 16012: 助手不存在\n - 16013: 助手名称已存在\n - 16014: 助手英文名称已存在\n - 16015: 助手路由已存在\n - 16016: 助手已禁用\n - 16017: 助手当前功能已禁用\n - 16018: 助手应用ID已存在\n - 16019: 小程序code无效\n - 16020: 不是知识的贡献者\n - 16021: 助手客服用户名重复\n - 16022: 文档有运行中的分段任务\n - 16023: doc外部源Token已过期\n - 16024: 文件剪存文件夹不存在", "title": "AI服务错误\n范围：[16000, 17000)"}, "iamUserCard": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "用户ID"}, "username": {"type": "string", "title": "用户名"}, "image": {"type": "string", "title": "头像"}, "level": {"type": "string", "title": "等级"}}, "title": "用户卡片"}, "iamUserInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "用户ID"}, "username": {"type": "string", "title": "用户名"}, "image": {"type": "string", "title": "头像"}, "firm_id": {"type": "string", "format": "uint64", "title": "团队ID"}, "identity_set": {"$ref": "#/definitions/baseIdentityType", "title": "身份"}, "level": {"type": "string", "title": "等级"}, "country": {"type": "string", "title": "国家"}, "province": {"type": "string", "title": "省"}, "city": {"type": "string", "title": "市"}, "region_code": {"type": "string", "title": "地区编码"}, "timezone": {"type": "string", "title": "时区"}, "union_id": {"type": "string", "title": "微信unionID"}, "phone": {"type": "string", "title": "手机号"}, "phone_hash": {"type": "string", "title": "手机号哈希"}, "nick_name": {"type": "string", "title": "用户昵称"}}, "title": "用户信息"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "tanliveaiAssistant": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "name_en": {"type": "string"}, "collections": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCollection"}}, "website_route": {"type": "string"}, "search_debug": {"type": "boolean"}, "threshold": {"type": "number", "format": "float"}, "top_n": {"type": "integer", "format": "int32"}, "text_weight": {"type": "number", "format": "float"}, "text_recall_top_n": {"type": "integer", "format": "int32", "title": "关键词召回条数"}, "temperature": {"type": "number", "format": "float", "title": "温度"}, "clean_chunks": {"type": "boolean"}, "text_recall_query": {"$ref": "#/definitions/aiTextRecallQuery", "title": "关键词召回匹配目标"}, "text_recall_pattern": {"$ref": "#/definitions/aiTextRecallPattern", "title": "关键词召回模式"}, "text_recall_slop": {"type": "integer", "format": "int32", "title": "关键词召回允许平移距离允许平移距离"}}}, "tanliveaiDocState": {"type": "integer", "format": "int32", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9], "description": "- 1: 启用\n - 2: 禁用\n - 3: 解析中\n - 4: 解析失败\n - 5: 文件上传中\n - 6: 文件上传成功\n - 7: 删除中\n - 8: 解除了助手绑定（在助手中已删除）\n - 9: 重新解析中", "title": "知识库文档状态"}, "tanliveaiExternalSourceUser": {"type": "object", "properties": {"user_id": {"type": "string", "title": "用户ID"}, "nickname": {"type": "string", "title": "用户昵称"}, "avatar": {"type": "string", "title": "用户头像"}, "auth_state": {"$ref": "#/definitions/aiExternalSourceUserAuthState", "title": "用户状态"}, "auth_source": {"type": "string", "title": "用户授权来源(xw qq)"}}}, "tanliveaiFeedbackReference": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "参考文献ID"}, "feedback_id": {"type": "string", "format": "uint64", "title": "反馈ID"}, "type": {"$ref": "#/definitions/aiReferenceType", "title": "类型"}, "url": {"type": "string", "title": "URL链接"}, "text": {"type": "string", "title": "文本内容"}, "file_path": {"type": "string", "title": "文件路径"}, "file_name": {"type": "string", "title": "文件名称"}, "create_by": {"type": "string", "format": "uint64", "title": "创建人"}, "create_date": {"type": "string", "format": "date-time", "title": "创建时间"}, "update_by": {"type": "string", "format": "uint64", "title": "更新人"}, "update_date": {"type": "string", "format": "date-time", "title": "更新时间"}}, "title": "反馈参考文献"}, "tanliveaiSearchCollectionItem": {"type": "object", "properties": {"text": {"type": "string"}, "question": {"type": "string"}, "score": {"type": "number", "format": "float"}, "file_name": {"type": "string"}, "url": {"type": "string"}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "update_by": {"$ref": "#/definitions/aiOperator"}, "id": {"type": "string"}, "type": {"$ref": "#/definitions/aiSearchCollectionType", "title": "召回类型"}, "is_related": {"type": "boolean", "title": "是否相关"}, "doc_type": {"$ref": "#/definitions/aiDocType", "title": "文件类型"}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}, "doc_id": {"type": "string", "format": "uint64"}, "ref_name": {"type": "string"}, "ref_url": {"type": "string"}, "doc_name": {"type": "string"}}}, "tanlivebff_webaiAssistant": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "name_en": {"type": "string"}, "website_route": {"type": "string"}, "search_debug": {"type": "boolean"}, "threshold": {"type": "number", "format": "float"}, "top_n": {"type": "integer", "format": "int32"}, "text_weight": {"type": "number", "format": "float"}, "text_recall_top_n": {"type": "integer", "format": "int32", "title": "关键词召回条数"}, "clean_chunks": {"type": "boolean"}}}, "tanlivebff_webaiChat": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "title": {"type": "string"}, "create_by": {"type": "string", "format": "uint64"}, "create_date": {"type": "string", "format": "date-time"}, "is_shared": {"type": "boolean"}}}, "tanlivebff_webaiChatDetail": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "title": {"type": "string"}, "messages": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiEventChatMessage"}, "title": "地区\n  tanlive.base.Region region = 4;"}, "create_by": {"$ref": "#/definitions/iamUserInfo"}, "finish_date": {"type": "string", "format": "date-time"}, "create_date": {"type": "string", "format": "date-time"}, "chat_type": {"$ref": "#/definitions/aiChatType"}, "chat_state": {"$ref": "#/definitions/aiChatCurrentState"}, "support_type": {"$ref": "#/definitions/aiChatSupportType", "title": "当前服务状态"}, "assistant_avatar": {"type": "string", "title": "微信客服助手头像"}, "records": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatSendRecordInfo"}, "title": "非web端时通过次字段返回消息详情"}, "assistant_id": {"type": "string", "format": "uint64"}}}, "tanlivebff_webaiExternalSourceUser": {"type": "object", "properties": {"hash_user_id": {"type": "string", "title": "用户ID"}, "nickname": {"type": "string", "title": "用户昵称"}, "avatar": {"type": "string", "title": "用户头像"}, "auth_state": {"$ref": "#/definitions/aiExternalSourceUserAuthState", "title": "用户状态"}, "auth_source": {"type": "string", "title": "用户授权来源(xw qq)"}}}, "tanlivebff_webaiFeedbackReference": {"type": "object", "properties": {"type": {"$ref": "#/definitions/aiReferenceType", "title": "文献类型"}, "url": {"type": "string", "title": "跳转链接（type为1时必填）"}, "text": {"type": "string", "title": "文本（type为2时必填）"}, "file_path": {"type": "string", "title": "文件路径（type为3时必填）"}, "file_name": {"type": "string", "title": "文件名称（type为3时必填）"}}, "title": "反馈参考文献"}, "tanlivebff_webaiReqAcceptFeedback": {"type": "object", "properties": {"feedback_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "反馈ID"}, "assistant_id": {"type": "string", "format": "uint64"}}}, "tanlivebff_webaiReqAuthTencentCode": {"type": "object", "properties": {"code": {"type": "string"}, "path": {"type": "string"}}}, "tanlivebff_webaiReqContinueChatFromShare": {"type": "object", "properties": {"share_id": {"type": "string", "title": "分享ID"}, "chat_type": {"$ref": "#/definitions/aiChatType"}, "assistant_id": {"type": "string", "format": "uint64", "title": "助手ID"}}, "title": "从分享继续聊天请求"}, "tanlivebff_webaiReqConvertCustomLabel": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "target": {"$ref": "#/definitions/aiCustomLabel"}, "dry_run": {"type": "boolean"}}}, "tanlivebff_webaiReqCreateChatShare": {"type": "object", "properties": {"chat_id": {"type": "string", "format": "uint64", "title": "会话ID"}, "message_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "要分享的消息ID列表"}, "share_type": {"$ref": "#/definitions/tanlivebff_webaiShareType", "title": "分享类型"}, "expire_days": {"type": "integer", "format": "int32", "title": "过期天数，0表示永久有效"}}, "title": "创建聊天分享请求"}, "tanlivebff_webaiReqCreateDocQuery": {"type": "object", "properties": {"doc": {"$ref": "#/definitions/aiReqListTextFiles"}, "qa": {"$ref": "#/definitions/tanlivebff_webaiReqListQA"}}}, "tanlivebff_webaiReqCreateDocShareConfigReceiverAssistant": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64"}, "receiver_state": {"$ref": "#/definitions/aiDocShareAcceptState"}, "user_shares": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiReqCreateDocShareConfigReceiverAssistantUserShare"}}, "other_state": {"$ref": "#/definitions/aiDocShareState"}}}, "tanlivebff_webaiReqCreateDocShareConfigReceiverUserTeam": {"type": "object", "properties": {"team_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "user_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "tanlivebff_webaiReqCreateDocShareConfigSender": {"type": "object", "properties": {"share_assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "share_user_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "share_team_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "tanlivebff_webaiReqCreateNebulaTask": {"type": "object", "properties": {"lang": {"type": "string"}, "assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "query_assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "filter_text": {"type": "string"}, "has_k": {"type": "boolean"}, "has_q": {"type": "boolean"}, "clustering_method": {"type": "string"}, "min_samples_range": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "eps_range": {"type": "array", "items": {"type": "number", "format": "float"}}, "n_clusters_range": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}, "tanlivebff_webaiReqCreateQA": {"type": "object", "properties": {"question": {"type": "string"}, "answer": {"type": "string"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}, "title": "贡献者"}, "state": {"$ref": "#/definitions/tanliveaiDocState"}, "assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}, "share_assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "match_patterns": {"type": "array", "items": {"$ref": "#/definitions/aiDocMatchPattern"}, "title": "匹配模式"}}}, "tanlivebff_webaiReqCreateTencentDocAuthUrl": {"type": "object"}, "tanlivebff_webaiReqDelTencentDocAuth": {"type": "object", "properties": {"hash_user_id": {"type": "string"}}}, "tanlivebff_webaiReqDeleteCustomLabels": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "tanlivebff_webaiReqDescribeChatRegionCode": {"type": "object", "properties": {"region": {"$ref": "#/definitions/baseRegion", "title": "地区"}, "assistant_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "tanlivebff_webaiReqDescribeDocTab": {"type": "object"}, "tanlivebff_webaiReqDescribeExportTasks": {"type": "object", "properties": {"type": {"type": "array", "items": {"$ref": "#/definitions/aiExportTaskType"}}, "operation_type": {"$ref": "#/definitions/aiTaskOperationType"}}}, "tanlivebff_webaiReqDescribeFeedbackRegionCode": {"type": "object", "properties": {"region": {"$ref": "#/definitions/baseRegion", "title": "地区"}}}, "tanlivebff_webaiReqDescribeMyDoc": {"type": "object"}, "tanlivebff_webaiReqDescribeNebulaData": {"type": "object", "properties": {"content_hash": {"type": "array", "items": {"type": "string"}}, "id": {"type": "string", "format": "uint64"}}}, "tanlivebff_webaiReqDescribeNebulaProjection": {"type": "object", "properties": {"uuid": {"type": "string"}, "query": {"type": "string"}, "algorithm": {"type": "string"}}}, "tanlivebff_webaiReqDescribeNebulaTask": {"type": "object", "properties": {"uuid": {"type": "string"}, "lang": {"type": "string"}, "assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "query_assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "tanlivebff_webaiReqDescribeNebulaTaskList": {"type": "object", "properties": {"offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "to_model": {"type": "string"}, "uuid": {"type": "array", "items": {"type": "string"}}}}, "tanlivebff_webaiReqDescribeTencentDocTask": {"type": "object"}, "tanlivebff_webaiReqGetChatDetail": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "chat_id"}, "offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "keyword": {"type": "string", "title": "消息内容搜索关键词"}, "send_range": {"$ref": "#/definitions/baseTimeRange", "title": "创建时间区间"}, "question_id": {"type": "string", "format": "uint64", "title": "问题ID"}}}, "tanlivebff_webaiReqGetChunkDocTasks": {"type": "object", "properties": {"doc_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "文档ID"}}}, "tanlivebff_webaiReqGetCustomLabelValueTopN": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}}}, "tanlivebff_webaiReqGetDocChunks": {"type": "object", "properties": {"doc_id": {"type": "string", "format": "uint64", "title": "文档ID"}, "assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "助手ID"}}}, "tanlivebff_webaiReqGetDocEmbeddingModels": {"type": "object", "properties": {"doc_id": {"type": "string", "format": "uint64", "title": "文档ID"}}}, "tanlivebff_webaiReqGetFeedbackLogs": {"type": "object", "properties": {"region": {"$ref": "#/definitions/baseRegion", "title": "地区"}, "feedback_id": {"type": "string", "format": "uint64", "title": "反馈ID"}, "create_identity": {"$ref": "#/definitions/baseIdentity", "title": "操作人"}, "action": {"type": "array", "items": {"$ref": "#/definitions/aiFeedbackAction"}, "title": "操作类型"}, "create_date_range": {"$ref": "#/definitions/baseTimeRange", "title": "操作时间区间"}, "offset": {"type": "integer", "format": "int64", "title": "分页偏移量"}, "limit": {"type": "integer", "format": "int64", "title": "分页大小"}, "order_by": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/baseOrderBy"}, "title": "排序"}}}, "tanlivebff_webaiReqGetFeedbacks": {"type": "object", "properties": {"region": {"$ref": "#/definitions/baseRegion", "title": "地区"}, "create_by": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "上传用户ID"}, "state": {"type": "array", "items": {"$ref": "#/definitions/aiFeedbackState"}, "title": "状态筛选"}, "create_date_range": {"$ref": "#/definitions/baseTimeRange", "title": "创建时间区间"}, "handled_at_range": {"$ref": "#/definitions/baseTimeRange", "title": "处理时间区间"}, "offset": {"type": "integer", "format": "int64", "title": "分页偏移量"}, "limit": {"type": "integer", "format": "int64", "title": "分页大小"}, "order_by": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/baseOrderBy"}, "title": "排序"}, "assistant_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "助手ID"}, "region_codes": {"type": "array", "items": {"type": "string"}}}}, "tanlivebff_webaiReqGetQaTip": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "文档ID"}}, "title": "获取QA知识提示请求"}, "tanlivebff_webaiReqGetTextFileTip": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "文档ID"}}, "title": "获取文件文本知识提示请求"}, "tanlivebff_webaiReqImportTencentDoc": {"type": "object", "properties": {"file_ids": {"type": "array", "items": {"type": "string"}}, "hash_user_id": {"type": "string"}, "path": {"type": "string"}}}, "tanlivebff_webaiReqImportTencentDocWebClip": {"type": "object", "properties": {"after_time": {"type": "string", "format": "date-time"}, "hash_user_id": {"type": "string"}}}, "tanlivebff_webaiReqListAssistant": {"type": "object", "properties": {"offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "type": {"$ref": "#/definitions/aiChatType"}, "name": {"type": "string", "title": "搜索名称关键词"}, "language": {"type": "string", "title": "语言，为空会从header里取"}}}, "tanlivebff_webaiReqListAssistantCanShareDoc": {"type": "object", "properties": {"doc_id": {"type": "string", "format": "uint64"}, "name": {"type": "string", "title": "搜索名称关键词"}, "language": {"type": "string", "title": "语言，为空会从header里取"}}}, "tanlivebff_webaiReqListChat": {"type": "object", "properties": {"offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "order_by": {"type": "array", "items": {"type": "string"}}, "filter": {"$ref": "#/definitions/tanlivebff_webaiReqListChatFilter"}, "create_date_range": {"$ref": "#/definitions/baseTimeRange", "title": "创建时间区间"}, "update_date_range": {"$ref": "#/definitions/baseTimeRange", "title": "处理时间区间"}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiLabelFilter"}, "title": "自定义标签kv对"}, "assistant_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "助手id"}, "order_by_label": {"$ref": "#/definitions/aiOrderByLabel", "title": "自定义标签排序，只能当个标签排序"}, "ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "筛选ids"}}}, "tanlivebff_webaiReqListChatFilter": {"type": "object", "properties": {"user_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "用户id"}, "chat_titles": {"type": "array", "items": {"type": "string"}, "title": "对话内容"}, "region": {"$ref": "#/definitions/baseRegion", "title": "地区"}, "chat_type": {"$ref": "#/definitions/aiChatType"}, "nicknames": {"type": "array", "items": {"type": "string"}}, "rating_scale": {"type": "array", "items": {"$ref": "#/definitions/aiRatingScale"}}, "reject_job_result": {"type": "integer", "format": "int64", "title": "筛选审核 1 违规 2 敏感 3 正常"}, "region_codes": {"type": "array", "items": {"type": "string"}, "title": "国家或地区编码"}, "is_manual": {"type": "integer", "format": "int32", "title": "是否转过人工服务 1 否 2 是"}}}, "tanlivebff_webaiReqListChatLiveAgent": {"type": "object", "properties": {"chat_id": {"type": "string", "format": "uint64"}}}, "tanlivebff_webaiReqListContributor": {"type": "object", "properties": {"search": {"type": "string"}, "type": {"$ref": "#/definitions/aiListDocFilterType"}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}}}, "tanlivebff_webaiReqListCustomLabel": {"type": "object", "properties": {"offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "object_type": {"$ref": "#/definitions/aiCustomLabelObjectType"}, "id": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "tanlivebff_webaiReqListDocShareConfigReceiverAssistant": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64"}}}, "tanlivebff_webaiReqListDocShareConfigReceiverUserTeam": {"type": "object"}, "tanlivebff_webaiReqListExternalSourceUser": {"type": "object"}, "tanlivebff_webaiReqListMyAssistantIds": {"type": "object"}, "tanlivebff_webaiReqListQA": {"type": "object", "properties": {"shared_state": {"type": "array", "items": {"$ref": "#/definitions/aiDocSharedState"}}, "state": {"$ref": "#/definitions/tanliveaiDocState"}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "update_by": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiOperator"}}, "order_by": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/baseOrderBy"}}, "offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "search": {"type": "string"}, "assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "助手id"}, "excluded_assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "不在助手中"}, "group_repeated": {"type": "boolean", "title": "重复 doc 紧凑排序"}, "show_contributor": {"type": "integer", "format": "int64"}, "share_assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "分享的助手"}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiLabelFilter"}}, "order_by_label": {"$ref": "#/definitions/aiOrderByLabel", "title": "自定义标签排序，只能当个标签排序"}, "ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "tql_expression": {"type": "string", "title": "TQL表达式（高级搜索）"}, "create_by": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiOperator"}}, "match_patterns": {"type": "array", "items": {"$ref": "#/definitions/aiDocMatchPattern"}, "title": "匹配模式"}, "tip_filter": {"$ref": "#/definitions/tanlivebff_webaiReqListQATipFilter"}, "with_tips": {"type": "boolean", "title": "是否返回知识提示"}, "share_team_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "分享的团队id"}, "share_user_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "分享的用户id"}, "embedding_state": {"$ref": "#/definitions/aiDocEmbeddingState", "title": "向量状态"}}}, "tanlivebff_webaiReqListQATipFilter": {"type": "object", "properties": {"warning": {"type": "boolean"}}, "title": "知识提示过滤条件，用来筛选问题超长等问题的记录"}, "tanlivebff_webaiReqListSharedAssistant": {"type": "object", "properties": {"search": {"type": "string"}, "type": {"$ref": "#/definitions/aiListDocFilterType"}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}}}, "tanlivebff_webaiReqListSharedTeam": {"type": "object", "properties": {"search": {"type": "string"}, "type": {"$ref": "#/definitions/aiListDocFilterType"}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}}}, "tanlivebff_webaiReqListSharedUser": {"type": "object", "properties": {"search": {"type": "string"}, "type": {"$ref": "#/definitions/aiListDocFilterType"}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}}}, "tanlivebff_webaiReqListTeamCanShareDoc": {"type": "object", "properties": {"name": {"type": "string", "title": "搜索名称关键词"}, "offset": {"type": "string", "format": "uint64", "title": "分页偏移量"}, "limit": {"type": "string", "format": "uint64", "title": "分页大小"}, "doc_id": {"type": "string", "format": "uint64", "title": "文档ID"}}}, "tanlivebff_webaiReqListUserCanShareDoc": {"type": "object", "properties": {"name": {"type": "string", "title": "搜索名称关键词"}, "doc_id": {"type": "string", "format": "uint64", "title": "文档ID"}}}, "tanlivebff_webaiReqListeDocShareConfigSender": {"type": "object", "properties": {"name": {"type": "string", "title": "搜索名称关键词"}, "language": {"type": "string", "title": "语言，为空会从header里取"}}}, "tanlivebff_webaiReqModifyDocTab": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "type": {"type": "integer", "format": "int64"}}}, "tanlivebff_webaiReqRateAiAnswer": {"type": "object", "properties": {"message_id": {"type": "string", "format": "uint64", "title": "消息ID"}, "rating_scale": {"$ref": "#/definitions/aiRatingScale", "title": "评价等级"}}}, "tanlivebff_webaiReqReimportTencentDoc": {"type": "object", "properties": {"doc_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}}, "title": "重新导入腾讯文档(doc_id 知识库的文档id)"}, "tanlivebff_webaiReqReparseTextFiles": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "parse_mode": {"$ref": "#/definitions/aiDocParseMode"}, "query_id": {"type": "string", "format": "uint64"}}}, "tanlivebff_webaiReqSearchCollection": {"type": "object", "properties": {"search": {"type": "string"}, "assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "doc_type": {"$ref": "#/definitions/aiDocType"}, "threshold": {"type": "number", "format": "float"}, "top_n": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "limit": {"type": "integer", "format": "int64"}, "text_weight": {"type": "number", "format": "float"}, "text_recall_top_n": {"type": "integer", "format": "int64"}, "text_recall_query": {"$ref": "#/definitions/aiTextRecallQuery", "title": "关键词召回匹配目标"}, "text_recall_pattern": {"$ref": "#/definitions/aiTextRecallPattern", "title": "关键词召回模式"}, "text_recall_slop": {"type": "integer", "format": "int32", "title": "关键词召回允许平移距离允许平移距离"}, "clean_chunks": {"type": "boolean"}, "temperature": {"type": "number", "format": "float"}}}, "tanlivebff_webaiReqStopQuestionReply": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "hash_id": {"type": "string"}, "question_id": {"type": "string", "format": "uint64"}}}, "tanlivebff_webaiReqSwitchChatLiveAgent": {"type": "object", "properties": {"live_agent_id": {"type": "string", "format": "uint64", "title": "人工客服id"}, "chat_id": {"type": "string", "format": "uint64", "title": "会话id"}}}, "tanlivebff_webaiReqUpdateQA": {"type": "object", "properties": {"question": {"type": "string"}, "answer": {"type": "string"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "states": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocAssistantState"}}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "id": {"type": "string", "format": "uint64"}, "mask": {"type": "string"}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}, "title": "标签"}, "match_patterns": {"type": "array", "items": {"$ref": "#/definitions/aiDocMatchPattern"}, "title": "匹配模式"}}}, "tanlivebff_webaiReqUpdateTextFile": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "file_name": {"type": "string"}, "text": {"type": "string"}, "url": {"type": "string"}, "states": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocAssistantState"}}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "ugc_type": {"$ref": "#/definitions/baseDataType"}, "ugc_id": {"type": "string", "format": "uint64"}, "parsed_url": {"type": "string", "title": "解析后的文件url"}, "mask": {"type": "string"}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}, "title": "标签"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "download_as_ref": {"$ref": "#/definitions/aiDocFileDownloadAsRef", "title": "是否可以作为参考资料下载"}}}, "tanlivebff_webaiRspAuthTencentCode": {"type": "object"}, "tanlivebff_webaiRspContinueChatFromShare": {"type": "object", "properties": {"chat_id": {"type": "string", "format": "uint64", "title": "新会话ID"}, "title": {"type": "string", "title": "会话标题"}, "assistant_id": {"type": "string", "format": "uint64", "title": "助手ID"}}, "title": "从分享继续聊天响应"}, "tanlivebff_webaiRspConvertCustomLabel": {"type": "object", "properties": {"reserved": {"type": "integer", "format": "int64"}, "deleted": {"type": "integer", "format": "int64"}}}, "tanlivebff_webaiRspCreateChatShare": {"type": "object", "properties": {"share_id": {"type": "string", "title": "分享ID"}}, "title": "创建聊天分享响应"}, "tanlivebff_webaiRspCreateDocQuery": {"type": "object", "properties": {"query_id": {"type": "string", "format": "uint64"}, "is_empty": {"type": "boolean"}, "total_count": {"type": "integer", "format": "int64"}}}, "tanlivebff_webaiRspCreateNebulaTask": {"type": "object", "properties": {"uuid": {"type": "string"}}}, "tanlivebff_webaiRspCreateTencentDocAuthUrl": {"type": "object", "properties": {"url": {"type": "string"}}}, "tanlivebff_webaiRspDescribeChatRegionCode": {"type": "object", "properties": {"region_codes": {"type": "array", "items": {"type": "string"}}}}, "tanlivebff_webaiRspDescribeDocTab": {"type": "object", "properties": {"tabs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiRspDescribeDocTabDocTab"}}}}, "tanlivebff_webaiRspDescribeDocTabDocTab": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "type": {"type": "integer", "format": "int64"}, "is_show": {"type": "boolean"}, "has_expired": {"type": "boolean"}}}, "tanlivebff_webaiRspDescribeExportTasks": {"type": "object", "properties": {"tasks": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiExportTask"}}}}, "tanlivebff_webaiRspDescribeFeedbackRegionCode": {"type": "object", "properties": {"region_codes": {"type": "array", "items": {"type": "string"}}}}, "tanlivebff_webaiRspDescribeMyDoc": {"type": "object", "properties": {"file_ids": {"type": "array", "items": {"type": "string"}}}}, "tanlivebff_webaiRspDescribeNebulaData": {"type": "object", "properties": {"content": {"type": "array", "items": {"type": "string"}}, "query_name": {"type": "string"}}}, "tanlivebff_webaiRspDescribeNebulaProjection": {"type": "object", "properties": {"projection": {"type": "array", "items": {"type": "number", "format": "double"}}}}, "tanlivebff_webaiRspDescribeNebulaTask": {"type": "object", "properties": {"uuid": {"type": "string"}, "state": {"type": "integer", "format": "int64"}, "end_date": {"type": "string"}, "start_date": {"type": "string"}, "calcu_result": {"type": "array", "items": {"type": "string"}}, "filter_text": {"type": "string"}, "cluster_list": {"type": "string"}, "connect_info": {"type": "string"}}}, "tanlivebff_webaiRspDescribeNebulaTaskList": {"type": "object", "properties": {"tasks": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiRspDescribeNebulaTaskListTask"}}, "total_count": {"type": "integer", "format": "int64"}, "unread_num": {"type": "integer", "format": "int64"}}}, "tanlivebff_webaiRspDescribeNebulaTaskListTask": {"type": "object", "properties": {"uuid": {"type": "string"}, "state": {"type": "integer", "format": "int64"}, "end_date": {"type": "string"}, "start_date": {"type": "string"}, "lang": {"type": "string"}, "filter_text": {"type": "string"}, "is_read": {"type": "integer", "format": "int64"}}}, "tanlivebff_webaiRspDescribeTencentDocTask": {"type": "object", "properties": {"is_running": {"type": "boolean"}}}, "tanlivebff_webaiRspGetAssistantOptions": {"type": "object", "properties": {"chat_model": {"type": "array", "items": {"type": "string"}, "title": "对话模型"}, "chat_or_sql_model": {"type": "array", "items": {"type": "string"}, "title": "ChatOrSql模型"}, "graph_parse_mode": {"type": "array", "items": {"type": "string"}, "title": "解析图谱模型"}, "search_engine": {"type": "array", "items": {"type": "string"}, "title": "搜索引擎"}, "interactive_code": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiInteractiveCodeOption"}, "title": "互动暗号"}, "visible_chain": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiVisibleChainOption"}, "title": "链路查询"}, "ask_suggestion_model": {"type": "array", "items": {"type": "string"}, "title": "问题建议模型"}, "embedding_model": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiEmbeddingModelOption"}, "title": "向量化模型"}, "chat_model_v2": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatModelOption"}, "title": "对话模型"}, "search_engine_v2": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiSearchEngineOption"}, "title": "对话模型"}, "quick_actions": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiQuickAction"}, "title": "快捷指令"}, "mini_white_url": {"type": "array", "items": {"type": "string"}, "title": "小程序URL白名单"}}}, "tanlivebff_webaiRspGetChatDetail": {"type": "object", "properties": {"chat_detail": {"$ref": "#/definitions/tanlivebff_webaiChatDetail"}, "totalCount": {"type": "integer", "format": "int64"}}}, "tanlivebff_webaiRspGetChunkDocTasks": {"type": "object", "properties": {"tasks": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocChunkTask"}, "title": "任务列表"}}}, "tanlivebff_webaiRspGetCustomLabelValueTopN": {"type": "object", "properties": {"values": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiLabelValue"}}}}, "tanlivebff_webaiRspGetDocChunks": {"type": "object", "properties": {"assistant_chunks": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiAssistantChunks"}, "title": "助手的分段列表"}}}, "tanlivebff_webaiRspGetDocEmbeddingModels": {"type": "object", "properties": {"embedding_models": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiEmbeddingModelCount"}, "title": "向量化模型列表"}}}, "tanlivebff_webaiRspGetFeedbackLogs": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiFullFeedbackLog"}, "title": "日志列表"}, "total_count": {"type": "integer", "format": "int64", "title": "总数"}, "user_cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/iamUserCard"}, "title": "用户卡片列表"}, "team_cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/teamTeamCard"}, "title": "团队卡片列表"}}}, "tanlivebff_webaiRspGetFeedbacks": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspGetFeedbacksItem"}, "title": "反馈列表"}, "total_count": {"type": "integer", "format": "int64", "title": "总数"}, "user_cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/iamUserCard"}, "title": "用户卡片列表"}, "team_cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/teamTeamCard"}, "title": "团队卡片列表"}}}, "tanlivebff_webaiRspGetQaTip": {"type": "object", "properties": {"question_over_size": {"type": "boolean", "title": "问题超长提示"}, "repeated": {"type": "array", "items": {"type": "string"}, "title": "内容重复信息"}}, "title": "获取QA知识提示响应"}, "tanlivebff_webaiRspGetTextFileTip": {"type": "object", "properties": {"table_over_size": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiTextFileTipTableOverSize"}, "title": "表头过长的表格"}, "state": {"$ref": "#/definitions/tanliveaiDocState", "title": "解析状态"}, "repeated": {"type": "array", "items": {"type": "string"}, "title": "内容重复信息"}}, "title": "获取文件文本知识提示响应"}, "tanlivebff_webaiRspImportTencentDoc": {"type": "object", "properties": {"uuid": {"type": "string"}}}, "tanlivebff_webaiRspImportTencentDocWebClip": {"type": "object", "properties": {"docs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiTencentDoc"}}}}, "tanlivebff_webaiRspListAssistant": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64"}, "assistants": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveaiAssistant"}}}}, "tanlivebff_webaiRspListAssistantCanShareDoc": {"type": "object", "properties": {"assistants": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiRspListAssistantCanShareDocSharedAssistant"}}}}, "tanlivebff_webaiRspListAssistantCanShareDocSharedAssistant": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "name_en": {"type": "string"}, "is_selected": {"type": "boolean"}}}, "tanlivebff_webaiRspListChat": {"type": "object", "properties": {"chats": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatInfo"}}, "total_count": {"type": "integer", "format": "int64"}}}, "tanlivebff_webaiRspListChatLiveAgent": {"type": "object", "properties": {"chatLiveAgent": {"$ref": "#/definitions/aiChatLiveAgentInfo"}}}, "tanlivebff_webaiRspListContributor": {"type": "object", "properties": {"contributors": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}}}, "tanlivebff_webaiRspListCustomLabel": {"type": "object", "properties": {"labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "total_count": {"type": "integer", "format": "int64"}}}, "tanlivebff_webaiRspListDocShareConfigReceiverAssistant": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64"}, "receiver_state": {"$ref": "#/definitions/aiDocShareAcceptState"}, "user_shares": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiRspListDocShareConfigReceiverAssistantUserShare"}}, "other_state": {"$ref": "#/definitions/aiDocShareState"}}}, "tanlivebff_webaiRspListDocShareConfigReceiverAssistantUserShare": {"type": "object", "properties": {"users": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspListDocShareConfigReceiverAssistantMembers"}}, "teams": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspListDocShareConfigReceiverAssistantMembers"}}, "state": {"$ref": "#/definitions/aiDocShareState"}, "group_id": {"type": "string", "format": "uint64"}}}, "tanlivebff_webaiRspListDocShareConfigReceiverUserTeam": {"type": "object", "properties": {"teams": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspListDocShareConfigReceiverUserTeamMembers"}}, "users": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspListDocShareConfigReceiverUserTeamMembers"}}}}, "tanlivebff_webaiRspListExternalSourceUser": {"type": "object", "properties": {"users": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiExternalSourceUser"}}}}, "tanlivebff_webaiRspListMyAssistantIds": {"type": "object", "properties": {"share_assistant_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "tanlivebff_webaiRspListQA": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCollectionQA"}}}}, "tanlivebff_webaiRspListSharedAssistant": {"type": "object", "properties": {"assistants": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiAssistant"}}}}, "tanlivebff_webaiRspListSharedTeam": {"type": "object", "properties": {"teams": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspListSharedTeamSharedTeam"}}}}, "tanlivebff_webaiRspListSharedUser": {"type": "object", "properties": {"users": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspListSharedUserSharedUser"}}}}, "tanlivebff_webaiRspListTeamCanShareDoc": {"type": "object", "properties": {"teams": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiRspListTeamCanShareDocTeams"}}}}, "tanlivebff_webaiRspListTeamCanShareDocTeams": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "name_en": {"type": "string"}, "is_selected": {"type": "boolean"}}}, "tanlivebff_webaiRspListUserCanShareDoc": {"type": "object", "properties": {"users": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiRspListUserCanShareDocUsers"}}}}, "tanlivebff_webaiRspListUserCanShareDocUsers": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "is_selected": {"type": "boolean"}}}, "tanlivebff_webaiRspListeDocShareConfigSender": {"type": "object", "properties": {"assistants": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspListeDocShareConfigSenderSharedAssistant"}}, "users": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspListeDocShareConfigSenderSharedUserTeam"}}, "teams": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspListeDocShareConfigSenderSharedUserTeam"}}}}, "tanlivebff_webaiRspReimportTencentDoc": {"type": "object", "properties": {"failed": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiRspReimportTencentDocFailInfo"}}}}, "tanlivebff_webaiRspReimportTencentDocFailInfo": {"type": "object", "properties": {"doc_id": {"type": "string", "format": "uint64"}, "file_name": {"type": "string"}, "user": {"$ref": "#/definitions/tanliveaiExternalSourceUser"}}}, "tanlivebff_webaiRspReparseTextFiles": {"type": "object", "properties": {"async": {"type": "boolean"}}}, "tanlivebff_webaiRspSearchCollection": {"type": "object", "properties": {"start": {"type": "string", "format": "date-time"}, "end": {"type": "string", "format": "date-time"}, "total_count": {"type": "integer", "format": "int64"}, "match_pattern": {"$ref": "#/definitions/aiDocMatchPattern"}, "item": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiSearchCollectionItem"}}}}, "tanlivebff_webaiRspStopQuestionReply": {"type": "object", "properties": {"message": {"$ref": "#/definitions/aiChatMessage"}}}, "tanlivebff_webaiSearchCollectionItem": {"type": "object", "properties": {"text": {"type": "string"}, "question": {"type": "string"}, "score": {"type": "number", "format": "float"}, "file_name": {"type": "string"}, "url": {"type": "string"}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "update_by": {"$ref": "#/definitions/aiDocOperator"}, "id": {"type": "string"}, "type": {"$ref": "#/definitions/aiSearchCollectionType", "title": "召回类型"}, "is_related": {"type": "boolean", "title": "是否相关"}, "doc_type": {"$ref": "#/definitions/aiDocType", "title": "文件类型"}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}, "doc_id": {"type": "string", "format": "uint64"}, "doc_name": {"type": "string"}}}, "tanlivebff_webaiShareStatus": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "description": "- 1: 有效\n - 2: 已失效（手动失效）\n - 3: 已过期", "title": "分享状态"}, "tanlivebff_webaiShareType": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "description": "- 1: 链接分享\n - 2: 二维码分享\n - 3: 小程序码分享", "title": "分享类型"}, "tanlivebff_webaiTencentDoc": {"type": "object", "properties": {"title": {"type": "string"}, "url": {"type": "string"}, "file_name": {"type": "string"}, "file_id": {"type": "string"}, "file_type": {"type": "string"}, "file_create_user": {"type": "string"}, "file_owner_name": {"type": "string"}, "file_create_time": {"type": "string"}, "file_modify_time": {"type": "string"}, "file_browse_time": {"type": "string"}, "file_url": {"type": "string"}}}, "teamTeamCard": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "团队ID"}, "short_name": {"type": "string", "title": "简称"}, "full_name": {"type": "string", "title": "主体名称"}, "is_verified": {"type": "boolean", "title": "是否认证"}, "brief_intro": {"type": "string", "title": "一句话介绍"}, "level": {"$ref": "#/definitions/teamTeamLevel", "title": "共创等级"}, "logo_url": {"type": "string", "title": "团队LOGO"}, "is_published": {"type": "boolean", "title": "是否已发布"}}, "title": "团队卡片"}, "teamTeamLevel": {"type": "integer", "format": "int32", "enum": [1, 2, 3], "description": "- 1: CONTRIBUTOR\n - 2: COMMITTER\n - 3: MAIN<PERSON>INER", "title": "团队共创等级"}}}