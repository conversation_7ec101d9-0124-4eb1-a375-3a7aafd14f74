{"consumes": ["application/json"], "produces": ["application/json"], "swagger": "2.0", "info": {"title": "tanlive/bff-web/notify/bff.proto", "version": "version not set"}, "paths": {"/ai/accept_feedback": {"post": {"tags": ["AiBff"], "summary": "采用用户反馈", "operationId": "AiBff_AcceptFeedback", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqAcceptFeedback"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspAcceptFeedback"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/auto_chunk_doc": {"post": {"tags": ["AiBff"], "summary": "自动文档分段", "operationId": "AiBff_AutoChunkDoc", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqAutoChunkDoc"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspAutoChunkDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/batch_user_assistant_limit": {"post": {"tags": ["AiBff"], "summary": "批量获取用户对应助手的限制情况", "operationId": "AiBff_BatchUserAssistantLimit", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqBatchUserAssistantLimit"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspBatchUserAssistantLimit"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/bind_mini_program_normal_account": {"post": {"tags": ["AiBff"], "summary": "绑定用户小程序普通账号", "operationId": "AiBff_BindMiniProgramNormalAccount", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqBindMiniProgramNormalAccount"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspBindMiniProgramNormalAccount"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/bind_mini_program_phone_account": {"post": {"tags": ["AiBff"], "summary": "绑定用户小程序手机号", "operationId": "AiBff_BindMiniProgramPhoneAccount", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqBindMiniProgramPhoneAccount"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspBindMiniProgramPhoneAccount"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/bind_mini_program_uniid": {"post": {"tags": ["AiBff"], "summary": "绑定用户小程序unionid", "operationId": "AiBff_BindMiniProgramUniID", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqBindMiniProgramUniID"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/bind_once_user_mini_program": {"post": {"tags": ["AiBff"], "summary": "一键绑定用户和小程序unionid", "operationId": "AiBff_BindOnceUserMiniProgram", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqBindOnceUserMiniProgram"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspBindOnceUserMiniProgram"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/bind_unitoke_by_code": {"post": {"tags": ["AiBff"], "summary": "绑定用户小程序临时映射unionid", "operationId": "AiBff_BindUnitokenByCode", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqBindUnitokenByCode"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/bind_user_phone_by_code": {"post": {"tags": ["AiBff"], "summary": "绑定手机号通过小程序code", "operationId": "AiBff_BindUserPhoneByCode", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqBindUserPhoneByCode"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspBindUserPhoneByCode"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/chat_share_continue": {"post": {"tags": ["AiBff"], "summary": "从分享继续聊天", "operationId": "AiBff_ContinueChatFromShare", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqContinueChatFromShare"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspContinueChatFromShare"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/chat_share_create": {"post": {"tags": ["AiBff"], "summary": "创建聊天分享", "operationId": "AiBff_CreateChatShare", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqCreateChatShare"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspCreateChatShare"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/chat_share_get": {"post": {"tags": ["AiGuestBff"], "summary": "获取公开分享详情", "operationId": "AiGuestBff_GetPublicChatShare", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetPublicChatShare"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetPublicChatShare"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/auth_tencent_code": {"post": {"tags": ["AiBff"], "summary": "腾讯文档授权code处理", "operationId": "AiBff_AuthTencentCode", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqAuthTencentCode"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspAuthTencentCode"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/batch_update_docs": {"post": {"tags": ["AiBff"], "summary": "批量更新doc的特定字段值", "operationId": "AiBff_BatchUpdateDocAttr", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqBatchUpdateDocAttr"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspBatchUpdateDocAttr"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/clone_doc": {"post": {"tags": ["AiBff"], "summary": "克隆QA/文本/文件", "operationId": "AiBff_CloneDoc", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCloneDoc"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCloneDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/create_doc_query": {"post": {"tags": ["AiBff"], "operationId": "AiBff_CreateDocQuery", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqCreateDocQuery"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspCreateDocQuery"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/create_gtb_doc_text": {"post": {"tags": ["AiBff"], "summary": "批量导入绿技行", "operationId": "AiBff_CreateGTBDocText", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateGTBDocText"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateGTBDocText"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/create_qas": {"post": {"tags": ["AiBff"], "summary": "创建QA", "operationId": "AiBff_CreateQAs", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateQAs"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateQAs"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/create_tencent_doc_auth_url": {"post": {"tags": ["AiBff"], "summary": "创建腾讯文档授权链接", "operationId": "AiBff_CreateTencentDocAuthUrl", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqCreateTencentDocAuthUrl"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspCreateTencentDocAuthUrl"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/create_text_files": {"post": {"tags": ["AiBff"], "summary": "创建文本或文件", "operationId": "AiBff_CreateTextFiles", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateTextFiles"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateTextFiles"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/del_tencent_doc_auth": {"post": {"tags": ["AiBff"], "summary": "删除腾讯文档授权", "operationId": "AiBff_DelTencentDocAuth", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqDelTencentDocAuth"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/delete_docs": {"post": {"tags": ["AiBff"], "summary": "删除Doc，包括QA，文本或文件", "operationId": "AiBff_DeleteDocs", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDeleteDocs"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspDeleteDocs"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/describe_account_is_gtb": {"post": {"tags": ["AiBff"], "summary": "查询是否为绿技行用户", "operationId": "AiBff_DescribeAccountIsGTB", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDescribeAccountIsGTB"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspDescribeAccountIsGTB"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/describe_doc_list": {"post": {"tags": ["AiBff"], "summary": "获取腾讯文档列表", "operationId": "AiBff_DescribeDocList", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDescribeDocList"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspDescribeDocList"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/describe_doc_tab": {"post": {"tags": ["AiBff"], "summary": "获取文档标签", "operationId": "AiBff_DescribeDocTab", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqDescribeDocTab"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspDescribeDocTab"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/describe_my_doc": {"post": {"tags": ["AiBff"], "summary": "查询选中的腾讯文档file_id", "operationId": "AiBff_DescribeMyDoc", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqDescribeMyDoc"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspDescribeMyDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/describe_tencent_doc_task": {"post": {"tags": ["AiBff"], "summary": "查询腾讯文档任务状态", "operationId": "AiBff_DescribeTencentDocTask", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqDescribeTencentDocTask"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspDescribeTencentDocTask"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/describe_tencent_token": {"post": {"tags": ["AiBff"], "summary": "查询腾讯文档缓存是否为空", "operationId": "AiBff_DescribeTencentToken", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDescribeTencentToken"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspDescribeTencentToken"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/get_qa_tip": {"post": {"tags": ["AiBff"], "summary": "查询QA的知识提示（问题超长，内容重复）等信息", "operationId": "AiBff_GetQaTip", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqGetQaTip"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspGetQaTip"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/get_text_file": {"post": {"tags": ["AiBff"], "summary": "id查询文本/文件详情", "operationId": "AiBff_GetTextFile", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetTextFile"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetTextFile"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/get_text_file_tip": {"post": {"tags": ["AiBff"], "summary": "查询文本文件的知识提示（解析失败，文件名重复，表头过长）等信息", "operationId": "AiBff_GetTextFileTip", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqGetTextFileTip"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspGetTextFileTip"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/import_qas": {"post": {"tags": ["AiBff"], "summary": "导入文本/文件", "operationId": "AiBff_ImportQAs", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqImportQAs"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspImportQAs"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/import_tencent_doc": {"post": {"tags": ["AiBff"], "summary": "腾讯文档导入", "operationId": "AiBff_ImportTencentDoc", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqImportTencentDoc"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspImportTencentDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/import_tencent_doc_webclip": {"post": {"tags": ["AiBff"], "summary": "导入腾讯文档网页剪辑", "operationId": "AiBff_ImportTencentDocWebClip", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqImportTencentDocWebClip"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspImportTencentDocWebClip"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/import_text_files": {"post": {"tags": ["AiBff"], "summary": "导入文本/文件", "operationId": "AiBff_ImportTextFiles", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqImportTextFiles"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspImportTextFiles"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/list_contributor": {"post": {"tags": ["AiBff"], "summary": "查询贡献者列表", "operationId": "AiBff_ListContributor", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListContributor"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListContributor"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/list_external_source_user": {"post": {"tags": ["AiBff"], "summary": "查询腾讯文档授权列表", "operationId": "AiBff_ListExternalSourceUser", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListExternalSourceUser"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListExternalSourceUser"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/list_filename": {"post": {"tags": ["AiBff"], "summary": "查询文件列表", "operationId": "AiBff_ListCollectionFileName", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListCollectionFileName"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListCollectionFileName"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/list_operator": {"post": {"tags": ["AiBff"], "summary": "查询更新人列表", "operationId": "AiBff_ListOperator", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListOperator"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListOperator"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/list_qa": {"post": {"tags": ["AiBff"], "summary": "查询QA列表", "operationId": "AiBff_ListQA", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListQA"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListQA"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/list_shared_assistant": {"post": {"tags": ["AiBff"], "summary": "查询已分享的助手列表，用于表头筛选", "operationId": "AiBff_ListSharedAssistant", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListSharedAssistant"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListSharedAssistant"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/list_shared_team": {"post": {"tags": ["AiBff"], "summary": "查询已分享的团队列表，用于表头筛选", "operationId": "AiBff_ListSharedTeam", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListSharedTeam"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListSharedTeam"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/list_shared_user": {"post": {"tags": ["AiBff"], "summary": "查询已分享的用户列表，用于表头筛选", "operationId": "AiBff_ListSharedUser", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListSharedUser"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListSharedUser"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/list_text_files": {"post": {"tags": ["AiBff"], "summary": "查询文本/文件列表", "operationId": "AiBff_ListTextFiles", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListTextFiles"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListTextFiles"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/modify_doc_tab": {"post": {"tags": ["AiBff"], "summary": "创建文档标签", "operationId": "AiBff_ModifyDocTab", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqModifyDocTab"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspModifyDocTab"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/onoff_docs": {"post": {"tags": ["AiBff"], "summary": "启用/禁用doc", "operationId": "AiBff_OnOffDocs", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqOnOffDocs"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspOnOffDocs"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/reimport_tencent_doc": {"post": {"tags": ["AiBff"], "summary": "重新导入腾讯文档", "operationId": "AiBff_ReimportTencentDoc", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqReimportTencentDoc"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspReimportTencentDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/reparse_text_files": {"post": {"tags": ["AiBff"], "summary": "重新解析文件", "operationId": "AiBff_ReparseTextFiles", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqReparseTextFiles"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspReparseTextFiles"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/search_collection": {"post": {"tags": ["AiBff"], "summary": "collection向量查询", "operationId": "AiBff_SearchCollection", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqSearchCollection"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspSearchCollection"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/update_qas": {"post": {"tags": ["AiBff"], "summary": "更新QA", "operationId": "AiBff_UpdateQAs", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqUpdateQAs"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/update_text_files": {"post": {"tags": ["AiBff"], "summary": "更新文本或文件", "operationId": "AiBff_UpdateTextFiles", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqUpdateTextFiles"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/validate_qas": {"post": {"tags": ["AiBff"], "summary": "校验待创建QA", "operationId": "AiBff_ValidateQAs", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqValidateQAs"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspValidateQAs"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/validate_text_files": {"post": {"tags": ["AiBff"], "summary": "校验待创建文本文件", "operationId": "AiBff_ValidateTextFiles", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqValidateTextFiles"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspValidateTextFiles"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/confirm_ai_service_terms": {"post": {"tags": ["AiBff"], "summary": "确认AI服务协议", "operationId": "AiBff_ConfirmAiServiceTerms", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqConfirmAiServiceTerms"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/convert_custom_label": {"post": {"tags": ["AiBff"], "summary": "转换标签", "operationId": "AiBff_ConvertCustomLabel", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqConvertCustomLabel"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspConvertCustomLabel"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/create_assistant_receiver": {"post": {"tags": ["AiBff"], "summary": "创建助手接收方设置", "operationId": "AiBff_CreateDocShareConfigReceiverAssistant", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqCreateDocShareConfigReceiverAssistant"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateDocShareConfigReceiverAssistant"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/create_assistant_sender": {"post": {"tags": ["AiBff"], "summary": "创建助手发送方设置", "operationId": "AiBff_CreateDocShareConfigSender", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqCreateDocShareConfigSender"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateDocShareConfigSender"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/create_assistant_share": {"post": {"tags": ["AiBff"], "summary": "创建助手分享", "operationId": "AiBff_CreateAssistantShare", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateAssistantShare"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateAssistantShare"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/create_chat": {"post": {"tags": ["AiBff"], "summary": "创建会话", "operationId": "AiBff_CreateChat", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateChat"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateChat"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/create_chat_export_task": {"post": {"tags": ["AiBff"], "summary": "导出会话", "operationId": "AiBff_CreateChatExportTask", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateChatExportTask"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspExportTask"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/create_chat_question": {"post": {"tags": ["AiBff"], "summary": "创建会话question", "operationId": "AiBff_CreateChatQuestion", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateChatQuestion"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateChatQuestion"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/create_feedback": {"post": {"tags": ["AiBff"], "summary": "创建用户反馈（助手维度）", "operationId": "AiBff_CreateFeedback", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateFeedback"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateFeedback"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/create_file_export_task": {"post": {"tags": ["AiBff"], "summary": "导出文本文件", "operationId": "AiBff_CreateFileExportTask", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateFileExportTask"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspExportTask"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/create_message_export_task": {"post": {"tags": ["AiBff"], "summary": "导出会话消息", "operationId": "AiBff_CreateChatMessageExportTask", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateChatMessageExportTask"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspExportTask"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/create_qa_export_task": {"post": {"tags": ["AiBff"], "summary": "导出qa", "operationId": "AiBff_CreateQaExportTask", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateQaExportTask"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspExportTask"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/create_user_team_receiver": {"post": {"tags": ["AiBff"], "summary": "创建个人/团队接收方设置", "operationId": "AiBff_CreateDocShareConfigReceiverUserTeam", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqCreateDocShareConfigReceiverUserTeam"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateDocShareConfigReceiverUserTeam"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/delete_chat": {"post": {"tags": ["AiBff"], "summary": "删除会话", "operationId": "AiBff_DeleteChat", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDeleteChat"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/delete_custom_labels": {"post": {"tags": ["AiBff"], "summary": "删除自定义标签", "operationId": "AiBff_DeleteCustomLabels", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqDeleteCustomLabels"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/describe_chat_messages": {"post": {"tags": ["AiBff"], "summary": "获取会话消息列表", "operationId": "AiBff_DescribeChatMessages", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDescribeChatMessages"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspDescribeChatMessages"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/describe_chat_region_code": {"post": {"tags": ["AiBff"], "summary": "获取所有会话的地区编码", "operationId": "AiBff_DescribeChatRegionCode", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqDescribeChatRegionCode"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspDescribeChatRegionCode"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/describe_chats": {"post": {"tags": ["AiBff"], "summary": "获取会话列表", "operationId": "AiBff_DescribeChats", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDescribeChats"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspDescribeChats"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/describe_export_tasks": {"post": {"tags": ["AiBff"], "summary": "查询导出任务", "operationId": "AiBff_DescribeExportTasks", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqDescribeExportTasks"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspDescribeExportTasks"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/describe_feedback_region_code": {"post": {"tags": ["AiBff"], "summary": "获取所有教学反馈的地区编码", "operationId": "AiBff_DescribeFeedbackRegionCode", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqDescribeFeedbackRegionCode"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspDescribeFeedbackRegionCode"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/describe_message_file_state": {"post": {"tags": ["AiBff"], "summary": "查询message附件状态", "operationId": "AiBff_DescribeMessageFileState", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDescribeMessageFileState"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspDescribeMessageFileState"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/find_feedback": {"post": {"tags": ["AiBff"], "summary": "查询用户反馈详情", "operationId": "AiBff_FindFeedback", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqFindFeedback"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspFindFeedback"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_account_once_bind_status": {"post": {"tags": ["AiBff"], "summary": "获取当前用户是否一键绑定提示", "operationId": "AiBff_GetAccountOnceBindStatus", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetAccountOnceBindStatus"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetAccountOnceBindStatus"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_account_unionid_status": {"post": {"tags": ["AiBff"], "summary": "获取用户账号union_id绑定状态", "operationId": "AiBff_GetAccountUnionIdStatus", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetAccountUnionIdStatus"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetAccountUnionIdStatus"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_assistant_chunk_config": {"post": {"tags": ["AiBff"], "summary": "获取助手分段配置", "operationId": "AiBff_GetAssistantChunkConfig", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetAssistantChunkConfig"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetAssistantChunkConfig"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_assistant_config": {"post": {"tags": ["AiGuestBff"], "summary": "获取助手配置", "operationId": "AiGuestBff_GetAssistantConfig", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetAssistantConfig"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetAssistantConfig"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_assistant_options": {"post": {"tags": ["AiGuestBff"], "summary": "获取助手下拉选项", "operationId": "AiGuestBff_GetAssistantOptions", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspGetAssistantOptions"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_assistants_miniprogram": {"post": {"tags": ["AiBff"], "summary": "小程序获取助手配置", "operationId": "AiBff_GetAssistantsMiniprogram", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetAssistantsMiniprogram"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetAssistantsMiniprogram"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_chat_detail": {"post": {"tags": ["AiBff"], "summary": "AI对话详情", "operationId": "AiBff_GetChatDetail", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqGetChatDetail"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspGetChatDetail"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_chat_message_detail": {"post": {"tags": ["AiBff"], "summary": "获取消息详情", "operationId": "AiBff_GetChatMessageDetail", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetChatMessageDetail"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetChatMessageDetail"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_chunk_doc_tasks": {"post": {"tags": ["AiBff"], "summary": "查询文档分段任务列表", "operationId": "AiBff_GetChunkDocTasks", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqGetChunkDocTasks"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspGetChunkDocTasks"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_custom_label_value_topn": {"post": {"tags": ["AiBff"], "summary": "获取标签topn值,默认top10", "operationId": "AiBff_GetCustomLabelValueTopN", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqGetCustomLabelValueTopN"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspGetCustomLabelValueTopN"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_custom_labels": {"post": {"tags": ["AiBff"], "summary": "获取自定义标签列表", "operationId": "AiBff_ListCustomLabel", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListCustomLabel"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListCustomLabel"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_doc_chunks": {"post": {"tags": ["AiBff"], "summary": "查询文档分段信息", "operationId": "AiBff_GetDocChunks", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqGetDocChunks"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspGetDocChunks"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_doc_embedding_models": {"post": {"tags": ["AiBff"], "summary": "查询文档的向量化模型", "operationId": "AiBff_GetDocEmbeddingModels", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqGetDocEmbeddingModels"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspGetDocEmbeddingModels"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_feedback_logs": {"post": {"tags": ["AiBff"], "summary": "查询用户反馈日志列表", "operationId": "AiBff_GetFeedbackLogs", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqGetFeedbackLogs"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspGetFeedbackLogs"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_feedbacks": {"post": {"tags": ["AiBff"], "summary": "查询用户反馈列表", "operationId": "AiBff_GetFeedbacks", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqGetFeedbacks"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspGetFeedbacks"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_mini_program_assistant_limit": {"post": {"tags": ["AiBff"], "summary": "获取小程序助手限制情况", "operationId": "AiBff_GetMiniProgramAssistantLimit", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetMiniProgramAssistantLimit"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetMiniProgramAssistantLimit"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_mini_program_auth": {"post": {"tags": ["AiBff"], "summary": "获取小程序文档相关权限", "operationId": "AiBff_GetMiniProgramAuth", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetMiniProgramAuth"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetMiniProgramAuth"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_mini_program_login_url": {"post": {"tags": ["AiBff"], "summary": "获取小程序登录url", "operationId": "AiBff_GetMiniProgramLoginURL", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetMiniProgramLoginURL"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetMiniProgramLoginURL"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_mini_program_user_info": {"post": {"tags": ["AiBff"], "summary": "通过选择账号绑定小程序，替换临时账号", "operationId": "AiBff_BindSelectedAccount", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetMiniProgramUserInfo"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetMiniProgramUserInfo"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_my_ai_service_terms_confirmation": {"post": {"tags": ["AiBff"], "summary": "获取我的AI服务协议确认情况", "operationId": "AiBff_GetMyAiServiceTermsConfirmation", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetMyAiServiceTermsConfirmation"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_my_assistants": {"post": {"tags": ["AiBff"], "summary": "获取我的助手列表", "operationId": "AiBff_GetMyAssistants", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetMyAssistants"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetMyAssistants"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_my_assistants_team": {"post": {"tags": ["AiBff"], "summary": "获取我团队的助手列表", "operationId": "AiBff_GetMyTeamAssistants", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetMyAssistants"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetMyAssistants"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_webview_to_mini_program_token": {"post": {"tags": ["AiBff"], "summary": "获取webview 迁移至小程序token", "operationId": "AiBff_GetWebViewToMiniProgramToken", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetWebViewToMiniProgramToken"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetWebViewToMiniProgramToken"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/list_assistant": {"post": {"tags": ["AiBff"], "summary": "获取管理的ai助手列表", "operationId": "AiBff_ListAssistant", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListAssistant"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListAssistant"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/list_assistant_receiver": {"post": {"tags": ["AiBff"], "summary": "查询助手接收方设置", "operationId": "AiBff_ListDocShareConfigReceiverAssistant", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListDocShareConfigReceiverAssistant"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListDocShareConfigReceiverAssistant"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/list_assistant_sender": {"post": {"tags": ["AiBff"], "summary": "查询助手发送方设置", "operationId": "AiBff_ListeDocShareConfigSender", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListeDocShareConfigSender"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListeDocShareConfigSender"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/list_assistant_to_share_doc": {"post": {"tags": ["AiBff"], "summary": "查询可分享的助手列表", "operationId": "AiBff_ListAssistantCanShareDoc", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListAssistantCanShareDoc"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListAssistantCanShareDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/list_chat": {"post": {"tags": ["AiBff"], "summary": "AI对话管理列表", "operationId": "AiBff_ListChat", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListChat"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListChat"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/list_chat_live_agent": {"post": {"tags": ["AiBff"], "summary": "获取人工坐席列表", "operationId": "AiBff_ListChatLiveAgent", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListChatLiveAgent"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListChatLiveAgent"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/list_my_assistant_ids": {"post": {"tags": ["AiBff"], "summary": "查询已经设置并开启知识库接收的助手", "operationId": "AiBff_ListMyAssistantIds", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListMyAssistantIds"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListMyAssistantIds"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/list_team_to_share_doc": {"post": {"tags": ["AiBff"], "summary": "查询可分享的团队列表", "operationId": "AiBff_ListTeamCanShareDoc", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListTeamCanShareDoc"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListTeamCanShareDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/list_user_team_receiver": {"post": {"tags": ["AiBff"], "summary": "查询个人/团队接收方设置", "operationId": "AiBff_ListDocShareConfigReceiverUserTeam", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListDocShareConfigReceiverUserTeam"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListDocShareConfigReceiverUserTeam"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/list_user_to_share_doc": {"post": {"tags": ["AiBff"], "summary": "查询可分享的个人列表", "operationId": "AiBff_ListUserCanShareDoc", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqListUserCanShareDoc"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspListUserCanShareDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/manual_chunk_doc": {"post": {"tags": ["AiBff"], "summary": "手动文档分段", "operationId": "AiBff_ManualChunkDoc", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqManualChunkDoc"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspManualChunkDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/modify_custom_labels": {"post": {"tags": ["AiBff"], "summary": "插入或更新自定义标签", "operationId": "AiBff_ModifyCustomLabels", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqModifyCustomLabels"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspModifyCustomLabels"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/nebula/create_task": {"post": {"tags": ["AiBff"], "summary": "创建星云任务", "operationId": "AiBff_CreateNebulaTask", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqCreateNebulaTask"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspCreateNebulaTask"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/nebula/describe_medata": {"post": {"tags": ["AiBff"], "summary": "查看投影元数据", "operationId": "AiBff_DescribeNebulaData", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqDescribeNebulaData"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspDescribeNebulaData"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/nebula/describe_projection": {"post": {"tags": ["AiBff"], "summary": "查看投影坐标", "operationId": "AiBff_DescribeNebulaProjection", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqDescribeNebulaProjection"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspDescribeNebulaProjection"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/nebula/describe_task": {"post": {"tags": ["AiBff"], "summary": "查看星云任务详情", "operationId": "AiBff_DescribeNebulaTask", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqDescribeNebulaTask"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspDescribeNebulaTask"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/nebula/describe_task_list": {"post": {"tags": ["AiBff"], "summary": "查看星云任务列表", "operationId": "AiBff_DescribeNebulaTaskList", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqDescribeNebulaTaskList"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspDescribeNebulaTaskList"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/nebula/list_assistants": {"post": {"tags": ["AiBff"], "summary": "知识星云，获取助手列表", "operationId": "AiBff_ListNebulaAssistants", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListNebulaAssistants"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListNebulaAssistants"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/nebula/list_contributors": {"post": {"tags": ["AiBff"], "summary": "知识星云，获取助手下的贡献者筛选项", "operationId": "AiBff_ListNebulaContributors", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListNebulaContributors"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListNebulaContributors"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/proxy_chat_url": {"post": {"tags": ["AiBff"], "summary": "获取助手url网页title", "operationId": "AiBff_ProxyChatHtmlUrl", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqProxyChatHtmlUrl"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspProxyChatHtmlUrl"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/rate_ai_answer": {"post": {"tags": ["AiBff"], "summary": "评价AI回答", "operationId": "AiBff_RateAiAnswer", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqRateAiAnswer"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/receive_chat_message": {"post": {"tags": ["AiBff"], "summary": "发送消息", "operationId": "AiBff_ReceiveChatMessage", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqReceiveChatMessage"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspReceiveChatMessage"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/resend_chat_message": {"post": {"tags": ["AiBff"], "summary": "重发会话消息", "operationId": "AiBff_ResendChatMessage", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqResendChatMessage"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/restart_export_task": {"post": {"tags": ["AiBff"], "summary": "重新开始导出任务", "operationId": "AiBff_RestartExportTask", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqRestartExportTask"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/save_op_feedback": {"post": {"tags": ["AiBff"], "summary": "保存运营反馈", "operationId": "AiBff_SaveOpFeedback", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqSaveOpFeedback"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateFeedback"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/save_user_feedback_by_question": {"post": {"tags": ["AiBff"], "summary": "保存用户反馈（问题维度）", "operationId": "AiBff_SaveUserFeedbackByQuestion", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqSaveUserFeedbackByQuestion"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateFeedback"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/search_chat": {"post": {"tags": ["AiBff"], "summary": "搜索chat", "operationId": "AiBff_SearchChat", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqSearchChat"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspSearchChat"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/search_chat_users": {"post": {"tags": ["AiBff"], "summary": "搜索AI对话的用户列表", "operationId": "AiBff_SearchChatUsers", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqSearchChatUsers"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspSearchChatUsers"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/stop_reply": {"post": {"tags": ["AiBff"], "summary": "停止消息发送", "operationId": "AiBff_StopQuestionReply", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqStopQuestionReply"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tanlivebff_webaiRspStopQuestionReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/switch_chat_live_agent": {"post": {"tags": ["AiBff"], "summary": "切换人工坐席", "operationId": "AiBff_SwitchChatLiveAgent", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tanlivebff_webaiReqSwitchChatLiveAgent"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspSwitchChatLiveAgent"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/update_my_assistant": {"post": {"tags": ["AiBff"], "summary": "更新我的助手", "operationId": "AiBff_UpdateMyAssistant", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqUpdateMyAssistant"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/update_my_assistant_team": {"post": {"tags": ["AiBff"], "summary": "更新我团队的助手", "operationId": "AiBff_UpdateMyTeamAssistant", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqUpdateMyAssistant"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/update_object_custom_labels": {"post": {"tags": ["AiBff"], "summary": "更新对象的自定义标签", "operationId": "AiBff_UpdateObjectCustomLabels", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqUpdateObjectCustomLabels"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/iam/active_my_email": {"post": {"tags": ["IamBff"], "summary": "激活我的邮箱", "operationId": "IamBff_ActiveMyEmail", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqActiveMyEmail"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/iam/bind_my_google": {"post": {"tags": ["IamBff"], "summary": "绑定我的谷歌账号", "operationId": "IamBff_BindMyGoogle", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqBindMyGoogle"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/iam/bind_my_weixin_by_oauth2": {"post": {"tags": ["IamBff"], "summary": "绑定我的微信（在微信浏览器中）", "operationId": "IamBff_BindMyWeixinByOauth2", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqBindMyWeixinByOauth2"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/iam/create_bind_my_weixin_qrcode": {"post": {"tags": ["IamBff"], "summary": "创建绑定我的微信二维码", "operationId": "IamBff_CreateBindMyWeixinQrcode", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqCreateBindMyWeixinQrcode"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspCreateBindMyWeixinQrcode"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/iam/create_email_2fa": {"post": {"tags": ["IamBff"], "summary": "创建邮箱二次验证", "operationId": "IamBff_CreateEmailTfa", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqCreateEmailTfa"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspCreateTfa"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/iam/create_google_2fa": {"post": {"tags": ["IamBff"], "summary": "创建谷歌二次认证", "operationId": "IamBff_CreateGoogleTfa", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqCreateGoogleTfa"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspCreateTfa"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/iam/create_password_2fa": {"post": {"tags": ["IamBff"], "summary": "创建密码二次验证", "operationId": "IamBff_CreatePasswordTfa", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqCreatePasswordTfa"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspCreateTfa"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/iam/create_phone_2fa": {"post": {"tags": ["IamBff"], "summary": "创建手机二次验证", "operationId": "IamBff_CreatePhoneTfa", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqCreatePhoneTfa"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspCreateTfa"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/iam/create_weixin_2fa_qrcode": {"post": {"tags": ["IamBff"], "summary": "创建微信二次验证二维码", "operationId": "IamBff_CreateWeixinTfaQrcode", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspCreateWeixinTfaQrcode"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/iam/create_weixin_browser_2fa": {"post": {"tags": ["IamBff"], "summary": "创建微信浏览器二次验证", "operationId": "IamBff_CreateWeixinBrowserTfa", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqCreateWeixinBrowserTfa"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspCreateTfa"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/iam/get_bind_my_weixin_state": {"post": {"tags": ["IamBff"], "summary": "查询绑定我的微信状态", "operationId": "IamBff_GetBindMyWeixinState", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqGetBindMyWeixinState"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspGetBindMyWeixinState"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/iam/get_create_weixin_2fa_state": {"post": {"tags": ["IamBff"], "summary": "查询创建微信二次验证状态", "operationId": "IamBff_GetCreateWeixinTfaState", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqGetCreateWeixinTfaState"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspGetCreateWeixinTfaState"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/iam/get_google_auth_url": {"post": {"tags": ["IamGuestBff"], "summary": "获取谷歌认证URL", "operationId": "IamGuestBff_GetGoogleAuthUrl", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqGetGoogleAuthUrl"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspGetGoogleAuthUrl"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/iam/get_my_2fa": {"post": {"tags": ["IamBff"], "summary": "查询我的的二次验证", "operationId": "IamBff_GetMyTfa", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspGetMyTfa"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/iam/login": {"post": {"tags": ["IamGuestBff"], "summary": "登入", "operationId": "IamGuestBff_Login", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqLogin"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspLogin"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/iam/logout": {"post": {"tags": ["IamBff"], "summary": "登出", "operationId": "IamBff_Logout", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/iam/modify_my_email": {"post": {"tags": ["IamBff"], "summary": "修改我的邮箱", "operationId": "IamBff_ModifyMyEmail", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqModifyMyEmail"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspModifyMyEmail"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/iam/modify_my_password": {"post": {"tags": ["IamBff"], "summary": "修改我的密码", "operationId": "IamBff_ModifyMyPassword", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqModifyMyPassword"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/iam/modify_my_phone": {"post": {"tags": ["IamBff"], "summary": "修改我的手机号", "operationId": "IamBff_ModifyMyPhone", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqModifyMyPhone"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/iam/modify_my_username": {"post": {"tags": ["IamBff"], "summary": "修改我的用户名", "operationId": "IamBff_ModifyMyUsername", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqModifyMyUsername"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/iam/send_2fa_otp": {"post": {"tags": ["IamBff"], "summary": "发送二次验证的验证码", "operationId": "IamBff_SendTfaOtp", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqSendTfaOtp"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspSendTfaOtp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/iam/unbind_my_google": {"post": {"tags": ["IamBff"], "summary": "解绑我的谷歌账号", "operationId": "IamBff_UnbindMyGoogle", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqUnbindMyGoogle"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/iam/unbind_my_weixin": {"post": {"tags": ["IamBff"], "summary": "解绑我的微信", "operationId": "IamBff_UnbindMyWeixin", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqUnbindMyWeixin"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/notify/get_email_send_status": {"post": {"tags": ["NotifyGuestBff"], "operationId": "NotifyGuestBff_GetEmailSendStatus", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/notifyReqGetEmailSendStatus"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/notifyRspGetEmailSendStatus"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/notify/get_sms_send_status": {"post": {"tags": ["NotifyGuestBff"], "operationId": "NotifyGuestBff_GetSmsSendStatus", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/notifyReqGetSmsSendStatus"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/notifyRspGetSmsSendStatus"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/notify/send_one_time_password": {"post": {"tags": ["IamGuestBff"], "summary": "发送验证码（未登录）", "operationId": "IamGuestBff_SendAuthOtp", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqSendAuthOtp"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspSendAuthOtp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/search/describe_atlas_search_option": {"post": {"tags": ["SearchGuestBff"], "operationId": "SearchGuestBff_DescribeAtlasSearchOptions", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/searchReqDescribeAtlasSearchOptions"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/searchRspDescribeAtlasSearchOptions"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/search/describe_search_prompt": {"post": {"tags": ["SearchGuestBff"], "operationId": "SearchGuestBff_DescribeSearchPrompts", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/searchReqDescribeSearchPrompts"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/searchRspDescribeSearchPrompts"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/support/get_place_selector": {"post": {"tags": ["SupportGuestBff"], "operationId": "SupportGuestBff_GetPlaceSelector", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/searchReqGetPlaceSelector"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/searchRspGetPlaceSelector"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/support/proxy": {"post": {"tags": ["SupportGuestBff"], "operationId": "SupportGuestBff_Proxy", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/searchReqProxy"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/searchRspProxy"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/support/search_places": {"post": {"tags": ["SupportGuestBff"], "operationId": "SupportGuestBff_SearchPlaces", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/searchReqSearchPlaces"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/searchRspSearchPlaces"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/support/send_map_request": {"post": {"tags": ["SupportGuestBff"], "operationId": "SupportGuestBff_SendMapRequest", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/searchReqSendMapRequest"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/searchRspSendMapRequest"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/tag/get_system_tags": {"post": {"tags": ["TagGuestBff"], "summary": "获取全量系统标签", "operationId": "TagGuestBff_GetSystemTags", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqGetSystemTags"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tagRspGetSystemTags"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/team/search_teams_in_ai_share_setting": {"post": {"tags": ["TeamBff"], "summary": "搜索团队（AI知识分享默认设置场景）", "operationId": "TeamBff_SearchTeamsInAiShareSetting", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/teamReqSearchTeamsInAiShareSetting"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/teamRspSearchTeamsInAiShareSetting"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/team/update_ugc_custom_route": {"post": {"tags": ["TeamBff"], "summary": "更新ugc自定义路由", "operationId": "TeamBff_UpdateUgcCustomRoute", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/teamReqUpdateUgcCustomRoute"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}}, "definitions": {"AssistantKefuReplyCustom": {"type": "object", "properties": {"text": {"type": "string", "title": "显示文本"}, "url": {"type": "string", "title": "url地址"}}}, "AssistantKefuReplyReply": {"type": "object", "properties": {"img_url": {"type": "string", "title": "图片地址"}, "reply_message": {"type": "string", "title": "回复消息"}}}, "ChatLiveAgentInfoLiveAgentInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "nickname": {"type": "string"}}}, "EventChatHashMessageEventContributor": {"type": "object", "properties": {"full_name": {"type": "string", "title": "team full name"}, "id": {"type": "string"}, "is_published": {"type": "boolean", "title": "是否已发布"}, "level": {"title": "共创等级", "$ref": "#/definitions/teamTeamLevel"}, "text": {"type": "string"}, "type": {"$ref": "#/definitions/baseIdentityType"}}}, "EventChatHashMessageEventMessageDoc": {"type": "object", "properties": {"contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/EventChatHashMessageEventContributor"}}, "data_type": {"type": "integer", "format": "int64"}, "file_name": {"type": "string"}, "id": {"type": "string", "format": "uint64"}, "rag_filename": {"type": "string"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "ugc_id": {"type": "string", "format": "uint64"}, "ugc_type": {"$ref": "#/definitions/baseDataType"}}}, "EventChatHashMessageEventMessageFilter": {"type": "object", "properties": {"field": {"type": "string"}, "tags": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/EventChatHashMessageEventMessageTag"}}, "value": {"type": "string"}}}, "EventChatHashMessageEventMessageTag": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "taggable_type": {"type": "integer", "format": "int32"}, "type": {"type": "integer", "format": "int32"}}}, "EventChatHashMessageEventMessageUgc": {"type": "object", "properties": {"cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/EventChatHashMessageEventMessageUgcCard"}}, "filter": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/EventChatHashMessageEventMessageFilter"}}, "is_ugc_link": {"type": "boolean"}, "ugc_ids": {"type": "array", "items": {"type": "string"}}, "ugc_type": {"$ref": "#/definitions/baseDataType"}}}, "EventChatHashMessageEventMessageUgcCard": {"type": "object", "properties": {"id": {"type": "string"}, "logo_url": {"type": "string"}, "name": {"type": "string"}, "tags": {"type": "string"}}}, "FeedbackCommentFile": {"type": "object", "title": "文件", "properties": {"file_name": {"type": "string", "title": "文件名称"}, "file_path": {"type": "string", "title": "路径"}}}, "ReqLoginAccountCredentials": {"type": "object", "properties": {"password": {"type": "string", "title": "密码"}, "username": {"type": "string", "title": "用户名"}}}, "ReqLoginEmailCredentials": {"type": "object", "properties": {"auth_code": {"type": "string", "title": "验证码"}, "email": {"type": "string", "title": "邮箱"}}}, "ReqLoginPhoneCredentials": {"type": "object", "properties": {"auth_code": {"type": "string", "title": "验证码"}, "phone": {"type": "string", "title": "手机号"}}}, "ReqLoginThirdCredentials": {"type": "object", "properties": {"third_ticket": {"type": "string", "title": "第三方凭证"}}}, "RspAcceptFeedbackResult": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "title": "错误码"}, "feedback_id": {"type": "string", "format": "uint64", "title": "反馈ID"}}}, "RspBatchUserAssistantLimitUserLimit": {"type": "object", "properties": {"can_in": {"type": "boolean", "title": "是否可以进入 true 可， false 不可"}, "empty_phone": {"type": "boolean", "title": "如果限制，需要检测手机号，true 空手机， false 不空；"}, "token": {"type": "string"}}}, "RspListSharedTeamSharedTeam": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}}}, "RspListSharedUserSharedUser": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}}}, "RspListeDocShareConfigSenderSharedUserTeam": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "is_selected": {"type": "boolean"}, "name": {"type": "string"}}}, "RspProxyChatHtmlUrlContent": {"type": "object", "properties": {"title": {"type": "string"}, "url": {"type": "string"}}}, "RspProxyContent": {"type": "object", "properties": {"title": {"type": "string"}, "url": {"type": "string"}}}, "aiAiRecordType": {"type": "integer", "format": "int32", "title": "- 1: 用户咨询\n - 2: 助手回答文本消息\n - 3: 助手回答菜单消息\n - 4: 助手回答建议问题菜单消息\n - 5: 助手回答贡献知识的小程序\n - 6: 发送转人工菜单信息\n - 7: 助手回答图片\n - 8: 助手回答音频\n - 9: 助手回答视频\n - 10: 助手回答文件", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]}, "aiAskSuggestionMode": {"description": "- 1: 模式1：根据历史问答生成问题建议\n - 2: 模式2：根据历史问答生成，并仅显示知识库中有相关知识的问题建议\n - 3: 模式3：根据问题在知识库中已命中的知识，生成问题建议\n - 4: 模式4：根据问题在知识库中尚未命中但排名靠前的知识，生成问题建议", "type": "integer", "format": "int32", "title": "问题建议模式", "enum": [1, 2, 3, 4]}, "aiAssistantAllowlistConfig": {"type": "object", "title": "助手白名单配置", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "phones": {"type": "array", "title": "手机列表", "items": {"type": "string"}}, "type": {"title": "白名单类型", "$ref": "#/definitions/aiAssistantAllowlistType"}}}, "aiAssistantAllowlistType": {"description": "- 1: 手机号码\n - 2: 微信昵称", "type": "integer", "format": "int32", "title": "助手白名单类型", "enum": [1, 2]}, "aiAssistantAskSuggestionConfig": {"type": "object", "title": "问题建议配置", "properties": {"count": {"type": "integer", "format": "int32", "title": "问题建议数量"}, "enabled": {"type": "boolean", "title": "是否启用"}, "mode": {"title": "问题建议模式", "$ref": "#/definitions/aiAskSuggestionMode"}, "model": {"type": "string", "title": "模型"}, "prompt": {"type": "string", "title": "提示词"}, "times": {"type": "integer", "format": "int64", "title": "问题建议倍数"}}}, "aiAssistantChannel": {"description": "- 1: 碳LIVE-微信\n - 2: 碳LIVE-Web\n - 3: 碳LIVE-应用\n - 4: 碳LIVE-WhatsApp\n - 5: 第三方机构-微信\n - 6: 碳LIVE-小程序", "type": "integer", "format": "int32", "title": "助手渠道", "enum": [1, 2, 3, 4, 5, 6]}, "aiAssistantChatOrSqlConfig": {"type": "object", "title": "ChatOrSql配置", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "model": {"type": "string", "title": "模型"}}}, "aiAssistantChunkConfig": {"type": "object", "title": "分段配置", "properties": {"max_char_count": {"type": "integer", "format": "int32", "title": "最大字符数"}, "max_char_lang": {"type": "string", "title": "最大字符语言"}, "min_char_count": {"type": "integer", "format": "int32", "title": "最小字符数"}, "min_char_lang": {"type": "string", "title": "最小字符语言"}, "overlap_count": {"type": "integer", "format": "int32", "title": "重合字符数"}, "overlap_lang": {"type": "string", "title": "重合字符语言"}}}, "aiAssistantChunks": {"type": "object", "title": "助手的分段信息", "properties": {"assistant_id": {"type": "array", "title": "助手ID", "items": {"type": "string", "format": "uint64"}}, "chunks": {"type": "array", "title": "分段列表", "items": {"type": "object", "$ref": "#/definitions/aiChunkItem"}}}}, "aiAssistantConfig": {"type": "object", "title": "助手配置", "properties": {"admins": {"type": "array", "title": "助手管理员", "items": {"type": "object", "$ref": "#/definitions/baseIdentity"}}, "allowlist_config": {"title": "白名单配置", "$ref": "#/definitions/aiAssistantAllowlistConfig"}, "ask_suggestion_config": {"title": "问题建议配置", "$ref": "#/definitions/aiAssistantAskSuggestionConfig"}, "brief_intro": {"type": "string", "title": "一句话介绍"}, "channel": {"title": "渠道", "$ref": "#/definitions/aiAssistantChannel"}, "chat_or_sql_config": {"title": "ChatOrSql配置", "$ref": "#/definitions/aiAssistantChatOrSqlConfig"}, "chunk_config": {"title": "分段配置", "$ref": "#/definitions/aiAssistantChunkConfig"}, "clean_chunks": {"type": "boolean", "title": "自动过滤"}, "close_search": {"type": "boolean", "title": "是否关闭搜索增强"}, "collection_lang": {"type": "string", "title": "知识库语言"}, "collection_name": {"type": "string", "title": "知识库名称（自动生成）"}, "detail_intro": {"type": "string", "title": "助手介绍"}, "doc_top_n": {"type": "integer", "format": "int32", "title": "知识库Top_N"}, "enabled": {"type": "boolean", "title": "是否启用"}, "field_manage_config": {"title": "参数管理权限", "$ref": "#/definitions/aiAssistantFieldManageConfig"}, "history_rounds": {"type": "integer", "format": "int32", "title": "对话轮数"}, "miniprogram_channel_config": {"title": "小程序渠道配置", "$ref": "#/definitions/aiAssistantMiniprogramChannelConfig"}, "miss_reply": {"type": "string", "title": "未命中知识库自动回复"}, "model": {"type": "string", "title": "对话模型"}, "name": {"type": "string", "title": "AI助手名"}, "name_en": {"type": "string", "title": "AI助手名（英文）"}, "prompt_prefix": {"type": "string", "title": "提示词"}, "question_type_config": {"title": "问题分类配置", "$ref": "#/definitions/aiAssistantQuestionTypeConfig"}, "search_debug": {"type": "boolean", "title": "搜索测试"}, "search_engine": {"type": "string", "title": "搜索引擎"}, "search_top_n": {"type": "integer", "format": "int32", "title": "互联网Top_N"}, "show_in_list": {"type": "boolean", "title": "是否在助手列表展示"}, "show_think": {"type": "boolean", "title": "是否展示思考过程"}, "tanlive_app_channel_config": {"title": "Web渠道配置", "$ref": "#/definitions/aiAssistantTanliveAppChannelConfig"}, "tanlive_web_channel_config": {"title": "碳LIVE Web渠道配置", "$ref": "#/definitions/aiAssistantTanliveWebChannelConfig"}, "temperature": {"type": "number", "format": "float", "title": "温度"}, "text_recall_pattern": {"title": "关键词召回模式", "$ref": "#/definitions/aiTextRecallPattern"}, "text_recall_query": {"title": "QA关键词召回目标", "$ref": "#/definitions/aiTextRecallQuery"}, "text_recall_slop": {"type": "integer", "format": "int32", "title": "允许平移距离"}, "text_recall_top_n": {"type": "integer", "format": "int32", "title": "关键词召回条数"}, "text_weight": {"type": "number", "format": "float", "title": "关键词搜索权重"}, "threshold": {"type": "number", "format": "float", "title": "对话阈值"}, "use_region_code": {"type": "string", "title": "使用地区（空不限制）"}, "user_label_config": {"title": "助手用户打标配置", "$ref": "#/definitions/aiAssistantUserLabelConfig"}, "visible_chain_config": {"title": "链路查询", "$ref": "#/definitions/aiAssistantVisibleChainConfig"}, "weixin_channel_config": {"title": "微信渠道配置", "$ref": "#/definitions/aiAssistantWeixinChannelConfig"}, "whatsapp_channel_config": {"title": "WhatsApp渠道配置", "$ref": "#/definitions/aiAssistantWhatsappChannelConfig"}}}, "aiAssistantFieldManageConfig": {"type": "object", "title": "字段管理配置", "properties": {"enabled": {"type": "boolean", "title": "是否启用（该字段暂不使用）"}, "readable": {"type": "array", "title": "可读字段（为空全部可读）", "items": {"type": "string"}}, "writable": {"type": "array", "title": "可写字段（为空全部不可写）", "items": {"type": "string"}}}}, "aiAssistantGraphParseConfig": {"type": "object", "title": "图谱解析配置", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "model": {"type": "string", "title": "模型"}}}, "aiAssistantInteractiveCode": {"type": "object", "title": "互动暗号详情", "properties": {"content": {"type": "string", "title": "内容"}, "interactive_code": {"title": "互动暗号", "$ref": "#/definitions/aiInteractiveCode"}, "lang": {"type": "string", "title": "语言"}, "show_in_welcome": {"type": "boolean", "title": "是否在欢迎语里显示"}}}, "aiAssistantInteractiveCodeConfig": {"type": "object", "title": "互动暗号配置", "properties": {"codes": {"type": "array", "title": "暗号配置", "items": {"type": "object", "$ref": "#/definitions/aiAssistantInteractiveCode"}}, "send_interactive_code": {"type": "boolean", "title": "开启回复发送暗号"}}}, "aiAssistantKefuConfig": {"type": "object", "title": "人工客服配置", "properties": {"after_remind_message": {"type": "string", "title": "转人工后的提示语"}, "before_remind_enabled": {"type": "boolean", "title": "是否启用转人工前的提示语"}, "before_remind_message": {"type": "string", "title": "转人工前的提示语"}, "enabled": {"type": "boolean", "title": "是否启用"}, "reply": {"title": "客服自动回复配置", "$ref": "#/definitions/aiAssistantKefuReply"}, "staffs": {"type": "array", "title": "员工信息", "items": {"type": "object", "$ref": "#/definitions/aiAssistantKefuStaff"}}}}, "aiAssistantKefuReply": {"type": "object", "title": "助手客服自动回复", "properties": {"custom": {"type": "array", "title": "自定义配置列表", "items": {"type": "object", "$ref": "#/definitions/AssistantKefuReplyCustom"}}, "enable_custom": {"type": "boolean", "title": "是否启用自定义配置"}, "mainland_en": {"title": "大陆英文", "$ref": "#/definitions/AssistantKefuReplyReply"}, "mainland_zh": {"title": "大陆中文", "$ref": "#/definitions/AssistantKefuReplyReply"}, "non_mainland_en": {"title": "非中国大陆英文", "$ref": "#/definitions/AssistantKefuReplyReply"}, "non_mainland_zh": {"title": "非中国大陆中文", "$ref": "#/definitions/AssistantKefuReplyReply"}}}, "aiAssistantKefuStaff": {"type": "object", "title": "人工客服信息", "properties": {"id": {"type": "string", "format": "uint64", "title": "主键"}, "nickname": {"type": "string", "title": "昵称"}, "username": {"type": "string", "title": "账号"}}}, "aiAssistantMiniprogramChannelConfig": {"type": "object", "title": "助手小程序渠道配置", "properties": {"assistant_lang": {"type": "string", "title": "助手语言（已废弃，请使用system_languages）"}, "avatar_url": {"type": "string", "title": "头像（默认头像留空）"}, "feedback_enabled": {"type": "boolean", "title": "用户教学反馈"}, "graph_parse_config": {"title": "图谱解析配置", "$ref": "#/definitions/aiAssistantGraphParseConfig"}, "interactive_code_config": {"title": "互动暗号配置", "$ref": "#/definitions/aiAssistantInteractiveCodeConfig"}, "kefu_config": {"title": "人工客服", "$ref": "#/definitions/aiAssistantKefuConfig"}, "miniprogram_config": {"title": "小程序配置", "$ref": "#/definitions/aiAssistantMiniprogramConfig"}, "nickname": {"type": "string", "title": "助手昵称"}, "nickname_en": {"type": "string", "title": "助手昵称（英文）"}, "preset_question_config": {"title": "预设问题配置", "$ref": "#/definitions/aiAssistantPresetQuestionConfig"}, "rating_scale_reply_config": {"title": "满意度回复配置", "$ref": "#/definitions/aiAssistantRatingScaleReplyConfig"}, "system_languages": {"type": "array", "title": "系统语言", "items": {"type": "string"}}, "welcome_message_config": {"title": "欢迎语配置", "$ref": "#/definitions/aiAssistantWelcomeMessageConfig"}}}, "aiAssistantMiniprogramConfig": {"type": "object", "title": "小程序配置", "properties": {"schema": {"type": "string", "title": "小程序schema"}, "share_image": {"type": "string", "title": "分享图片"}, "share_title": {"type": "string", "title": "分享标题"}, "url": {"type": "string", "title": "二维码URL"}}}, "aiAssistantPresetQuestion": {"type": "object", "title": "预设问题详情", "properties": {"content": {"type": "string", "title": "内容"}, "lang": {"type": "string", "title": "语言"}}}, "aiAssistantPresetQuestionConfig": {"type": "object", "title": "预设问题配置", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "questions": {"type": "array", "title": "配置列表", "items": {"type": "object", "$ref": "#/definitions/aiAssistantPresetQuestion"}}}}, "aiAssistantQuestionTypeConfig": {"type": "object", "title": "问题类型配置", "properties": {"chat_model": {"type": "string", "title": "聊天问题的模型"}, "complex_model": {"type": "string", "title": "复杂问题的模型"}, "enabled": {"type": "boolean", "title": "是否启用"}, "prompt": {"type": "string", "title": "简单、复杂问题提示词"}, "simple_model": {"type": "string", "title": "简单问题的模型"}}}, "aiAssistantRatingScaleReply": {"type": "object", "title": "满意度回复详情", "properties": {"content": {"type": "string", "title": "内容"}, "lang": {"type": "string", "title": "语言"}, "rating_scale": {"title": "评价等级", "$ref": "#/definitions/aiRatingScale"}}}, "aiAssistantRatingScaleReplyConfig": {"type": "object", "title": "满意度回复配置", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "replies": {"type": "array", "title": "满意度回复配置", "items": {"type": "object", "$ref": "#/definitions/aiAssistantRatingScaleReply"}}}}, "aiAssistantTanliveAppChannelConfig": {"type": "object", "title": "碳LIVE应用助手渠道配置", "properties": {"app_id": {"type": "string", "title": "应用ID"}, "assistant_lang": {"type": "string", "title": "助手语言（已废弃，请使用system_languages）"}, "avatar_url": {"type": "string", "title": "头像（默认头像留空）"}, "feedback_enabled": {"type": "boolean", "title": "用户教学反馈"}, "graph_parse_config": {"title": "图谱解析配置", "$ref": "#/definitions/aiAssistantGraphParseConfig"}, "interactive_code_config": {"title": "互动暗号配置", "$ref": "#/definitions/aiAssistantInteractiveCodeConfig"}, "kefu_config": {"title": "人工客服", "$ref": "#/definitions/aiAssistantKefuConfig"}, "nickname": {"type": "string", "title": "助手昵称"}, "nickname_en": {"type": "string", "title": "助手昵称（英文）"}, "preset_question_config": {"title": "预设问题配置", "$ref": "#/definitions/aiAssistantPresetQuestionConfig"}, "rating_scale_reply_config": {"title": "满意度回复配置", "$ref": "#/definitions/aiAssistantRatingScaleReplyConfig"}, "system_languages": {"type": "array", "title": "系统语言", "items": {"type": "string"}}, "welcome_message_config": {"title": "欢迎语配置", "$ref": "#/definitions/aiAssistantWelcomeMessageConfig"}}}, "aiAssistantTanliveWebChannelConfig": {"type": "object", "title": "碳LIVE Web助手渠道配置", "properties": {"assistant_lang": {"type": "string", "title": "助手语言（已废弃，请使用system_languages）"}, "avatar_url": {"type": "string", "title": "头像（默认头像留空）"}, "feedback_enabled": {"type": "boolean", "title": "用户教学反馈"}, "graph_parse_config": {"title": "图谱解析配置", "$ref": "#/definitions/aiAssistantGraphParseConfig"}, "interactive_code_config": {"title": "互动暗号配置", "$ref": "#/definitions/aiAssistantInteractiveCodeConfig"}, "kefu_config": {"title": "人工客服", "$ref": "#/definitions/aiAssistantKefuConfig"}, "nickname": {"type": "string", "title": "助手昵称"}, "nickname_en": {"type": "string", "title": "助手昵称（英文）"}, "preset_question_config": {"title": "预设问题配置", "$ref": "#/definitions/aiAssistantPresetQuestionConfig"}, "rating_scale_reply_config": {"title": "满意度回复配置", "$ref": "#/definitions/aiAssistantRatingScaleReplyConfig"}, "switch_assistant_id": {"type": "string", "format": "uint64", "title": "切换助手ID"}, "system_languages": {"type": "array", "title": "系统语言", "items": {"type": "string"}}, "website_config": {"title": "网站配置", "$ref": "#/definitions/aiAssistantWebsiteConfig"}, "welcome_message_config": {"title": "欢迎语配置", "$ref": "#/definitions/aiAssistantWelcomeMessageConfig"}}}, "aiAssistantUserLabelConfig": {"type": "object", "title": "助手用户打标配置", "properties": {"tag_ids": {"type": "array", "title": "绑定标签id", "items": {"type": "string", "format": "uint64"}}, "tag_names": {"type": "array", "title": "绑定标签的名称", "items": {"type": "string"}}}}, "aiAssistantV2": {"type": "object", "title": "助手信息", "properties": {"batch_no": {"type": "string", "title": "批次号"}, "config": {"title": "配置详情", "$ref": "#/definitions/aiAssistantConfig"}, "create_by": {"title": "创建人", "$ref": "#/definitions/baseIdentity"}, "create_date": {"type": "string", "format": "date-time", "title": "创建时间"}, "id": {"type": "string", "format": "uint64", "title": "主键"}, "is_draft": {"type": "boolean", "title": "是否为草稿"}, "update_by": {"title": "更新人", "$ref": "#/definitions/baseIdentity"}, "update_date": {"type": "string", "format": "date-time", "title": "更新时间"}}}, "aiAssistantVisibleChainConfig": {"type": "object", "title": "链路查询", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "visible": {"type": "array", "title": "可见字段", "items": {"type": "string"}}}}, "aiAssistantWebsiteConfig": {"type": "object", "title": "网站配置", "properties": {"route_path": {"type": "string", "title": "路由路径"}, "title": {"type": "string", "title": "标题"}}}, "aiAssistantWeixinChannelConfig": {"type": "object", "title": "微信渠道配置", "properties": {"avatar_url": {"type": "string", "title": "头像（默认头像留空）"}, "chat_idle_duration": {"type": "integer", "format": "int32", "title": "会话闲置超时时间（分钟）"}, "interactive_code_config": {"title": "互动暗号配置", "$ref": "#/definitions/aiAssistantInteractiveCodeConfig"}, "kefu_config": {"title": "人工客服配置", "$ref": "#/definitions/aiAssistantKefuConfig"}, "nickname": {"type": "string", "title": "助手昵称"}, "preset_question_config": {"title": "预设问题配置", "$ref": "#/definitions/aiAssistantPresetQuestionConfig"}, "rating_scale_reply_config": {"title": "满意度回复配置", "$ref": "#/definitions/aiAssistantRatingScaleReplyConfig"}, "system_lang": {"type": "string", "title": "系统语言（已废弃，请使用system_languages）"}, "system_languages": {"type": "array", "title": "系统语言", "items": {"type": "string"}}, "weixin_develop_config": {"title": "微信开发配置", "$ref": "#/definitions/aiAssistantWeixinDevelopConfig"}, "welcome_message_config": {"title": "欢迎语配置", "$ref": "#/definitions/aiAssistantWelcomeMessageConfig"}}}, "aiAssistantWeixinDevelopConfig": {"type": "object", "title": "微信开发配置", "properties": {"corp_id": {"type": "string", "title": "企业ID"}, "kf_url": {"type": "string", "title": "客服URL"}, "open_kfid": {"type": "string", "title": "客服账号ID"}}}, "aiAssistantWelcomeMessage": {"type": "object", "title": "欢迎语详情", "properties": {"content": {"type": "string", "title": "内容"}, "lang": {"type": "string", "title": "语言"}}}, "aiAssistantWelcomeMessageConfig": {"type": "object", "title": "欢迎语配置", "properties": {"messages": {"type": "array", "title": "配置列表", "items": {"type": "object", "$ref": "#/definitions/aiAssistantWelcomeMessage"}}}}, "aiAssistantWhatsappChannelConfig": {"type": "object", "title": "Whatsapp助手渠道配置", "properties": {"avatar_url": {"type": "string", "title": "头像"}, "chat_idle_duration": {"type": "integer", "format": "int32", "title": "会话闲置超时时间（分钟）"}, "nickname": {"type": "string", "title": "昵称"}, "preset_question_config": {"title": "预设问题配置", "$ref": "#/definitions/aiAssistantPresetQuestionConfig"}, "rating_scale_reply_config": {"title": "满意度回复配置", "$ref": "#/definitions/aiAssistantRatingScaleReplyConfig"}, "system_lang": {"type": "string", "title": "系统语言（已废弃，请使用system_languages）"}, "system_languages": {"type": "array", "title": "系统语言", "items": {"type": "string"}}, "welcome_message_config": {"title": "欢迎语配置", "$ref": "#/definitions/aiAssistantWelcomeMessageConfig"}, "whatsapp_develop_config": {"title": "WhatsApp开发配置", "$ref": "#/definitions/aiAssistantWhatsappDevelopConfig"}}}, "aiAssistantWhatsappDevelopConfig": {"type": "object", "title": "WhatsApp开发配置", "properties": {"business_number": {"type": "string", "title": "Business number"}}}, "aiAutoChunkPara": {"type": "object", "title": "自动分段参数", "properties": {"assistant_id": {"type": "array", "title": "助手ID", "items": {"type": "string", "format": "uint64"}}, "chunk_config": {"title": "分段配置", "$ref": "#/definitions/aiAssistantChunkConfig"}, "dry_run": {"type": "boolean", "title": "仅预览"}}}, "aiChatCurrentState": {"type": "integer", "format": "int32", "title": "- 1: 当前会话未结束\n - 2: 当前会话已经被其他会话替代-已结束\n - 3: 当前会话已经超过可聊天规定时限-已结束", "enum": [1, 2, 3]}, "aiChatInfo": {"type": "object", "title": "对话管理端信息", "properties": {"assistant_id": {"type": "string", "format": "uint64"}, "assistant_name": {"type": "string"}, "avg_duration": {"type": "number", "format": "float"}, "chat_state": {"$ref": "#/definitions/aiChatCurrentState"}, "chat_type": {"$ref": "#/definitions/aiChatType"}, "create_by": {"$ref": "#/definitions/iamUserInfo"}, "create_date": {"type": "string", "format": "date-time"}, "doc_hits": {"type": "number", "format": "float"}, "id": {"type": "string", "format": "uint64"}, "is_manual": {"type": "integer", "format": "int32", "title": "是否转过人工服务"}, "labels": {"type": "array", "title": "自定义标签kv对", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "question_cnt": {"type": "integer", "format": "int64", "title": "对话中问题数量"}, "rating_scale": {"$ref": "#/definitions/aiRatingScale"}, "region_code": {"type": "string"}, "reject_job_result": {"type": "integer", "format": "int64"}, "support_type": {"title": "当前服务状态", "$ref": "#/definitions/aiChatSupportType"}, "title": {"type": "string"}, "update_date": {"type": "string", "format": "date-time"}}}, "aiChatLiveAgentInfo": {"type": "object", "properties": {"chat_id": {"type": "string", "format": "uint64", "title": "会话ID"}, "current_live_agent": {"title": "当前正在会话中的客服", "$ref": "#/definitions/ChatLiveAgentInfoLiveAgentInfo"}, "live_agents": {"type": "array", "title": "人工客服列表", "items": {"type": "object", "$ref": "#/definitions/ChatLiveAgentInfoLiveAgentInfo"}}}}, "aiChatMessage": {"type": "object", "properties": {"answer_draft_id": {"type": "string", "format": "uint64", "title": "answer草稿id"}, "answer_index": {"type": "integer", "format": "int32", "title": "多任务索引"}, "ask_type": {"$ref": "#/definitions/aiQuestionAskType"}, "assistant_id": {"type": "string", "format": "uint64"}, "chat_id": {"type": "string", "format": "uint64"}, "collection_snapshot": {"$ref": "#/definitions/aiMessageCollectionSnapshot"}, "create_by": {"type": "string", "format": "uint64"}, "create_date": {"type": "string", "format": "date-time"}, "doc_match_pattern": {"$ref": "#/definitions/aiDocMatchPattern"}, "doc_names": {"type": "array", "items": {"type": "string"}}, "doc_snapshot": {"title": "doc 快照", "$ref": "#/definitions/aiMessageDocSnapshot"}, "end_time": {"type": "string", "format": "date-time"}, "feedback_id": {"type": "string", "format": "uint64", "title": "教学反馈ID"}, "files": {"type": "array", "title": "问题附件", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageFile"}}, "final_query": {"type": "string"}, "final_search_query": {"type": "string"}, "has_user_feedback": {"type": "boolean", "title": "是否有用户反馈"}, "history_ignore_id": {"type": "string", "format": "uint64"}, "id": {"type": "string", "format": "uint64"}, "image_url": {"type": "array", "items": {"type": "string"}}, "is_file_ready": {"type": "boolean", "title": "文件解析成功"}, "lang": {"type": "string"}, "last_operation_type": {"title": "最后一次操作", "$ref": "#/definitions/aiChatOperationType"}, "last_operator": {"title": "最后一次操作", "$ref": "#/definitions/aiChatMessageOperator"}, "link": {"type": "string"}, "live_agent_name": {"type": "string"}, "logs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageLog"}}, "prompt_prefix": {"type": "string"}, "prompt_type": {"type": "string", "title": "问题类型"}, "publish_hash_id": {"type": "string", "title": "推送消息hash id"}, "question_id": {"type": "string", "format": "uint64"}, "rating_scale": {"$ref": "#/definitions/aiRatingScale"}, "ref_file_names": {"type": "array", "items": {"type": "string"}}, "reject_reason": {"type": "string"}, "show_type": {"type": "integer", "format": "int32"}, "sql_query": {"type": "string"}, "start_time": {"type": "string", "format": "date-time"}, "state": {"$ref": "#/definitions/aiChatMessageState"}, "suggest_question": {"type": "array", "items": {"type": "string"}}, "suggestion_mode": {"$ref": "#/definitions/aiAskSuggestionMode"}, "task": {"title": "当前所执行的任务", "$ref": "#/definitions/aiChatMessageTask"}, "text": {"type": "string"}, "think": {"type": "string", "title": "深度思考"}, "think_duration": {"type": "integer", "format": "int32"}, "type": {"$ref": "#/definitions/aiChatMessageType"}, "ugcs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiMessageUgc"}}, "wait_answer": {"type": "boolean"}}}, "aiChatMessageContentFilterItem": {"type": "object", "properties": {"field": {"type": "string"}, "tags": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiMessageTag"}}, "value": {"type": "string"}}}, "aiChatMessageDoc": {"type": "object", "properties": {"contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}, "data_type": {"type": "integer", "format": "int64"}, "doc_match_pattern": {"$ref": "#/definitions/aiDocMatchPattern"}, "file_name": {"type": "string"}, "id": {"type": "string", "format": "uint64"}, "index_text": {"type": "string"}, "rag_filename": {"type": "string"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "text": {"type": "string"}, "ugc_id": {"type": "string", "format": "uint64"}, "ugc_type": {"$ref": "#/definitions/baseDataType"}, "update_by": {"$ref": "#/definitions/aiOperator"}, "url": {"type": "string"}}}, "aiChatMessageFile": {"type": "object", "title": "文件信息", "properties": {"id": {"type": "string", "format": "uint64", "title": "文件ID"}, "parsed_url": {"type": "string", "title": "解析后的URL"}, "state": {"$ref": "#/definitions/aiChatMessageFileState"}, "url": {"type": "string", "title": "文件URL"}}}, "aiChatMessageFileState": {"type": "integer", "format": "int32", "title": "- 1: 文件解析中\n - 2: 文件解析成功\n - 3: 文件解析失败", "enum": [1, 2, 3]}, "aiChatMessageLog": {"type": "object", "properties": {"code": {"type": "string", "format": "uint64"}, "config_snapshot": {"type": "string"}, "create_date": {"type": "string", "format": "date-time"}, "end_time": {"type": "string", "format": "date-time"}, "enhancement": {"type": "string"}, "fetch_resp_time": {"type": "string", "format": "date-time"}, "gpt": {"type": "string"}, "message_id": {"type": "string", "format": "uint64"}, "ref": {"type": "string"}, "request_text": {"type": "string"}, "sql_query": {"type": "string"}, "start_time": {"type": "string", "format": "date-time"}, "type": {"$ref": "#/definitions/aiChatMessageType"}}}, "aiChatMessageOperator": {"type": "object", "properties": {"hash_id": {"type": "string"}, "operation_params": {"type": "string"}, "operation_type": {"$ref": "#/definitions/aiChatOperationType"}, "stop_chunk_state": {"type": "integer", "format": "int32"}, "stop_text": {"type": "string"}, "stop_think": {"type": "string"}}}, "aiChatMessageState": {"type": "integer", "format": "int32", "title": "- 1: 消息未发送标识\n - 2: 消息已经发送标识\n - 3: 默认消息已经发送标识\n - 4: 努力思考\n - 5: 整理答案\n - 6: 停止回答\n - 7: 回答推流中\n - 8: 推流全部完成\n - 9: 思考过程推流\n - 10: 切片推流完成\n - 12: 参考资料消息\n - 13: 建议问题", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13]}, "aiChatMessageTask": {"type": "object", "properties": {"order": {"type": "integer", "format": "int32"}, "pipeline_id": {"type": "string", "format": "uint64"}, "state": {"$ref": "#/definitions/aiPipelineTaskState"}, "task_id": {"type": "string", "format": "uint64"}}}, "aiChatMessageType": {"type": "integer", "format": "int32", "title": "- 1: 用户消息\n - 2: 数据库查询\n - 3: collection查询\n - 4: 搜索引擎查询\n - 5: 系统错误\n - 6: 敏感信息错误\n - 7: 超时错误\n - 8: 取消回复\n - 9: 预定会话聊天回复\n - 10: 人工客服消息\n - 11: 多模态消息\n - 12: 清空上下文\n - 13: 建议问题\n - 14: 转人工二维码\n - 15: 回答草稿", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}, "aiChatModelOption": {"type": "object", "title": "聊天模型选项", "properties": {"disable_in_console": {"type": "boolean", "title": "是否在用户后台禁用"}, "model": {"type": "string", "title": "模型"}, "only_non_stream": {"type": "boolean", "title": "仅支持非流式"}, "support_think": {"type": "boolean", "title": "是否支持思考"}}}, "aiChatOperationType": {"description": "- 1: 正常回答\n - 2: 停止回答\n - 3: 停止思考\n - 4: 重新回答", "type": "integer", "format": "int32", "title": "会话操作类型", "enum": [1, 2, 3, 4]}, "aiChatSendRecordInfo": {"type": "object", "properties": {"content": {"type": "string", "title": "记录内容"}, "doc_match_pattern": {"$ref": "#/definitions/aiDocMatchPattern"}, "id": {"type": "string", "format": "uint64", "title": "记录id"}, "image_url": {"type": "array", "items": {"type": "string"}}, "message_id": {"type": "string", "format": "uint64", "title": "消息id"}, "message_rating_scale": {"title": "记录对应的消息的评价信息", "$ref": "#/definitions/aiRatingScale"}, "message_type": {"title": "消息类型", "$ref": "#/definitions/aiChatMessageType"}, "record_type": {"title": "类型 1 用户询问 2 助手回答-text 3 助手回答-menu 4 助手回答建议问题菜单消息", "$ref": "#/definitions/aiAiRecordType"}, "reject_reason": {"type": "string"}, "send_date": {"type": "string", "format": "date-time", "title": "发送时间"}, "show_type": {"type": "integer", "format": "int32", "title": "显示状态"}, "suggest_questions": {"type": "array", "items": {"type": "string"}}, "suggestion_mode": {"$ref": "#/definitions/aiAskSuggestionMode"}}}, "aiChatSupportType": {"type": "integer", "format": "int32", "title": "- 1: ai聊天\n - 2: 客服聊天", "enum": [1, 2]}, "aiChatType": {"description": "- 1: web\n - 2: 微信公众号\n - 3: whatsapp\n - 4: 小程序", "type": "integer", "format": "int32", "title": "后续会改为使用助手表的channel，如果是新功能就不要使用这个枚举了", "enum": [1, 2, 3, 4]}, "aiChunkItem": {"type": "object", "title": "文档分段信息", "properties": {"content": {"type": "string", "title": "分段内容"}, "len": {"type": "integer", "format": "int64", "title": "内容长度"}, "start": {"type": "integer", "format": "int64", "title": "起始索引位置"}}}, "aiCollection": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}}}, "aiCollectionQA": {"type": "object", "properties": {"answer": {"type": "string", "title": "答案"}, "assistants": {"type": "array", "title": "用户端", "items": {"type": "object", "$ref": "#/definitions/tanliveaiAssistant"}}, "contributor": {"type": "array", "title": "贡献者", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "create_by": {"$ref": "#/definitions/aiDocOperator"}, "create_date": {"type": "string", "format": "date-time"}, "embedding_state": {"$ref": "#/definitions/aiDocEmbeddingState"}, "has_repeated": {"type": "boolean"}, "hit_count": {"type": "integer", "format": "int64", "title": "命中次数"}, "id": {"type": "string", "format": "uint64"}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "match_patterns": {"type": "array", "title": "匹配模式", "items": {"$ref": "#/definitions/aiDocMatchPattern"}}, "question": {"type": "string", "title": "问题"}, "question_oversize": {"type": "boolean"}, "received_share": {"type": "boolean", "title": "是否收到分享"}, "reference": {"type": "array", "title": "参考资料", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "shared_states": {"type": "array", "title": "分享的用户端", "items": {"type": "object", "$ref": "#/definitions/aiDocAssistantState"}}, "shared_teams": {"type": "array", "title": "分享的团队", "items": {"type": "object", "$ref": "#/definitions/aiDocShareTeamReceiver"}}, "shared_users": {"type": "array", "title": "分享的用户", "items": {"type": "object", "$ref": "#/definitions/aiDocShareUserReceiver"}}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}, "state": {"title": "状态(不包含助手对应的状态)", "$ref": "#/definitions/tanliveaiDocState"}, "states": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocAssistantState"}}, "update_by": {"$ref": "#/definitions/aiDocOperator"}, "update_date": {"type": "string", "format": "date-time"}}}, "aiCollectionTextFile": {"type": "object", "properties": {"assistants": {"type": "array", "title": "用户端", "items": {"type": "object", "$ref": "#/definitions/tanliveaiAssistant"}}, "contributor": {"type": "array", "title": "贡献者", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "create_by": {"$ref": "#/definitions/aiDocOperator"}, "create_date": {"type": "string", "format": "date-time"}, "data_source_state": {"type": "integer", "format": "int64", "title": "外部数据源信息"}, "download_as_ref": {"title": "是否可以作为参考资料下载", "$ref": "#/definitions/aiDocFileDownloadAsRef"}, "embedding_state": {"$ref": "#/definitions/aiDocEmbeddingState"}, "file_name": {"type": "string", "title": "文件/文本名称"}, "has_over_sized_tables": {"type": "boolean", "title": "知识提示\n是否有超长标题表格"}, "has_repeated": {"type": "boolean", "title": "是否内容重复（租户内）"}, "hit_count": {"type": "integer", "format": "int64", "title": "命中次数"}, "id": {"type": "string", "format": "uint64"}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "parse_mode": {"$ref": "#/definitions/aiDocParseMode"}, "parse_progress": {"type": "number", "format": "float", "title": "解析进度，0.5 = 50%"}, "received_share": {"type": "boolean", "title": "是否收到分享"}, "reference": {"type": "array", "title": "参考资料", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "shared_states": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocAssistantState"}}, "shared_teams": {"type": "array", "title": "分享的团队", "items": {"type": "object", "$ref": "#/definitions/aiDocShareTeamReceiver"}}, "shared_users": {"type": "array", "title": "分享的用户", "items": {"type": "object", "$ref": "#/definitions/aiDocShareUserReceiver"}}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}, "state": {"title": "状态(不包含助手对应的状态)", "$ref": "#/definitions/tanliveaiDocState"}, "states": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocAssistantState"}}, "text": {"type": "string", "title": "文件/文本内容"}, "ugc_id": {"type": "string", "format": "uint64", "title": "ugc的id"}, "ugc_type": {"title": "ugc类型", "$ref": "#/definitions/baseDataType"}, "update_by": {"$ref": "#/definitions/aiDocOperator"}, "update_date": {"type": "string", "format": "date-time"}, "url": {"type": "string", "title": "文件url"}}}, "aiContributor": {"type": "object", "properties": {"assistant_ids": {"type": "array", "title": "贡献者所在的助手", "items": {"type": "string", "format": "uint64"}}, "full_name": {"type": "string", "title": "team full name"}, "id": {"type": "string", "format": "uint64", "title": "账户id\n个人账户: 个人账户id\n团队账户: 团队账户id\n运营端: 运营端账户id"}, "is_published": {"type": "boolean", "title": "是否已发布"}, "level": {"title": "共创等级", "$ref": "#/definitions/teamTeamLevel"}, "text": {"type": "string", "title": "自定义纯文本 或者 个人/团队/运营端账户名称"}, "type": {"$ref": "#/definitions/baseIdentityType"}}}, "aiCustomLabel": {"type": "object", "title": "AI对话的标签", "properties": {"create_by": {"type": "string", "format": "uint64", "title": "创建人"}, "id": {"type": "string", "format": "uint64", "title": "标签id"}, "key": {"type": "string", "title": "标签key"}, "next_label_id": {"type": "string", "format": "uint64"}, "next_label_name": {"type": "string"}, "type": {"title": "标签类型", "$ref": "#/definitions/aiCustomLabelType"}, "update_by": {"type": "string", "format": "uint64", "title": "更新人"}, "value": {"$ref": "#/definitions/aiLabelValue"}}}, "aiCustomLabelObjectType": {"description": "- 1: AI 对话\n - 2: 知识库文档文本与文件\n - 3: 知识库文档QA\n - 4: 腾讯云文档导入的文本文件\n - 5: SQL数据导入的文本文件", "type": "integer", "format": "int32", "title": "标签对象类型", "enum": [1, 2, 3, 4, 5]}, "aiCustomLabelType": {"description": "- 1: 任意纯文本\n - 2: 字符串枚举(单选)\n - 3: 字符串枚举(多选)\n - 4: int\n - 5: uint\n - 6: float\n - 7: 年\n - 8: 年月\n - 9: 年月日\n - 10: 日期时间(年月日和时间)\n - 11: 时间", "type": "integer", "format": "int32", "title": "标签类型", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]}, "aiDocAssistantState": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64"}, "is_shared": {"type": "integer", "format": "int64", "title": "1否 2是"}, "state": {"$ref": "#/definitions/tanliveaiDocState"}}}, "aiDocChunkTask": {"type": "object", "title": "文段分段任务", "properties": {"id": {"type": "string", "format": "uint64", "title": "任务ID"}, "state": {"title": "任务状态", "$ref": "#/definitions/aiDocChunkTaskState"}}}, "aiDocChunkTaskState": {"description": "- 1: 执行中\n - 2: 已完成", "type": "integer", "format": "int32", "title": "文档分段任务状态", "enum": [1, 2]}, "aiDocDataSource": {"description": "- 1: tanlive ugc数据\n - 2: tanlive 知识库数据\n - 3: 腾讯云文档\n - 4: SQL 数据", "type": "integer", "format": "int32", "title": "知识的数据来源", "enum": [1, 2, 3, 4]}, "aiDocEmbeddingState": {"type": "integer", "format": "int32", "title": "- 1: 同步中\n - 2: 同步完成\n - 3: 删除中", "enum": [1, 2, 3]}, "aiDocFileDownloadAsRef": {"description": "- 1: 可下载\n - 2: 仅显示文件名\n - 3: 直接发送\n - 4: 隐藏", "type": "integer", "format": "int32", "title": "文本文件是否能作为参考资料下载", "enum": [1, 2, 3, 4]}, "aiDocMatchPattern": {"description": "- 1: 大模型召回\n - 2: 完全匹配\n - 3: 忽略标点匹配\n - 4: 未命中\n - 5: 包含关键字", "type": "integer", "format": "int32", "title": "匹配模式", "enum": [1, 2, 3, 4, 5]}, "aiDocOperator": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "用户账户: 用户id\n团队账户: 用户id\n运营端账户: 运营端用户id"}, "team_name": {"type": "string", "title": "用户所属团队名称"}, "type": {"$ref": "#/definitions/baseIdentityType"}, "user_id": {"type": "string", "format": "uint64", "title": "用户id，只有为团队用户时，才需要"}, "username": {"type": "string", "title": "用户名称"}}}, "aiDocParseMode": {"description": "- 1: 智能解析\n - 2: 文件解析\n - 3: 图像解析\n - 4: 表格解析", "type": "integer", "format": "int32", "title": "文档解析模式", "enum": [1, 2, 3, 4]}, "aiDocReference": {"type": "object", "title": "参考文献", "properties": {"id": {"type": "string", "format": "uint64", "title": "引用doc的参考文献"}, "name": {"type": "string"}, "show_type": {"title": "仅用于控制对话展示", "$ref": "#/definitions/aiDocFileDownloadAsRef"}, "text": {"type": "string", "title": "纯文本参考文献"}, "url": {"type": "string"}}}, "aiDocShareAcceptState": {"description": "- 1: 接收分享\n - 2: 不接收分享", "type": "integer", "format": "int32", "title": "是否接收知识库分享", "enum": [1, 2]}, "aiDocShareState": {"description": "- 1: 启用\n - 2: 禁用\n - 3: 不接收", "type": "integer", "format": "int32", "title": "知识库分享状态", "enum": [1, 2, 3]}, "aiDocShareTeamReceiver": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "name_en": {"type": "string"}}}, "aiDocShareUserReceiver": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}}}, "aiDocSharedState": {"description": "- 1: 启用\n - 2: 禁用\n - 3: 收到分享", "type": "integer", "format": "int32", "title": "知识库文档收到分享状态", "enum": [1, 2, 3]}, "aiDocType": {"description": "- 1: QA\n - 2: 文本\n - 3: 文件", "type": "integer", "format": "int32", "title": "文档类型", "enum": [1, 2, 3]}, "aiEmbeddingModelCount": {"type": "object", "title": "向量化模型计数", "properties": {"collection_lang": {"type": "string", "title": "向量化模型"}, "count": {"type": "integer", "format": "int64", "title": "数量"}, "embedding_model_name": {"type": "string", "title": "向量化模型名称"}}}, "aiEmbeddingModelOption": {"type": "object", "title": "向量化模型选项", "properties": {"embedding_vector_length": {"type": "integer", "format": "int32", "title": "embedding向量长度"}, "en_default": {"title": "英文默认配置", "$ref": "#/definitions/aiAssistantChunkConfig"}, "en_max_tokens_per_char": {"type": "number", "format": "float", "title": "1个英文字符约对应多少个token（上限）"}, "en_min_tokens_per_char": {"type": "number", "format": "float", "title": "1个英文字符约对应多少个token（下限）"}, "model": {"type": "string", "title": "模型名称"}, "name": {"type": "string", "title": "名称"}, "recommended": {"type": "boolean", "title": "是否推荐"}, "tech_overlap_max_tokens": {"type": "integer", "format": "int32", "title": "技术overlap最大长度（token）"}, "tech_overlap_min_tokens": {"type": "integer", "format": "int32", "title": "技术overlap最小长度（token）"}, "tech_seg_max_tokens": {"type": "integer", "format": "int32", "title": "技术最大分段长度（token）"}, "tech_seg_min_tokens": {"type": "integer", "format": "int32", "title": "技术最小分段长度（token）"}, "user_overlap_tokens": {"type": "integer", "format": "int32", "title": "用户overlap长度默认值（token）"}, "user_seg_max_tokens": {"type": "integer", "format": "int32", "title": "用户最大分段长度默认值（token）"}, "user_seg_min_tokens": {"type": "integer", "format": "int32", "title": "用户最小分段长度默认值（token）"}, "zh_default": {"title": "中文默认配置", "$ref": "#/definitions/aiAssistantChunkConfig"}, "zh_max_tokens_per_char": {"type": "number", "format": "float", "title": "1个中文字符约对应多少个token（上限）"}, "zh_min_tokens_per_char": {"type": "number", "format": "float", "title": "1个中文字符约对应多少个token（下限）"}}}, "aiEventChatHashMessage": {"type": "object", "properties": {"answer_index": {"type": "integer", "format": "int32", "title": "多任务索引"}, "assistant_id": {"type": "string"}, "chat_id": {"type": "string"}, "create_date": {"type": "string", "format": "date-time"}, "custom_question_id": {"type": "string", "format": "uint64"}, "doc_final_query": {"type": "string"}, "doc_match_pattern": {"$ref": "#/definitions/aiDocMatchPattern"}, "docs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/EventChatHashMessageEventMessageDoc"}}, "id": {"type": "string"}, "lang": {"type": "string"}, "last_operation_type": {"title": "最后一次操作", "$ref": "#/definitions/aiChatOperationType"}, "link": {"type": "string"}, "process_time": {"$ref": "#/definitions/baseTimeRange"}, "prompt_type": {"type": "string", "title": "问题类型"}, "question_id": {"type": "string"}, "rating_scale": {"$ref": "#/definitions/aiRatingScale"}, "reject_reason": {"type": "string"}, "show_type": {"type": "integer", "format": "int32"}, "sql_query": {"type": "string"}, "state": {"type": "integer", "format": "int32"}, "suggest_count": {"type": "integer", "format": "int32"}, "suggest_questions": {"type": "array", "items": {"type": "string"}}, "suggestion_mode": {"$ref": "#/definitions/aiAskSuggestionMode"}, "text": {"type": "string"}, "think": {"type": "string"}, "think_duration": {"type": "integer", "format": "int32"}, "type": {"$ref": "#/definitions/aiChatMessageType"}, "ugcs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/EventChatHashMessageEventMessageUgc"}}, "wait_answer": {"type": "boolean"}}}, "aiEventChatMessage": {"type": "object", "properties": {"answer_index": {"type": "integer", "format": "int32", "title": "多任务索引"}, "answers": {"type": "array", "title": "关联的回答", "items": {"type": "object", "$ref": "#/definitions/aiEventChatMessage"}}, "ask_type": {"$ref": "#/definitions/aiQuestionAskType"}, "assistant_id": {"type": "string", "format": "uint64"}, "chat_id": {"type": "string", "format": "uint64"}, "create_by": {"type": "string", "format": "uint64"}, "create_date": {"type": "string", "format": "date-time"}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}, "doc_final_query": {"type": "string"}, "doc_match_pattern": {"$ref": "#/definitions/aiDocMatchPattern"}, "docs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageDoc"}}, "end_time": {"type": "string", "format": "date-time"}, "feedback_id": {"type": "string", "format": "uint64", "title": "教学反馈ID"}, "files": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageFile"}}, "has_user_feedback": {"type": "boolean", "title": "是否有用户反馈"}, "id": {"type": "string", "format": "uint64"}, "image_url": {"type": "array", "items": {"type": "string"}}, "is_agent_command": {"type": "boolean", "title": "是否是agent回答"}, "is_file_ready": {"type": "boolean", "title": "文件解析成功"}, "lang": {"type": "string"}, "last_operation_type": {"title": "最后一次操作", "$ref": "#/definitions/aiChatOperationType"}, "link": {"type": "string"}, "process_time": {"$ref": "#/definitions/baseTimeRange"}, "prompt_type": {"type": "string", "title": "问题类型"}, "question_id": {"type": "string", "format": "uint64"}, "rating_scale": {"$ref": "#/definitions/aiRatingScale"}, "reject_reason": {"type": "string"}, "show_type": {"type": "integer", "format": "int32"}, "sql_query": {"type": "string"}, "start_time": {"type": "string", "format": "date-time"}, "state": {"$ref": "#/definitions/aiChatMessageState"}, "suggest_count": {"type": "integer", "format": "int32"}, "suggest_questions": {"type": "array", "items": {"type": "string"}}, "suggestion_mode": {"$ref": "#/definitions/aiAskSuggestionMode"}, "text": {"type": "string"}, "think": {"type": "string"}, "think_duration": {"type": "integer", "format": "int32"}, "type": {"$ref": "#/definitions/aiChatMessageType"}, "ugcs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiMessageUgc"}}, "wait_answer": {"type": "boolean"}}}, "aiExportField": {"type": "object", "properties": {"example": {"type": "string"}, "id": {"type": "string", "format": "uint64"}, "key": {"type": "string"}, "label": {"type": "string"}, "rule": {"type": "string"}, "tips": {"type": "string"}}}, "aiExportTask": {"type": "object", "properties": {"create_by": {"type": "string", "format": "uint64"}, "create_date": {"type": "string", "format": "date-time"}, "extra_info": {"type": "string"}, "fields_snapshot": {"type": "string"}, "filter_snapshot": {"type": "string"}, "id": {"type": "string", "format": "uint64"}, "last_update_date": {"type": "string", "format": "date-time"}, "max_batch_size": {"type": "integer", "format": "int32", "title": "单次请求最大数量"}, "max_response_threshold": {"type": "integer", "format": "int32", "title": "返回数据最大阈值"}, "operation_type": {"$ref": "#/definitions/aiTaskOperationType"}, "paths": {"type": "array", "items": {"type": "string"}}, "state": {"$ref": "#/definitions/aiExportTaskState"}, "type": {"$ref": "#/definitions/aiExportTaskType"}, "url": {"type": "string"}}}, "aiExportTaskState": {"type": "integer", "format": "int32", "title": "- 1: 导出中\n - 2: 已完成\n - 3: 失败", "enum": [1, 2, 3]}, "aiExportTaskType": {"type": "integer", "format": "int32", "title": "- 1: QA1\n - 2: 文本文件\n - 3: 会话消息\n - 4: 会话", "enum": [1, 2, 3, 4]}, "aiExternalSourceUserAuthState": {"type": "integer", "format": "int32", "title": "- 1: 已授权\n - 2: 未授权\n - 3: 已失效\n - 4: 已取消", "enum": [1, 2, 3, 4]}, "aiFeedback": {"type": "object", "title": "用户反馈", "properties": {"answer": {"type": "string", "title": "答案"}, "answer_id": {"type": "string", "format": "uint64", "title": "原始答案ID"}, "answer_rating": {"title": "回答评价", "$ref": "#/definitions/aiFeedbackAnswerRating"}, "assistant_id": {"type": "string", "format": "uint64", "title": "助手id"}, "assistant_name": {"type": "string", "title": "助手名称"}, "chat_id": {"type": "string", "format": "uint64", "title": "对话ID"}, "create_date": {"type": "string", "format": "date-time", "title": "创建时间"}, "create_identity": {"title": "创建人身份", "$ref": "#/definitions/baseIdentity"}, "has_mgmt_feedback": {"type": "boolean", "title": "是否有碳LIVE反馈"}, "has_op_feedback": {"type": "boolean", "title": "是否有运营反馈"}, "has_user_feedback": {"type": "boolean", "title": "是否有用户反馈"}, "hit_expected_doc": {"type": "boolean", "title": "是否命中预期知识"}, "id": {"type": "string", "format": "uint64", "title": "反馈ID"}, "message_id": {"type": "string", "format": "uint64", "title": "消息ID"}, "mgmt_comment": {"title": "碳LIVE备注", "$ref": "#/definitions/aiFeedbackComment"}, "mgmt_feedback": {"title": "碳LIVE反馈", "$ref": "#/definitions/aiFeedbackComment"}, "mgmt_feedback_at": {"type": "string", "format": "date-time", "title": "碳LIVE反馈时间"}, "mgmt_feedback_by": {"title": "碳LIVE反馈人", "$ref": "#/definitions/baseIdentity"}, "op_comment": {"title": "分析备注", "$ref": "#/definitions/aiFeedbackComment"}, "op_feedback_at": {"type": "string", "format": "date-time", "title": "运营反馈时间"}, "op_feedback_by": {"title": "运营反馈人", "$ref": "#/definitions/baseIdentity"}, "question": {"type": "string", "title": "问题"}, "question_id": {"type": "string", "format": "uint64", "title": "原始问题ID"}, "state": {"title": "状态", "$ref": "#/definitions/aiFeedbackState"}, "update_date": {"type": "string", "format": "date-time", "title": "更新时间"}, "update_identity": {"title": "更新人身份", "$ref": "#/definitions/baseIdentity"}, "user_feedback_at": {"type": "string", "format": "date-time", "title": "用户反馈时间"}, "user_feedback_by": {"title": "用户反馈人", "$ref": "#/definitions/baseIdentity"}}}, "aiFeedbackAction": {"description": "- 1: 已读（已废弃）\n - 2: 采用\n - 3: 创建用户反馈\n - 4: 创建运营反馈\n - 5: 创建碳LIVE反馈\n - 6: 更新用户反馈\n - 7: 更新运营反馈\n - 8: 更新碳LIVE反馈", "type": "integer", "format": "int32", "title": "反馈操作", "enum": [1, 2, 3, 4, 5, 6, 7, 8]}, "aiFeedbackAnswerRating": {"description": "- 1: 好\n - 2: 坏", "type": "integer", "format": "int32", "title": "回答评价", "enum": [1, 2]}, "aiFeedbackComment": {"type": "object", "title": "反馈备注", "properties": {"content": {"type": "string", "title": "内容"}, "files": {"type": "array", "title": "文件列表", "items": {"type": "object", "$ref": "#/definitions/FeedbackCommentFile"}}}}, "aiFeedbackLog": {"type": "object", "title": "反馈操作日志", "properties": {"action": {"title": "操作", "$ref": "#/definitions/aiFeedbackAction"}, "create_date": {"type": "string", "format": "date-time", "title": "操作时间"}, "create_identity": {"title": "操作人", "$ref": "#/definitions/baseIdentity"}, "feedback_id": {"type": "string", "format": "uint64", "title": "反馈ID"}, "id": {"type": "string", "format": "uint64", "title": "主键"}}}, "aiFeedbackState": {"description": "- 1: 未读\n - 2: 已读\n - 3: 已采用", "type": "integer", "format": "int32", "title": "反馈状态", "enum": [1, 2, 3]}, "aiFullAssistant": {"type": "object", "title": "完整助手信息", "properties": {"assistant": {"title": "助手详情", "$ref": "#/definitions/aiAssistantV2"}, "terms_confirmed": {"type": "boolean", "title": "是否确认协议"}}}, "aiFullFeedbackLog": {"type": "object", "title": "完整反馈", "properties": {"feedback": {"title": "反馈", "$ref": "#/definitions/aiFeedback"}, "log": {"title": "日志", "$ref": "#/definitions/aiFeedbackLog"}, "original_question": {"title": "原始问题", "$ref": "#/definitions/aiChatMessage"}}}, "aiInteractiveCode": {"description": "- 1: 人工\n - 2: 重新回答\n - 3: 清空上下文\n - 4: 读配料表\n - 5: 读检测报告\n - 6: 贡献知识库", "type": "integer", "format": "int32", "title": "互动暗号", "enum": [1, 2, 3, 4, 5, 6]}, "aiInteractiveCodeOption": {"type": "object", "title": "互动暗号选项", "properties": {"code": {"title": "编号", "$ref": "#/definitions/aiInteractiveCode"}, "default_en": {"type": "string", "title": "默认英文值"}, "default_pre_en": {"type": "string", "title": "默认英文前缀"}, "default_pre_zh": {"type": "string", "title": "默认中文前缀"}, "default_zh": {"type": "string", "title": "默认中文值"}, "deletable": {"type": "boolean", "title": "是否可删除"}, "text": {"type": "string", "title": "文本"}}}, "aiLabelFilter": {"type": "object", "properties": {"eq": {"$ref": "#/definitions/aiLabelValue"}, "gte": {"$ref": "#/definitions/aiLabelValue"}, "id": {"type": "string", "format": "uint64", "title": "标签id"}, "in": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiLabelValue"}}, "like": {"$ref": "#/definitions/aiLabelValue"}, "lte": {"$ref": "#/definitions/aiLabelValue"}, "op": {"$ref": "#/definitions/aiLabelFilterOp"}}}, "aiLabelFilterOp": {"type": "integer", "format": "int32", "title": "- 1: 等于\n - 2: IN\n - 3: 大于等于\n - 4: 小于等于\n - 5: LIKE模糊搜索", "enum": [1, 2, 3, 4, 5]}, "aiLabelValue": {"type": "object", "title": "标签取值", "properties": {"datetime_value": {"type": "string", "format": "int64", "title": "日期时间(年月日和时间)"}, "enum_m_value": {"type": "string", "title": "字符串枚举(多选)"}, "enum_value": {"type": "string", "title": "字符串枚举(单选)"}, "float_value": {"type": "number", "format": "double", "title": "浮点型"}, "int_value": {"type": "string", "format": "int64", "title": "整型值"}, "text_value": {"type": "string", "title": "任意纯文本"}, "time_value": {"type": "string", "format": "int64", "title": "时间"}, "uint_value": {"type": "string", "format": "uint64", "title": "无符号整形"}, "y_value": {"type": "string", "format": "int64", "title": "年"}, "ym_value": {"type": "string", "format": "int64", "title": "年月"}, "ymd_value": {"type": "string", "format": "int64", "title": "年月日"}}}, "aiListDocFilterType": {"type": "integer", "format": "int32", "title": "- 1: qa\n - 2: 文本/文件\n - 3: 系统数据", "enum": [1, 2, 3]}, "aiManualChunkPara": {"type": "object", "title": "文档手动分段参数", "properties": {"assistant_chunks": {"type": "array", "title": "新分段列表", "items": {"type": "object", "$ref": "#/definitions/aiAssistantChunks"}}}}, "aiMessageCollectionSnapshot": {"type": "object", "properties": {"clean_chunks": {"type": "boolean"}, "end_time": {"type": "string", "format": "date-time"}, "items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveaiSearchCollectionItem"}}, "message_id": {"type": "string", "format": "uint64"}, "start_time": {"type": "string", "format": "date-time"}}}, "aiMessageDocSnapshot": {"type": "object", "properties": {"docs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageDoc"}}, "message_id": {"type": "string", "format": "uint64"}}}, "aiMessageTag": {"type": "object", "properties": {"data_type": {"type": "integer", "format": "int32"}, "id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "taggable_id": {"type": "string", "format": "uint64"}, "taggable_type": {"type": "integer", "format": "int32"}, "type": {"type": "integer", "format": "int32"}}}, "aiMessageUgc": {"type": "object", "properties": {"cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiMessageUgcCard"}}, "filter": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageContentFilterItem"}}, "is_ugc_link": {"type": "boolean"}, "ugc_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "ugc_type": {"$ref": "#/definitions/baseDataType"}}}, "aiMessageUgcCard": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "logo_url": {"type": "string"}, "name": {"type": "string"}, "tags": {"type": "string"}}}, "aiOperator": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "用户账户: 用户id\n团队账户: 用户id\n运营端账户: 运营端用户id"}, "type": {"$ref": "#/definitions/baseIdentityType"}, "user_id": {"type": "string", "format": "uint64", "title": "type为团队用户时，可选的传入个人的 id"}}}, "aiOrderByLabel": {"type": "object", "properties": {"desc": {"type": "boolean"}, "id": {"type": "string", "format": "uint64"}}}, "aiPipelineTaskState": {"type": "integer", "format": "int32", "title": "- 1: 进行中\n - 2: 已完成\n - 3: 失败", "enum": [1, 2, 3]}, "aiQuestionAskType": {"type": "integer", "format": "int32", "title": "- 1: 正常问答\n - 2: 重新回答(包括了用户输入的\"重新回答\"或者，用户输入了同样的问题)\n - 3: 继续回答(包括被错误识别为了转人工之后确认为继续回答，或者 发送条数到达上限后的继续回答)\n - 4: 预设问答\n - 5: 预设隐藏回答\n - 6: 文件问答\n - 7: 语音问答\n - 8: 图片问答\n - 9: 撤回问答（用户在微信端撤回消息）\n - 10: 匹配到QA的问答", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]}, "aiQuickAction": {"type": "object", "title": "助手urgent配置", "properties": {"assistants": {"type": "string", "title": "assistantIds"}, "content": {"type": "string", "title": "命令"}, "next_msg_file_forbidden": {"type": "boolean", "title": "下一条问题不能携带image"}, "type": {"type": "integer", "format": "int32", "title": "类型"}}}, "aiRatingScale": {"description": "- 1: 满意\n - 2: 一般\n - 3: 不满意", "type": "integer", "format": "int32", "title": "评价等级", "enum": [1, 2, 3]}, "aiReferenceType": {"description": "- 1: URL\n - 2: 文本\n - 3: 文件", "type": "integer", "format": "int32", "title": "参考文献类型", "enum": [1, 2, 3]}, "aiReqAutoChunkDoc": {"type": "object", "properties": {"auto_para": {"title": "自动分段参数", "$ref": "#/definitions/aiAutoChunkPara"}, "doc_id": {"type": "string", "format": "uint64", "title": "文档ID"}, "new_text": {"type": "string", "title": "新文本（如果文本未改动不需要传值）"}}}, "aiReqBatchUpdateDocAttr": {"type": "object", "properties": {"assistant_id": {"type": "array", "title": "修改关联的助手，增加的助手启用/禁用状态和已有的助手状态一致", "items": {"type": "string", "format": "uint64"}}, "contributor": {"type": "array", "title": "贡献者", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "download_as_ref": {"title": "参考资料下载方式", "$ref": "#/definitions/aiDocFileDownloadAsRef"}, "id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "mask": {"type": "string"}, "match_patterns": {"type": "array", "title": "匹配模式", "items": {"$ref": "#/definitions/aiDocMatchPattern"}}, "query_id": {"type": "string", "format": "uint64"}, "reference": {"type": "array", "title": "参考资料", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}}}, "aiReqBatchUserAssistantLimit": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64"}, "token": {"type": "array", "items": {"type": "string"}}}}, "aiReqBindMiniProgramNormalAccount": {"type": "object", "properties": {"token": {"type": "string"}, "webview_uni_token": {"type": "string"}}}, "aiReqBindMiniProgramPhoneAccount": {"type": "object", "properties": {"code": {"type": "string"}}}, "aiReqBindMiniProgramUniID": {"type": "object", "properties": {"uin_token": {"type": "string"}}}, "aiReqBindOnceUserMiniProgram": {"type": "object", "properties": {"code": {"type": "string"}}}, "aiReqBindUnitokenByCode": {"type": "object", "properties": {"code": {"type": "string"}}}, "aiReqBindUserPhoneByCode": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64"}, "code": {"type": "string"}}}, "aiReqCloneDoc": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "query_id": {"type": "string", "format": "uint64"}}}, "aiReqConfirmAiServiceTerms": {"type": "object", "properties": {"is_agreed": {"type": "boolean", "title": "是否同意"}}}, "aiReqCreateAssistantShare": {"type": "object", "properties": {"assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "doc_id": {"type": "string", "format": "uint64"}, "doc_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "query_id": {"type": "string", "format": "uint64"}, "team_id": {"type": "array", "title": "分享给团队的ID列表", "items": {"type": "string", "format": "uint64"}}, "user_id": {"type": "array", "title": "分享给个人的ID列表", "items": {"type": "string", "format": "uint64"}}}}, "aiReqCreateChat": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64", "title": "助手id"}, "image_urls": {"type": "array", "title": "图片", "items": {"type": "string"}}, "lang": {"type": "string", "title": "语言 zh en"}, "mini_program": {"type": "boolean", "title": "是否小程序"}, "publish_hash_id": {"type": "string", "title": "hash_id"}, "show_answer": {"type": "boolean", "title": "是否返回answer"}, "title": {"type": "string", "title": "会话标题"}}}, "aiReqCreateChatExportTask": {"type": "object", "properties": {"fields": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiExportField"}}, "filter": {"$ref": "#/definitions/tanlivebff_webaiReqListChat"}}}, "aiReqCreateChatMessageExportTask": {"type": "object", "properties": {"fields": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiExportField"}}, "filter": {"$ref": "#/definitions/tanlivebff_webaiReqListChat"}}}, "aiReqCreateChatQuestion": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64", "title": "助手id"}, "chat_id": {"type": "string", "format": "uint64"}, "file_urls": {"type": "array", "title": "文件urls", "items": {"type": "string"}}, "is_agent_command": {"type": "boolean", "title": "是否是agent暗号"}, "lang": {"type": "string", "title": "语言 zh en"}, "text": {"type": "string", "title": "问题"}}}, "aiReqCreateDocShareConfigReceiverAssistantUserShare": {"type": "object", "properties": {"state": {"$ref": "#/definitions/aiDocShareState"}, "team_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "user_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "aiReqCreateFeedback": {"type": "object", "properties": {"answer": {"type": "string", "title": "答案"}, "assistant_id": {"type": "string", "format": "uint64", "title": "助手ID"}, "question": {"type": "string", "title": "问题"}, "references": {"type": "array", "title": "参考文献", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiFeedbackReference"}}}}, "aiReqCreateFileExportTask": {"type": "object", "properties": {"fields": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiExportField"}}, "filter": {"$ref": "#/definitions/aiReqListTextFiles"}}}, "aiReqCreateGTBDocText": {"type": "object", "properties": {"is_pull_all": {"type": "boolean"}}}, "aiReqCreateQAs": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiReqCreateQA"}}}}, "aiReqCreateQaExportTask": {"type": "object", "properties": {"fields": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiExportField"}}, "filter": {"$ref": "#/definitions/tanlivebff_webaiReqListQA"}}}, "aiReqCreateTextFile": {"type": "object", "properties": {"assistant_id": {"type": "array", "title": "助手", "items": {"type": "string", "format": "uint64"}}, "contributor": {"type": "array", "title": "贡献者", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "data_source": {"title": "数据源", "$ref": "#/definitions/aiDocDataSource"}, "download_as_ref": {"$ref": "#/definitions/aiDocFileDownloadAsRef"}, "file_name": {"type": "string", "title": "文件/文本名称"}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "parse_mode": {"$ref": "#/definitions/aiDocParseMode"}, "parsed_url": {"type": "string", "title": "文件解析后地址"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "share_assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}, "state": {"title": "状态 1: 启用 2: 停用 状态设置只对文本有效", "$ref": "#/definitions/tanliveaiDocState"}, "text": {"type": "string", "title": "文件/文本内容"}, "type": {"type": "integer", "format": "int64", "title": "类型 2:文本 3:文件"}, "ugc_id": {"type": "string", "format": "uint64", "title": "ugc的id"}, "ugc_type": {"title": "ugc类型", "$ref": "#/definitions/baseDataType"}, "url": {"type": "string", "title": "文件url"}}}, "aiReqCreateTextFiles": {"type": "object", "properties": {"items": {"type": "array", "title": "解析模式", "items": {"type": "object", "$ref": "#/definitions/aiReqCreateTextFile"}}}}, "aiReqDeleteChat": {"type": "object", "properties": {"chat_id": {"type": "string", "format": "uint64"}}}, "aiReqDeleteDocs": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "query_id": {"type": "string", "format": "uint64"}}}, "aiReqDescribeAccountIsGTB": {"type": "object"}, "aiReqDescribeChatMessages": {"type": "object", "properties": {"chat_id": {"type": "string", "format": "uint64"}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "question_id": {"type": "string", "format": "uint64"}, "without_docs": {"type": "boolean"}}}, "aiReqDescribeChats": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64", "title": "助手id"}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}}}, "aiReqDescribeDocList": {"type": "object", "properties": {"folder_id": {"type": "string"}, "hash_user_id": {"type": "string"}, "search": {"type": "string"}, "start": {"type": "integer", "format": "int64"}, "state": {"type": "string"}, "title_name_sort": {"type": "string", "title": "asc desc"}}}, "aiReqDescribeMessageFileState": {"type": "object", "properties": {"message_id": {"type": "string", "format": "uint64"}}}, "aiReqDescribeTencentToken": {"type": "object", "properties": {"hash_user_id": {"type": "string"}}}, "aiReqFindFeedback": {"type": "object", "properties": {"feedback_id": {"type": "string", "format": "uint64", "title": "用户反馈ID"}}}, "aiReqGetAccountOnceBindStatus": {"type": "object", "properties": {"code": {"type": "string"}}}, "aiReqGetAccountUnionIdStatus": {"type": "object", "properties": {"code": {"type": "string"}}}, "aiReqGetAssistantChunkConfig": {"type": "object", "properties": {"collection_lang": {"type": "string", "title": "向量化模型"}, "doc_id": {"type": "string", "format": "uint64", "title": "文档ID"}, "limit": {"type": "integer", "format": "int64", "title": "分页大小"}, "offset": {"type": "integer", "format": "int64", "title": "分页偏移量"}}}, "aiReqGetAssistantConfig": {"type": "object", "properties": {"app_id": {"type": "string", "title": "应用ID"}, "assistant_id": {"type": "array", "title": "助手ID", "items": {"type": "string", "format": "uint64"}}, "only_tanlive_app": {"type": "boolean", "title": "仅查询碳LIVE应用助手"}, "only_tanlive_miniprogram": {"type": "boolean", "title": "仅查询碳LIVE小程序助手"}, "route_path": {"type": "string", "title": "通过路由查询"}}}, "aiReqGetAssistantsMiniprogram": {"type": "object", "properties": {"limit": {"type": "integer", "format": "int64", "title": "分页大小"}, "offset": {"type": "integer", "format": "int64", "title": "分页偏移量"}, "only_recently_use": {"type": "boolean", "title": "true 只查看最近使用的助手，如果没有则用默认配置填充"}, "search": {"type": "string", "title": "搜索助手名称或机构名称"}}}, "aiReqGetChatMessageDetail": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}}}, "aiReqGetMiniProgramAssistantLimit": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64"}}}, "aiReqGetMiniProgramAuth": {"type": "object", "properties": {"auth_id": {"type": "string"}, "ref_auth_id": {"type": "string", "title": "pc、h5来源"}}}, "aiReqGetMiniProgramLoginURL": {"type": "object", "properties": {"type": {"type": "integer", "format": "int64", "title": "0 pc 1 h5"}}}, "aiReqGetMiniProgramUserInfo": {"type": "object", "properties": {"select_token": {"type": "string"}}}, "aiReqGetMyAssistants": {"type": "object", "properties": {"assistant_id": {"type": "array", "title": "助手ID", "items": {"type": "string", "format": "uint64"}}, "limit": {"type": "integer", "format": "int64", "title": "分页大小"}, "offset": {"type": "integer", "format": "int64", "title": "分页偏移量"}}}, "aiReqGetPublicChatShare": {"type": "object", "title": "获取公开分享详情请求（无需登录）", "properties": {"share_id": {"type": "string", "title": "分享ID"}}}, "aiReqGetTextFile": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}}}, "aiReqGetWebViewToMiniProgramToken": {"type": "object"}, "aiReqImportQA": {"type": "object", "properties": {"answer": {"type": "string"}, "assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "contributor": {"type": "array", "title": "贡献者", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "force_create": {"type": "boolean", "title": "是否强制新建，如果为true则不进行重复校验，直接创建新的QA"}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "mask": {"type": "string", "title": "用于指定哪些字段需要更新"}, "match_patterns": {"type": "array", "title": "匹配模式", "items": {"$ref": "#/definitions/aiDocMatchPattern"}}, "question": {"type": "string"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "share_assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}}}, "aiReqImportQAs": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiReqImportQA"}}}}, "aiReqImportTextFile": {"type": "object", "properties": {"assistant_id": {"type": "array", "title": "助手", "items": {"type": "string", "format": "uint64"}}, "contributor": {"type": "array", "title": "贡献者", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "download_as_ref": {"title": "是否可以作为参考资料下载", "$ref": "#/definitions/aiDocFileDownloadAsRef"}, "file_name": {"type": "string", "title": "文件/文本名称"}, "force_create": {"type": "boolean", "title": "是否强制新建，如果为true则不进行重复校验，直接创建新的文本文件"}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "mask": {"type": "string", "title": "用于指定哪些字段需要更新"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "share_assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}, "text": {"type": "string", "title": "文件/文本内容"}}}, "aiReqImportTextFiles": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiReqImportTextFile"}}}}, "aiReqListCollectionFileName": {"type": "object", "properties": {"data_source": {"$ref": "#/definitions/aiDocDataSource"}, "full_search": {"type": "array", "title": "文件名精确匹配搜索", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "search": {"type": "string", "title": "文件名模糊搜索匹配"}}}, "aiReqListNebulaAssistants": {"type": "object", "properties": {"limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "search": {"type": "string"}}}, "aiReqListNebulaContributors": {"type": "object", "properties": {"assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "aiReqListOperator": {"type": "object", "properties": {"creator": {"type": "boolean", "title": "是否为创建人，false代表更新人，true代表创建人"}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}, "search": {"type": "string"}, "type": {"$ref": "#/definitions/aiListDocFilterType"}}}, "aiReqListTextFiles": {"type": "object", "properties": {"assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "create_by": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiOperator"}}, "data_source": {"title": "数据源过滤", "$ref": "#/definitions/aiDocDataSource"}, "data_source_state": {"type": "integer", "format": "int64", "title": "数据源同步状态"}, "download_as_ref": {"$ref": "#/definitions/aiDocFileDownloadAsRef"}, "embedding_state": {"title": "向量状态", "$ref": "#/definitions/aiDocEmbeddingState"}, "excluded_assistant_id": {"type": "array", "title": "不在助手中", "items": {"type": "string", "format": "uint64"}}, "group_repeated": {"type": "boolean", "title": "重复 doc 紧凑排序"}, "id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiLabelFilter"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "order_by": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/baseOrderBy"}}, "order_by_label": {"title": "自定义标签排序，只能当个标签排序", "$ref": "#/definitions/aiOrderByLabel"}, "parse_mode": {"type": "array", "items": {"$ref": "#/definitions/aiDocParseMode"}}, "parse_state": {"title": "查询解析失败的数据", "$ref": "#/definitions/tanliveaiDocState"}, "search": {"$ref": "#/definitions/aiReqListTextFilesSearch"}, "share_assistant_id": {"type": "array", "title": "分享的助手", "items": {"type": "string", "format": "uint64"}}, "share_team_id": {"type": "array", "title": "分享的团队id", "items": {"type": "string", "format": "uint64"}}, "share_user_id": {"type": "array", "title": "分享的用户id", "items": {"type": "string", "format": "uint64"}}, "shared_state": {"type": "array", "items": {"$ref": "#/definitions/aiDocSharedState"}}, "show_contributor": {"type": "integer", "format": "int64"}, "state": {"$ref": "#/definitions/tanliveaiDocState"}, "tip_filter": {"$ref": "#/definitions/aiReqListTextFilesTipFilter"}, "tql_expression": {"type": "string", "title": "TQL表达式（高级搜索）"}, "update_by": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiOperator"}}}}, "aiReqListTextFilesSearch": {"type": "object", "properties": {"file_name": {"type": "string", "title": "文件名搜索"}, "text": {"type": "string", "title": "文本内容搜索"}}}, "aiReqListTextFilesTipFilter": {"type": "object", "title": "知识提示过滤条件", "properties": {"warning": {"type": "boolean", "title": "警告条件组"}}}, "aiReqManualChunkDoc": {"type": "object", "properties": {"doc_id": {"type": "string", "format": "uint64", "title": "文档ID"}, "manual_para": {"title": "手动分段参数", "$ref": "#/definitions/aiManualChunkPara"}, "new_text": {"type": "string", "title": "新文本（如果文本未改动不需要传值）"}}}, "aiReqModifyCustomLabels": {"type": "object", "properties": {"labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "object_type": {"$ref": "#/definitions/aiCustomLabelObjectType"}}}, "aiReqOnOffDocs": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "query_id": {"type": "string", "format": "uint64"}, "state": {"$ref": "#/definitions/tanliveaiDocState"}}}, "aiReqProxyChatHtmlUrl": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64", "title": "助手id"}, "urls": {"type": "array", "items": {"type": "string"}}}}, "aiReqReceiveChatMessage": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64", "title": "助手id"}, "chat_id": {"type": "string", "format": "uint64"}, "image_urls": {"type": "array", "title": "图片", "items": {"type": "string"}}, "lang": {"type": "string", "title": "语言 zh en"}, "mini_program": {"type": "boolean", "title": "是否小程序"}, "publish_hash_id": {"type": "string", "title": "hash id"}, "question_id": {"type": "string", "format": "uint64", "title": "重新回答的问题id"}, "show_answer": {"type": "boolean", "title": "是否返回answer"}, "text": {"type": "string", "title": "问题"}}}, "aiReqResendChatMessage": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64", "title": "助手id"}, "message_id": {"type": "string", "format": "uint64", "title": "问题的id"}, "publish_hash_id": {"type": "string", "title": "hash id"}}}, "aiReqRestartExportTask": {"type": "object", "properties": {"task_id": {"type": "string", "format": "uint64"}}}, "aiReqSaveOpFeedback": {"type": "object", "properties": {"answer_id": {"type": "string", "format": "uint64", "title": "答案ID"}, "answer_rating": {"title": "AI回答评价", "$ref": "#/definitions/aiFeedbackAnswerRating"}, "doc_id": {"type": "array", "title": "预期命中的知识", "items": {"type": "string", "format": "uint64"}}, "feedback_id": {"type": "string", "format": "uint64", "title": "答案ID"}, "hit_expected_doc": {"type": "boolean", "title": "是否命中预期知识"}, "op_comment": {"title": "分析备注", "$ref": "#/definitions/aiFeedbackComment"}}}, "aiReqSaveUserFeedbackByQuestion": {"type": "object", "properties": {"answer": {"type": "string", "title": "答案"}, "answer_id": {"type": "string", "format": "uint64", "title": "答案ID"}, "references": {"type": "array", "title": "参考文献", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiFeedbackReference"}}}}, "aiReqSearchChat": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64", "title": "助手id"}, "clean_chunks": {"type": "boolean"}, "question_id": {"type": "string", "format": "uint64"}, "temperature": {"type": "number", "format": "float", "title": "温度"}, "text": {"type": "string"}, "text_recall_pattern": {"title": "关键词召回模式", "$ref": "#/definitions/aiTextRecallPattern"}, "text_recall_query": {"title": "关键词召回匹配目标", "$ref": "#/definitions/aiTextRecallQuery"}, "text_recall_slop": {"type": "integer", "format": "int32", "title": "关键词召回允许平移距离允许平移距离"}, "text_recall_top_n": {"type": "integer", "format": "int32", "title": "关键词召回条数"}, "threshold": {"type": "number", "format": "float", "title": "阈值"}, "top_n": {"type": "integer", "format": "int64", "title": "topN"}}}, "aiReqSearchChatUsers": {"type": "object", "properties": {"assistant_ids": {"type": "array", "title": "助手id", "items": {"type": "string", "format": "uint64"}}, "keyword": {"type": "string", "title": "搜索关键词"}, "limit": {"type": "integer", "format": "int64", "title": "分页大小"}, "offset": {"type": "integer", "format": "int64", "title": "分页偏移量"}, "region": {"title": "地区", "$ref": "#/definitions/baseRegion"}, "user_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "aiReqUpdateMyAssistant": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64", "title": "助手ID"}, "config": {"title": "配置详情", "$ref": "#/definitions/aiAssistantConfig"}, "mask": {"type": "string", "title": "更新字段列表，例如：\"name,prompt_prefix,search_engine\""}}}, "aiReqUpdateObjectCustomLabels": {"type": "object", "properties": {"id": {"type": "array", "title": "打标签的对象 id", "items": {"type": "string", "format": "uint64"}}, "labels": {"type": "array", "title": "自定义标签kv对", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "object_type": {"title": "打标签的对象类型", "$ref": "#/definitions/aiCustomLabelObjectType"}}}, "aiReqUpdateQAs": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiReqUpdateQA"}}}}, "aiReqUpdateTextFiles": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiReqUpdateTextFile"}}}}, "aiReqValidateQAs": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiReqValidateQAsItem"}}}}, "aiReqValidateQAsItem": {"type": "object", "properties": {"answer": {"type": "string"}, "assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "contributor": {"type": "array", "title": "贡献者", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "question": {"type": "string"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "state": {"$ref": "#/definitions/tanliveaiDocState"}}}, "aiReqValidateTextFiles": {"type": "object", "properties": {"data_source": {"$ref": "#/definitions/aiDocDataSource"}, "items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiReqValidateTextFilesItem"}}}}, "aiReqValidateTextFilesItem": {"type": "object", "properties": {"file_name": {"type": "string"}, "text": {"type": "string"}}}, "aiRspAcceptFeedback": {"type": "object", "properties": {"results": {"type": "array", "title": "结果", "items": {"type": "object", "$ref": "#/definitions/RspAcceptFeedbackResult"}}}}, "aiRspAutoChunkDoc": {"type": "object", "properties": {"chunks": {"type": "array", "title": "分段列表", "items": {"type": "object", "$ref": "#/definitions/aiChunkItem"}}}}, "aiRspBatchUpdateDocAttr": {"type": "object", "properties": {"async": {"type": "boolean"}}}, "aiRspBatchUserAssistantLimit": {"type": "object", "properties": {"user_limits": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspBatchUserAssistantLimitUserLimit"}}}}, "aiRspBindMiniProgramNormalAccount": {"type": "object", "properties": {"token": {"type": "string"}}}, "aiRspBindMiniProgramPhoneAccount": {"type": "object", "properties": {"is_have_phone": {"type": "boolean"}, "phone": {"type": "string"}, "token": {"type": "string"}, "user_name": {"type": "string"}}}, "aiRspBindOnceUserMiniProgram": {"type": "object", "properties": {"token": {"type": "string"}}}, "aiRspBindUserPhoneByCode": {"type": "object", "properties": {"bind_other_account": {"type": "boolean"}, "can_in": {"type": "boolean"}, "is_have_phone": {"type": "boolean"}, "user_name": {"type": "string"}}}, "aiRspCloneDoc": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "aiRspCreateAssistantShare": {"type": "object", "properties": {"async": {"type": "boolean"}}}, "aiRspCreateChat": {"type": "object", "properties": {"answer": {"$ref": "#/definitions/aiChatMessage"}, "answer_id": {"type": "string", "format": "uint64"}, "chat_id": {"type": "string", "format": "uint64", "title": "会话id"}, "message_id": {"type": "string", "format": "uint64", "title": "第一个问题的id"}}}, "aiRspCreateChatQuestion": {"type": "object", "properties": {"chat_id": {"type": "string", "format": "uint64"}, "is_file_ready": {"type": "boolean"}, "lang": {"type": "string"}, "question_id": {"type": "string", "format": "uint64"}}}, "aiRspCreateDocShareConfigReceiverAssistant": {"type": "object"}, "aiRspCreateDocShareConfigReceiverUserTeam": {"type": "object"}, "aiRspCreateDocShareConfigSender": {"type": "object"}, "aiRspCreateFeedback": {"type": "object", "properties": {"feedback_id": {"type": "string", "format": "uint64", "title": "反馈ID"}}}, "aiRspCreateGTBDocText": {"type": "object", "properties": {"uuid": {"type": "string"}}}, "aiRspCreateQAs": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "aiRspCreateTextFiles": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "aiRspDeleteDocs": {"type": "object", "properties": {"async": {"type": "boolean"}}}, "aiRspDescribeAccountIsGTB": {"type": "object", "properties": {"is_gtb": {"type": "boolean"}}}, "aiRspDescribeChatMessages": {"type": "object", "properties": {"chat_messages": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiEventChatMessage"}}, "total_count": {"type": "integer", "format": "int64"}}}, "aiRspDescribeChats": {"type": "object", "properties": {"chats": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiChat"}}, "total_count": {"type": "integer", "format": "int64"}}}, "aiRspDescribeDocList": {"type": "object", "properties": {"docs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiTencentDoc"}}, "next": {"type": "integer", "format": "int64"}}}, "aiRspDescribeMessageFileState": {"type": "object", "properties": {"files": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageFile"}}, "is_file_ready": {"type": "boolean"}}}, "aiRspDescribeTencentToken": {"type": "object", "properties": {"is_empty": {"type": "boolean"}}}, "aiRspExportTask": {"type": "object", "properties": {"task_id": {"type": "string", "format": "uint64"}}}, "aiRspFindFeedback": {"type": "object", "properties": {"create_by": {"title": "上传用户", "$ref": "#/definitions/iamUserInfo"}, "expected_docs": {"type": "array", "title": "预期命中的知识", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageDoc"}}, "expected_mgmt_docs": {"type": "array", "title": "预期命中的知识（碳LIVE运营）", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageDoc"}}, "feedback": {"title": "反馈详情", "$ref": "#/definitions/aiFeedback"}, "original_answer": {"title": "原始回答", "$ref": "#/definitions/aiChatMessage"}, "original_question": {"title": "原始问题", "$ref": "#/definitions/aiChatMessage"}, "references": {"type": "array", "title": "参考文献", "items": {"type": "object", "$ref": "#/definitions/tanliveaiFeedbackReference"}}, "team_cards": {"type": "array", "title": "团队卡片列表", "items": {"type": "object", "$ref": "#/definitions/teamTeamCard"}}, "user_cards": {"type": "array", "title": "用户卡片列表", "items": {"type": "object", "$ref": "#/definitions/iamUserCard"}}}}, "aiRspGetAccountOnceBindStatus": {"type": "object", "properties": {"has_once_bind": {"type": "boolean"}}}, "aiRspGetAccountUnionIdStatus": {"type": "object", "properties": {"bind_account_status": {"type": "boolean"}}}, "aiRspGetAssistantChunkConfig": {"type": "object", "properties": {"assistants": {"type": "array", "title": "助手列表", "items": {"type": "object", "$ref": "#/definitions/aiFullAssistant"}}, "collection_lang_count": {"type": "integer", "format": "int64", "title": "向量化模型数量"}, "total_count": {"type": "integer", "format": "int64", "title": "总数"}}}, "aiRspGetAssistantConfig": {"type": "object", "properties": {"assistants": {"type": "array", "title": "助手列表", "items": {"type": "object", "$ref": "#/definitions/aiFullAssistant"}}, "team_cards": {"type": "array", "title": "团队卡片列表", "items": {"type": "object", "$ref": "#/definitions/teamTeamCard"}}, "user_cards": {"type": "array", "title": "用户卡片列表", "items": {"type": "object", "$ref": "#/definitions/iamUserCard"}}}}, "aiRspGetAssistantsMiniprogram": {"type": "object", "properties": {"assistants": {"type": "array", "title": "助手列表", "items": {"type": "object", "$ref": "#/definitions/aiFullAssistant"}}, "team_cards": {"type": "array", "title": "团队卡片列表", "items": {"type": "object", "$ref": "#/definitions/teamTeamCard"}}, "total_count": {"type": "integer", "format": "int64"}, "user_cards": {"type": "array", "title": "用户卡片列表", "items": {"type": "object", "$ref": "#/definitions/iamUserCard"}}}}, "aiRspGetChatMessageDetail": {"type": "object", "properties": {"clean_chunks": {"type": "boolean"}, "collection_items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiSearchCollectionItem"}}, "collection_time": {"$ref": "#/definitions/baseTimeRange"}, "logs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageLog"}}, "message": {"$ref": "#/definitions/aiEventChatHashMessage"}}}, "aiRspGetFeedbacksItem": {"type": "object", "properties": {"expected_docs": {"type": "array", "title": "预期命中的知识", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageDoc"}}, "expected_mgmt_docs": {"type": "array", "title": "预期命中的知识（碳LIVE运营）", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageDoc"}}, "feedback": {"title": "反馈详情", "$ref": "#/definitions/aiFeedback"}, "original_answer": {"title": "原始回答", "$ref": "#/definitions/aiChatMessage"}, "original_question": {"title": "原始问题", "$ref": "#/definitions/aiChatMessage"}, "references": {"type": "array", "title": "参考文献", "items": {"type": "object", "$ref": "#/definitions/tanliveaiFeedbackReference"}}}}, "aiRspGetMiniProgramAssistantLimit": {"type": "object", "properties": {"can_in": {"type": "boolean", "title": "是否可以进入 true 可， false 不可"}, "empty_phone": {"type": "boolean", "title": "如果限制，需要检测手机号，true 空手机， false 不空；"}}}, "aiRspGetMiniProgramAuth": {"type": "object", "properties": {"has_assistant": {"type": "boolean"}, "has_bind_wx": {"type": "boolean", "title": "是否还在绑定微信中"}, "has_doc_auth": {"type": "boolean"}, "has_doc_auth_read": {"type": "boolean"}, "has_doc_auth_write": {"type": "boolean"}, "has_team": {"type": "boolean"}, "uin_token": {"type": "string"}}}, "aiRspGetMiniProgramLoginURL": {"type": "object", "properties": {"url": {"type": "string"}}}, "aiRspGetMiniProgramUserInfo": {"type": "object", "properties": {"token": {"type": "string"}}}, "aiRspGetMyAiServiceTermsConfirmation": {"type": "object", "properties": {"assistant_identity": {"type": "integer", "format": "int32", "title": "开通助手身份：0未开通；1个人开通；2团队开通"}, "is_agreed": {"type": "boolean", "title": "是否已同意"}, "is_verified": {"type": "boolean", "title": "团队是否已认证"}, "popup": {"type": "boolean", "title": "是否已弹窗"}, "terms_doc": {"type": "string", "title": "协议文档路径"}, "terms_type": {"title": "协议类型", "$ref": "#/definitions/cmsTermsType"}}}, "aiRspGetMyAssistants": {"type": "object", "properties": {"assistants": {"type": "array", "title": "助手列表", "items": {"type": "object", "$ref": "#/definitions/aiFullAssistant"}}, "total_count": {"type": "integer", "format": "int64", "title": "总数"}}}, "aiRspGetPublicChatShare": {"type": "object", "title": "获取公开分享详情响应", "properties": {"access_count": {"type": "integer", "format": "int32", "title": "访问次数"}, "assistant_id": {"type": "string", "format": "uint64", "title": "助手ID"}, "chat_id": {"type": "string", "format": "uint64", "title": "原会话ID"}, "expire_date": {"type": "string", "format": "date-time", "title": "分享过期时间"}, "last_access_time": {"type": "string", "format": "date-time", "title": "最后访问时间"}, "messages": {"type": "array", "title": "分享的消息列表", "items": {"type": "object", "$ref": "#/definitions/aiEventChatMessage"}}, "share_date": {"type": "string", "format": "date-time", "title": "分享创建时间"}, "share_id": {"type": "string", "title": "分享ID"}, "share_status": {"title": "分享状态", "$ref": "#/definitions/tanlivebff_webaiShareStatus"}, "share_type": {"title": "分享类型", "$ref": "#/definitions/tanlivebff_webaiShareType"}, "shared_by": {"type": "string", "format": "uint64", "title": "分享者ID"}}}, "aiRspGetTextFile": {"type": "object", "properties": {"item": {"$ref": "#/definitions/aiCollectionTextFile"}}}, "aiRspGetWebViewToMiniProgramToken": {"type": "object", "properties": {"token": {"type": "string"}, "webview_uni_token": {"type": "string"}}}, "aiRspImportQAs": {"type": "object"}, "aiRspImportTextFiles": {"type": "object"}, "aiRspListCollectionFileName": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspListCollectionFileNameItem"}}, "total_count": {"type": "integer", "format": "int64"}}}, "aiRspListCollectionFileNameItem": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "文件id"}, "name": {"type": "string", "title": "文件名称"}, "url": {"type": "string", "title": "文件绑定的url/path"}}}, "aiRspListDocShareConfigReceiverAssistantMembers": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}}}, "aiRspListDocShareConfigReceiverUserTeamMembers": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}}}, "aiRspListNebulaAssistants": {"type": "object", "properties": {"assistants": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiAssistantV2"}}, "total_count": {"type": "integer", "format": "int64"}}}, "aiRspListNebulaContributors": {"type": "object", "properties": {"contributors": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}}}, "aiRspListOperator": {"type": "object", "properties": {"operators": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocOperator"}}}}, "aiRspListTextFiles": {"type": "object", "properties": {"fail_parse_count": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCollectionTextFile"}}, "total_count": {"type": "integer", "format": "int64"}}}, "aiRspListeDocShareConfigSenderSharedAssistant": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "is_selected": {"type": "boolean"}, "name": {"type": "string"}, "name_en": {"type": "string"}}}, "aiRspManualChunkDoc": {"type": "object", "properties": {"task_id": {"type": "string", "format": "uint64", "title": "任务ID"}}}, "aiRspModifyCustomLabels": {"type": "object"}, "aiRspModifyDocTab": {"type": "object", "properties": {"account_id": {"type": "string", "format": "uint64"}, "admin_type": {"type": "string", "format": "uint64"}}}, "aiRspOnOffDocs": {"type": "object", "properties": {"async": {"type": "boolean"}, "pre_repeat_collections": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspOnOffDocsRepeatCollection"}}, "qa_num_exceed": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspOnOffDocsQaContainsMatchCount"}}, "repeat_collections": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspOnOffDocsRepeatCollection"}}}}, "aiRspOnOffDocsQaContainsMatchCount": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64"}, "cnt": {"type": "string", "format": "uint64"}}}, "aiRspOnOffDocsRepeatCollection": {"type": "object", "properties": {"file_name": {"type": "object", "additionalProperties": {"type": "string"}}, "id": {"type": "string", "format": "uint64"}}}, "aiRspProxyChatHtmlUrl": {"type": "object", "properties": {"contents": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspProxyChatHtmlUrlContent"}}}}, "aiRspReceiveChatMessage": {"type": "object", "properties": {"answer": {"$ref": "#/definitions/aiChatMessage"}, "answer_id": {"type": "string", "format": "uint64"}, "message_id": {"type": "string", "format": "uint64", "title": "问题的id"}}}, "aiRspSearchChat": {"type": "object", "properties": {"is_only_search": {"type": "boolean", "title": "是否仅搜索"}, "is_op": {"type": "boolean", "title": "是否是推送运营端"}, "message": {"$ref": "#/definitions/aiEventChatMessage"}, "user_id": {"type": "string", "format": "uint64"}}}, "aiRspSearchChatUsers": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64", "title": "总数"}, "users": {"type": "array", "title": "用户列表", "items": {"type": "object", "$ref": "#/definitions/iamUserInfo"}}}}, "aiRspSwitchChatLiveAgent": {"type": "object", "properties": {"state": {"title": "切换结果", "$ref": "#/definitions/aiSwitchChatState"}}}, "aiRspValidateQAs": {"type": "object", "properties": {"errors": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspValidateQAsErr"}}}}, "aiRspValidateQAsErr": {"type": "object", "properties": {"code": {"$ref": "#/definitions/errorsAiError"}, "id": {"type": "string", "format": "uint64"}, "message": {"type": "string"}}}, "aiRspValidateTextFiles": {"type": "object", "properties": {"errors": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspValidateTextFilesErr"}}}}, "aiRspValidateTextFilesErr": {"type": "object", "properties": {"code": {"$ref": "#/definitions/errorsAiError"}, "id": {"type": "string", "format": "uint64"}, "message": {"type": "string"}}}, "aiSearchCollectionType": {"type": "integer", "format": "int32", "title": "- 1: 向量搜索\n - 2: 文本搜索", "enum": [1, 2]}, "aiSearchEngineOption": {"type": "object", "title": "搜索引擎选项", "properties": {"name": {"type": "string", "title": "名称"}, "name_en": {"type": "string", "title": "英文名称"}, "value": {"type": "string", "title": "值"}}}, "aiSwitchChatState": {"type": "integer", "format": "int32", "title": "- 1: 切换成功\n - 2: 会话已结束\n - 3: 人工坐席离线\n - 4: 会话信息错误（需要重新开启一个会话或者再问一个问题）\n - 5: 人工坐席不存在", "enum": [1, 2, 3, 4, 5]}, "aiTaskOperationType": {"type": "integer", "format": "int32", "title": "- 1: 导出\n - 2: 导入", "enum": [1, 2]}, "aiTextFileTipTableOverSize": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64", "title": "助手 id"}, "assistant_name": {"type": "string", "title": "助手中文名称"}, "assistant_name_en": {"type": "string", "title": "助手英文名称"}, "header": {"type": "string", "title": "表头"}, "table_title": {"type": "string", "title": "表格标题"}}}, "aiTextRecallPattern": {"description": "- 1: 短语匹配\n - 2: 字匹配\n - 3: 英文模糊匹配", "type": "integer", "format": "int32", "title": "关键词召回模式", "enum": [1, 2, 3]}, "aiTextRecallQuery": {"description": "- 1: 在知识库的\"QA\"中召回\n - 2: 仅在知识库的\"Q\"中召回", "type": "integer", "format": "int32", "title": "QA关键词召回目标", "enum": [1, 2]}, "aiVisibleChainOption": {"type": "object", "title": "链路查询选项", "properties": {"field": {"type": "string", "title": "字段"}, "name": {"type": "string", "title": "名称"}, "name_en": {"type": "string", "title": "英文名称"}, "uncheck": {"type": "boolean", "title": "是否默认不选中"}}}, "baseDataType": {"description": "- 1: 团队\n - 2: 产品\n - 3: 资源\n - 4: 图谱\n - 5: 定向推送\n - 6: 用户个人\n - 7: 图谱AI\n - 8: 帮助中心文档\n - 9: AI助手", "type": "integer", "format": "int32", "title": "数据类型", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9]}, "baseIdentity": {"type": "object", "title": "身份", "properties": {"extra_id": {"type": "string", "format": "uint64", "title": "额外ID（团队类型表示用户ID）"}, "identity_id": {"type": "string", "format": "uint64", "title": "身份ID"}, "identity_type": {"title": "身份类型", "$ref": "#/definitions/baseIdentityType"}, "name": {"type": "string", "title": "名字"}}}, "baseIdentityType": {"description": "- 1: 门户端用户\n - 2: 团队\n - 3: 运营端用户\n - 4: 自定义", "type": "integer", "format": "int32", "title": "身份类型", "enum": [1, 2, 3, 4]}, "baseOrderBy": {"type": "object", "title": "排序", "properties": {"column": {"type": "string", "title": "列名"}, "desc": {"type": "boolean", "title": "是否倒序"}}}, "baseRegion": {"description": "- 1: 国内\n - 2: 海外", "type": "integer", "format": "int32", "title": "地域", "enum": [1, 2]}, "baseTcloudCaptcha": {"type": "object", "title": "腾讯云验证码参数", "properties": {"randstr": {"type": "string", "title": "前端回调函数返回的随机字符串"}, "ticket": {"type": "string", "title": "前端回调函数返回的用户验证票据"}}}, "baseTimeRange": {"type": "object", "title": "时间范围", "properties": {"end": {"type": "string", "format": "date-time", "title": "结束时间"}, "start": {"type": "string", "format": "date-time", "title": "开始时间"}}}, "baseUgcState": {"description": "- 1: 草稿。用户前台：草稿；运营后台：草稿\n - 2: 自动审核中。用户前台：处理中；运营后台：处理中\n - 3: 人工审核中（用户前台：处理中；运营后台：待处理）\n - 4: 人工审核中-可疑（用户前台：处理中；运营后台：可疑，待处理）\n - 5: 审核已通过（用户前台：下架存档；运营后台：下架存档）\n - 6: 已发布（用户前台：已发布；运营后台：已发布）\n - 7: 审核已驳回（用户前台：已驳回；运营后台：已驳回）\n - 8: 申诉中（用户前台：申诉中；运营后台：申诉待处理）", "type": "integer", "format": "int32", "title": "UGC状态", "enum": [1, 2, 3, 4, 5, 6, 7, 8]}, "cmsTermsType": {"description": "- 1: AI服务协议（国内）\n - 2: AI服务协议（海外）", "type": "integer", "format": "int32", "title": "协议类型", "enum": [1, 2]}, "errorsAiError": {"description": "- 16001: QA中的问题已经存在\n - 16002: 用户反馈已被采用\n - 16003: 用户反馈已标记已读\n - 16004: 用户反馈状态不允许被采用\n - 16005: 用户chat不存在\n - 16006: 非法的文档状态转换\n - 16007: 问题审核失败\n - 16008: 非法的AI租户\n - 16009: 非法的文档内容状态\n - 16010: doc中的文本/文件已经存在\n - 16011: 非法的自定义列转换\n - 16012: 助手不存在\n - 16013: 助手名称已存在\n - 16014: 助手英文名称已存在\n - 16015: 助手路由已存在\n - 16016: 助手已禁用\n - 16017: 助手当前功能已禁用\n - 16018: 助手应用ID已存在\n - 16019: 小程序code无效\n - 16020: 不是知识的贡献者\n - 16021: 助手客服用户名重复\n - 16022: 文档有运行中的分段任务\n - 16023: doc外部源Token已过期\n - 16024: 文件剪存文件夹不存在", "type": "integer", "format": "int32", "title": "AI服务错误\n范围：[16000, 17000)", "enum": [16001, 16002, 16003, 16004, 16005, 16006, 16007, 16008, 16009, 16010, 16011, 16012, 16013, 16014, 16015, 16016, 16017, 16018, 16019, 16020, 16021, 16022, 16023, 16024]}, "iamBindWeixinState": {"description": "- 1: 等待扫码\n - 2: 绑定成功\n - 3: 当前微信已绑定其他账号", "type": "integer", "format": "int32", "title": "绑定微信状态", "enum": [1, 2, 3]}, "iamCreateWeixinTfaState": {"description": "- 1: 等待扫码\n - 2: 创建成功", "type": "integer", "format": "int32", "title": "创建微信二次验证状态", "enum": [1, 2]}, "iamGoogleAuthScene": {"description": "- 1: 谷歌登录\n - 2: 绑定谷歌账号\n - 3: 创建谷歌二次验证", "type": "integer", "format": "int32", "title": "谷歌认证场景", "enum": [1, 2, 3]}, "iamLoginType": {"description": "- 1: 用户名登录\n - 2: 手机验证码登录\n - 3: 邮箱验证码登录\n - 4: 第三方登录", "type": "integer", "format": "int32", "title": "登录类型", "enum": [1, 2, 3, 4]}, "iamMenu": {"type": "object", "title": "菜单信息", "properties": {"auth_action": {"type": "integer", "format": "int64"}, "can_authorize": {"type": "boolean"}, "can_view": {"type": "boolean"}, "code": {"type": "string"}, "item_code": {"type": "string"}, "item_desc": {"type": "string"}, "item_name": {"type": "string"}, "level": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "only_holder": {"type": "boolean"}}}, "iamOtpReceiverKind": {"description": "- 1: 手机号码\n - 2: 邮件", "type": "integer", "format": "int32", "title": "验证码接收者类型", "enum": [1, 2]}, "iamOtpScene": {"description": "- 1: 注册\n - 2: 修改手机号\n - 4: 忘记密码\n - 6: 团队企业邮箱验证\n - 7: 登录场景\n - 8: 二次验证\n - 9: 修改邮箱", "type": "integer", "format": "int32", "title": "一次性密码使用场景（兼容老版的枚举值）", "enum": [1, 2, 4, 6, 7, 8, 9]}, "iamReqActiveMyEmail": {"type": "object", "properties": {"value": {"type": "string", "title": "激活邮件参数值"}}}, "iamReqBindMyGoogle": {"type": "object", "properties": {"code": {"type": "string", "title": "谷歌回调code"}, "tfa_token": {"type": "string", "title": "2FA令牌"}}}, "iamReqBindMyWeixinByOauth2": {"type": "object", "properties": {"code": {"type": "string", "title": "微信静默授权返回的code"}, "tfa_token": {"type": "string", "title": "2FA令牌"}}}, "iamReqCreateBindMyWeixinQrcode": {"type": "object", "properties": {"tfa_token": {"type": "string", "title": "2FA令牌"}}}, "iamReqCreateEmailTfa": {"type": "object", "properties": {"otp": {"type": "string", "title": "一次性密码"}}}, "iamReqCreateGoogleTfa": {"type": "object", "properties": {"code": {"type": "string", "title": "谷歌静默授权返回的code"}}}, "iamReqCreatePasswordTfa": {"type": "object", "properties": {"password": {"type": "string", "title": "密码（需要RSA加密）"}}}, "iamReqCreatePhoneTfa": {"type": "object", "properties": {"otp": {"type": "string", "title": "一次性密码"}}}, "iamReqCreateWeixinBrowserTfa": {"type": "object", "properties": {"code": {"type": "string", "title": "微信静默授权返回的code"}}}, "iamReqGetBindMyWeixinState": {"type": "object", "properties": {"scene_str": {"type": "string", "title": "场景值"}}}, "iamReqGetCreateWeixinTfaState": {"type": "object", "properties": {"scene_str": {"type": "string", "title": "场景值"}}}, "iamReqGetGoogleAuthUrl": {"type": "object", "properties": {"scene": {"title": "认证场景", "$ref": "#/definitions/iamGoogleAuthScene"}, "state": {"type": "string", "title": "state参数"}}}, "iamReqLogin": {"type": "object", "title": "登录", "properties": {"account_credentials": {"title": "密码登录凭证", "$ref": "#/definitions/ReqLoginAccountCredentials"}, "bind_third_ticket": {"type": "string", "title": "第三方凭证(用于绑定)"}, "email_credentials": {"title": "邮箱登录凭证", "$ref": "#/definitions/ReqLoginEmailCredentials"}, "login_type": {"title": "登录类型", "$ref": "#/definitions/iamLoginType"}, "phone_credentials": {"title": "手机号登录凭证", "$ref": "#/definitions/ReqLoginPhoneCredentials"}, "tcloud_captcha": {"title": "腾讯云验证码参数", "$ref": "#/definitions/baseTcloudCaptcha"}, "third_credentials": {"title": "第三方登录凭证", "$ref": "#/definitions/ReqLoginThirdCredentials"}}}, "iamReqModifyMyEmail": {"type": "object", "properties": {"email": {"type": "string", "title": "邮箱"}, "tcloud_captcha": {"title": "腾讯云验证码参数", "$ref": "#/definitions/baseTcloudCaptcha"}, "tfa_token": {"type": "string", "title": "2FA令牌"}}}, "iamReqModifyMyPassword": {"type": "object", "properties": {"password": {"type": "string", "title": "密码（需要RSA加密）"}, "tfa_token": {"type": "string", "title": "2FA令牌"}}}, "iamReqModifyMyPhone": {"type": "object", "properties": {"otp": {"type": "string", "title": "验证码"}, "phone": {"type": "string", "title": "手机号"}, "tfa_token": {"type": "string", "title": "2FA令牌"}}}, "iamReqModifyMyUsername": {"type": "object", "properties": {"tfa_token": {"type": "string", "title": "2FA令牌"}, "username": {"type": "string", "title": "用户名（需要RSA加密）"}}}, "iamReqSendAuthOtp": {"type": "object", "properties": {"receiver_email": {"type": "string", "title": "接收人邮箱"}, "receiver_kind": {"title": "接收人类型", "$ref": "#/definitions/iamOtpReceiverKind"}, "receiver_phone": {"type": "string", "title": "接收人手机号"}, "scene": {"title": "场景（仅1、2、4、6、7、9有效）", "$ref": "#/definitions/iamOtpScene"}, "tcloud_captcha": {"title": "腾讯云验证码参数", "$ref": "#/definitions/baseTcloudCaptcha"}}}, "iamReqSendTfaOtp": {"type": "object", "properties": {"receiver_kind": {"title": "接收者类型", "$ref": "#/definitions/iamOtpReceiverKind"}, "tcloud_captcha": {"title": "腾讯云验证码参数", "$ref": "#/definitions/baseTcloudCaptcha"}}}, "iamReqUnbindMyGoogle": {"type": "object", "properties": {"tfa_token": {"type": "string", "title": "2FA令牌"}}}, "iamReqUnbindMyWeixin": {"type": "object", "properties": {"tfa_token": {"type": "string", "title": "2FA令牌"}}}, "iamRspCreateBindMyWeixinQrcode": {"type": "object", "properties": {"qrcode": {"title": "二维码信息", "$ref": "#/definitions/iamWeixinQrcode"}}}, "iamRspCreateTfa": {"type": "object", "properties": {"tfa_token": {"title": "2FA令牌", "$ref": "#/definitions/iamTfaToken"}}}, "iamRspCreateWeixinTfaQrcode": {"type": "object", "properties": {"qrcode": {"title": "二维码信息", "$ref": "#/definitions/iamWeixinQrcode"}}}, "iamRspGetBindMyWeixinState": {"type": "object", "properties": {"state": {"title": "状态", "$ref": "#/definitions/iamBindWeixinState"}}}, "iamRspGetCreateWeixinTfaState": {"type": "object", "properties": {"state": {"title": "状态", "$ref": "#/definitions/iamCreateWeixinTfaState"}, "tfa_token": {"title": "2FA令牌", "$ref": "#/definitions/iamTfaToken"}}}, "iamRspGetGoogleAuthUrl": {"type": "object", "properties": {"url": {"type": "string", "title": "认证URL"}}}, "iamRspGetMyTfa": {"type": "object", "properties": {"tfa_token": {"title": "2FA令牌（未创建或已过期返回null）", "$ref": "#/definitions/iamTfaToken"}}}, "iamRspLogin": {"type": "object", "properties": {"menus": {"type": "array", "title": "该用户可显示的菜单列表", "items": {"type": "object", "$ref": "#/definitions/iamMenu"}}, "new_user": {"type": "boolean", "title": "该账号是否为未登录过状态"}, "user_info": {"title": "用户信息结构体", "$ref": "#/definitions/iamRspLoginUserInfo"}}}, "iamRspLoginUserInfo": {"type": "object", "properties": {"firm_name": {"type": "string"}, "identity_set": {"$ref": "#/definitions/baseIdentityType"}, "image": {"type": "string"}, "level": {"type": "string"}, "user_name": {"type": "string"}}}, "iamRspModifyMyEmail": {"type": "object", "properties": {"sdk_error": {"type": "string", "title": "腾讯云SDK错误码"}, "task_no": {"type": "string", "title": "任务ID"}}}, "iamRspSendAuthOtp": {"type": "object", "properties": {"sdk_error": {"type": "string", "title": "腾讯云SDK错误码"}, "task_no": {"type": "string", "title": "任务ID"}}}, "iamRspSendTfaOtp": {"type": "object", "properties": {"sdk_error": {"type": "string", "title": "腾讯云SDK错误码"}, "task_no": {"type": "string", "title": "任务ID"}}}, "iamTfaToken": {"type": "object", "title": "2FA(2-Factor Authentication) Token", "properties": {"token": {"type": "string", "title": "认证TOKEN"}, "ttl": {"type": "string", "title": "有效期"}}}, "iamUserCard": {"type": "object", "title": "用户卡片", "properties": {"id": {"type": "string", "format": "uint64", "title": "用户ID"}, "image": {"type": "string", "title": "头像"}, "level": {"type": "string", "title": "等级"}, "username": {"type": "string", "title": "用户名"}}}, "iamUserInfo": {"type": "object", "title": "用户信息", "properties": {"city": {"type": "string", "title": "市"}, "country": {"type": "string", "title": "国家"}, "firm_id": {"type": "string", "format": "uint64", "title": "团队ID"}, "id": {"type": "string", "format": "uint64", "title": "用户ID"}, "identity_set": {"title": "身份", "$ref": "#/definitions/baseIdentityType"}, "image": {"type": "string", "title": "头像"}, "level": {"type": "string", "title": "等级"}, "nick_name": {"type": "string", "title": "用户昵称"}, "phone": {"type": "string", "title": "手机号"}, "phone_hash": {"type": "string", "title": "手机号哈希"}, "province": {"type": "string", "title": "省"}, "region_code": {"type": "string", "title": "地区编码"}, "timezone": {"type": "string", "title": "时区"}, "union_id": {"type": "string", "title": "微信unionID"}, "username": {"type": "string", "title": "用户名"}}}, "iamWeixinQrcode": {"type": "object", "title": "微信二维码信息", "properties": {"expires_in": {"type": "integer", "format": "int32", "title": "过期时间（单位秒）"}, "scene_str": {"type": "string", "title": "场景值"}, "ticket": {"type": "string", "title": "获取的二维码ticket"}, "url": {"type": "string", "title": "二维码解析后的URL"}}}, "notifyReqGetEmailSendStatus": {"type": "object", "properties": {"task_no": {"type": "string", "title": "任务ID"}}}, "notifyReqGetSmsSendStatus": {"type": "object", "properties": {"task_no": {"type": "string", "title": "任务ID"}}}, "notifyRspGetEmailSendStatus": {"type": "object", "properties": {"event": {"type": "string", "title": "事件类型：\ndelivering 投递中\ndeferred 邮件被收件人邮件服务商延迟传递，正在重试中\ndelivered 递送成功，如您未收到请检查垃圾箱\ndropped 邮件无法送达，请尝试其他邮箱地址\nbounce 收件人邮件服务商拒收此邮件，请检查邮箱地址或尝试其他邮箱地址"}, "timestamp": {"type": "integer", "format": "int32", "title": "事件产生的时间戳"}}}, "notifyRspGetSmsSendStatus": {"type": "object", "properties": {"errmsg": {"type": "string", "title": "用户接收短信状态码错误信息，参考：https://cloud.tencent.com/document/api/382/59177#.E5.9B.9E.E6.89.A7.E7.8A.B6.E6.80.81.E9.94.99.E8.AF.AF.E7.A0.81"}, "report_status": {"type": "string", "title": "状态：SENDING（发送中）、SUCCESS（成功）、FAIL（失败）"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}, "message": {"type": "string"}}}, "searchReqDescribeAtlasSearchOptions": {"type": "object", "properties": {"target_type": {"title": "搜索对应的模块类型 1:团队,2:产品", "$ref": "#/definitions/searchSearchOptionTargetType"}}}, "searchReqDescribeSearchPrompts": {"type": "object", "properties": {"refer_type": {"title": "搜索对应的筛选项类型", "$ref": "#/definitions/searchSearchOptionReferType"}, "target_type": {"title": "搜索对应的模块类型", "$ref": "#/definitions/searchSearchOptionTargetType"}}}, "searchReqGetPlaceSelector": {"type": "object", "properties": {"by_data_type": {"title": "通过数据类型查询", "$ref": "#/definitions/supportGetPlaceCondByDataType"}, "by_place_id": {"title": "通过地点ID查询", "$ref": "#/definitions/supportGetPlaceCondByPlaceId"}}}, "searchReqProxy": {"type": "object", "properties": {"urls": {"type": "array", "items": {"type": "string"}}}}, "searchReqSearchPlaces": {"type": "object", "properties": {"data_field": {"type": "string", "title": "数据字段\n团队：location、service_region 资源：location、target_regions"}, "data_type": {"title": "数据类型", "$ref": "#/definitions/baseDataType"}, "name": {"type": "string", "title": "按名称模糊搜索"}}}, "searchReqSendMapRequest": {"type": "object", "properties": {"platform": {"title": "地图平台", "$ref": "#/definitions/supportMapPlatform"}, "platform1_client": {"title": "谷歌地图客户端", "$ref": "#/definitions/supportGoogleMapClient"}, "request": {"title": "请求", "$ref": "#/definitions/supportMapRequest"}}}, "searchRspDescribeAtlasSearchOptions": {"type": "object", "properties": {"options": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_websearchSearchOption"}}}}, "searchRspDescribeSearchPrompts": {"type": "object", "properties": {"prompts": {"$ref": "#/definitions/tanlivebff_websearchSearchPrompt"}}}, "searchRspGetPlaceSelector": {"type": "object", "properties": {"places": {"type": "array", "title": "地点列表", "items": {"type": "object", "$ref": "#/definitions/supportPlace"}}}}, "searchRspProxy": {"type": "object", "properties": {"contents": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspProxyContent"}}}}, "searchRspSearchPlaces": {"type": "object", "properties": {"places": {"type": "array", "title": "地点列表", "items": {"type": "object", "$ref": "#/definitions/supportPlaceWithParents"}}}}, "searchRspSendMapRequest": {"type": "object", "properties": {"response": {"title": "响应", "$ref": "#/definitions/supportMapResponse"}}}, "searchSearchOptionReferType": {"description": "- 1: 图谱\n - 2: 行业认可\n - 3: 资源系列", "type": "integer", "format": "int32", "title": "搜索对应的筛选项", "enum": [1, 2, 3]}, "searchSearchOptionTargetType": {"description": "- 1: 团队\n - 2: 产品\n - 3: 资源", "type": "integer", "format": "int32", "title": "搜索对应的模块", "enum": [1, 2, 3]}, "supportAdAreaType": {"description": "- 1: 洲\n - 2: 国家/地区\n - 3: 一级行政区划\n - 4: 二级行政区划", "type": "integer", "format": "int32", "title": "行政区划类型", "enum": [1, 2, 3, 4]}, "supportGetPlaceCondByDataType": {"type": "object", "title": "通过数据类型查询条件", "properties": {"data_field": {"type": "string", "title": "数据字段\n团队：location、service_region 资源：location、target_regions"}, "data_type": {"title": "数据类型", "$ref": "#/definitions/baseDataType"}, "parent_id": {"type": "string", "format": "uint64", "title": "父级ID，不传返回大洲数据"}}}, "supportGetPlaceCondByPlaceId": {"type": "object", "title": "通过地点ID查询条件", "properties": {"place_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "supportGoogleMapClient": {"description": "- 1: Maps客户端\n - 2: Places客户端", "type": "integer", "format": "int32", "title": "谷歌地图客户端", "enum": [1, 2]}, "supportMapHeader": {"type": "object", "properties": {"key": {"type": "string", "title": "键名"}, "value": {"type": "string", "title": "键值"}}}, "supportMapPlatform": {"description": "- 1: 谷歌地图\n - 2: 腾讯地图", "type": "integer", "format": "int32", "title": "地图平台", "enum": [1, 2]}, "supportMapRequest": {"type": "object", "title": "地图请求", "properties": {"body": {"type": "string", "title": "请求体"}, "header": {"type": "array", "title": "请求头", "items": {"type": "object", "$ref": "#/definitions/supportMapHeader"}}, "method": {"type": "string", "title": "请求方法"}, "path": {"type": "string", "title": "请求路径"}}}, "supportMapResponse": {"type": "object", "title": "地图响应", "properties": {"body": {"type": "string", "title": "响应体"}, "header": {"type": "array", "title": "响应头", "items": {"type": "object", "$ref": "#/definitions/supportMapHeader"}}}}, "supportPlace": {"type": "object", "title": "地点详情", "properties": {"ad_area_type": {"title": "行政区划等级", "$ref": "#/definitions/supportAdAreaType"}, "address": {"type": "string", "title": "地址"}, "id": {"type": "string", "format": "uint64", "title": "主键"}, "is_ad_area": {"type": "boolean", "title": "是否为行政区划地点"}, "iso_code2": {"type": "string", "title": "2位ISO编码"}, "iso_code3": {"type": "string", "title": "3位ISO编码"}, "iso_number": {"type": "string", "title": "ISO编号"}, "lang": {"type": "string", "title": "语言"}, "lat": {"type": "number", "format": "double", "title": "纬度"}, "lng": {"type": "number", "format": "double", "title": "经度"}, "name": {"type": "string", "title": "名称"}, "parent_id": {"type": "string", "format": "uint64", "title": "父级ID"}}}, "supportPlaceWithParents": {"type": "object", "title": "地点详情", "properties": {"parents": {"type": "array", "title": "父级列表", "items": {"type": "object", "$ref": "#/definitions/supportPlace"}}, "place": {"title": "地点详情", "$ref": "#/definitions/supportPlace"}}}, "tagReqGetSystemTags": {"type": "object", "properties": {"is_direct_display_id": {"type": "boolean", "title": "是否直接显示标签id，不会被编码为hashId"}, "language": {"type": "string", "title": "语言类型zh、en"}, "notify_type": {"title": "定向推送用的筛选条件 1 系统标签", "$ref": "#/definitions/tagTagCreateType"}, "order_by": {"type": "array", "title": "排序", "items": {"type": "string"}}, "tag_name": {"type": "string", "title": "标签名称模糊搜索"}, "taggable_type": {"title": "标签索引类型", "$ref": "#/definitions/tagTaggableType"}}}, "tagRspGetSystemTags": {"type": "object", "properties": {"tag_set": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tagRspGetSystemTagsTag"}}}}, "tagRspGetSystemTagsTag": {"type": "object", "properties": {"direct_display_id": {"type": "string", "format": "uint64", "title": "直接显示的标签id"}, "id": {"type": "string", "format": "uint64", "title": "标签id"}, "name": {"type": "string", "title": "标签名称"}}}, "tagTagCreateType": {"description": "- 1: 系统标签\n - 2: 自创标签", "type": "integer", "format": "int32", "title": "TagCreateType 标签创建类型枚举", "enum": [1, 2]}, "tagTaggableType": {"description": "- 1: 1 个人信息-我来自\n - 2: 2 团队类型\n - 3: 3 团队属性\n - 4: 4 团队行业\n - 5: 5 资源受众行业\n - 6: 6 产品技术-面向用户（场景）\n - 7: 7 资源类型\n - 8: 8 图谱-适用行业\n - 9: 9 图谱-适用场景\n - 10: 10 图谱类型\n - 11: 11 产品行业认可 --2.2迭代将 21资源-产品行业认可合并入11\n - 12: 12 团队行业认可 --2.2迭代将 22资源-团队行业认可合并入12\n - 14: 14 产品适用行业\n - 23: 23 资源系列\n - 101: 101 团队发展阶段\n - 102: 102 团队产品类型\n - 103: 103 受众团队融资阶段\n - 200: 200 后端用于业务及联的特殊类型，前端忽略\n - 201: 201 个人用户-用户标签\n - 202: 202 团队用户-用户标签\n - 203: 203 AI助手-给用户打标", "type": "integer", "format": "int32", "title": "标签类型", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 23, 101, 102, 103, 200, 201, 202, 203]}, "tanliveaiAssistant": {"type": "object", "properties": {"clean_chunks": {"type": "boolean"}, "collections": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCollection"}}, "id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "name_en": {"type": "string"}, "search_debug": {"type": "boolean"}, "temperature": {"type": "number", "format": "float", "title": "温度"}, "text_recall_pattern": {"title": "关键词召回模式", "$ref": "#/definitions/aiTextRecallPattern"}, "text_recall_query": {"title": "关键词召回匹配目标", "$ref": "#/definitions/aiTextRecallQuery"}, "text_recall_slop": {"type": "integer", "format": "int32", "title": "关键词召回允许平移距离允许平移距离"}, "text_recall_top_n": {"type": "integer", "format": "int32", "title": "关键词召回条数"}, "text_weight": {"type": "number", "format": "float"}, "threshold": {"type": "number", "format": "float"}, "top_n": {"type": "integer", "format": "int32"}, "website_route": {"type": "string"}}}, "tanliveaiDocState": {"description": "- 1: 启用\n - 2: 禁用\n - 3: 解析中\n - 4: 解析失败\n - 5: 文件上传中\n - 6: 文件上传成功\n - 7: 删除中\n - 8: 解除了助手绑定（在助手中已删除）\n - 9: 重新解析中", "type": "integer", "format": "int32", "title": "知识库文档状态", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9]}, "tanliveaiExternalSourceUser": {"type": "object", "properties": {"auth_source": {"type": "string", "title": "用户授权来源(xw qq)"}, "auth_state": {"title": "用户状态", "$ref": "#/definitions/aiExternalSourceUserAuthState"}, "avatar": {"type": "string", "title": "用户头像"}, "nickname": {"type": "string", "title": "用户昵称"}, "user_id": {"type": "string", "title": "用户ID"}}}, "tanliveaiFeedbackReference": {"type": "object", "title": "反馈参考文献", "properties": {"create_by": {"type": "string", "format": "uint64", "title": "创建人"}, "create_date": {"type": "string", "format": "date-time", "title": "创建时间"}, "feedback_id": {"type": "string", "format": "uint64", "title": "反馈ID"}, "file_name": {"type": "string", "title": "文件名称"}, "file_path": {"type": "string", "title": "文件路径"}, "id": {"type": "string", "format": "uint64", "title": "参考文献ID"}, "text": {"type": "string", "title": "文本内容"}, "type": {"title": "类型", "$ref": "#/definitions/aiReferenceType"}, "update_by": {"type": "string", "format": "uint64", "title": "更新人"}, "update_date": {"type": "string", "format": "date-time", "title": "更新时间"}, "url": {"type": "string", "title": "URL链接"}}}, "tanliveaiSearchCollectionItem": {"type": "object", "properties": {"contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}, "doc_id": {"type": "string", "format": "uint64"}, "doc_name": {"type": "string"}, "doc_type": {"title": "文件类型", "$ref": "#/definitions/aiDocType"}, "file_name": {"type": "string"}, "id": {"type": "string"}, "is_related": {"type": "boolean", "title": "是否相关"}, "question": {"type": "string"}, "ref_name": {"type": "string"}, "ref_url": {"type": "string"}, "score": {"type": "number", "format": "float"}, "text": {"type": "string"}, "type": {"title": "召回类型", "$ref": "#/definitions/aiSearchCollectionType"}, "update_by": {"$ref": "#/definitions/aiOperator"}, "url": {"type": "string"}}}, "tanlivebff_webaiAssistant": {"type": "object", "properties": {"clean_chunks": {"type": "boolean"}, "id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "name_en": {"type": "string"}, "search_debug": {"type": "boolean"}, "text_recall_top_n": {"type": "integer", "format": "int32", "title": "关键词召回条数"}, "text_weight": {"type": "number", "format": "float"}, "threshold": {"type": "number", "format": "float"}, "top_n": {"type": "integer", "format": "int32"}, "website_route": {"type": "string"}}}, "tanlivebff_webaiChat": {"type": "object", "properties": {"create_by": {"type": "string", "format": "uint64"}, "create_date": {"type": "string", "format": "date-time"}, "id": {"type": "string", "format": "uint64"}, "is_shared": {"type": "boolean"}, "title": {"type": "string"}}}, "tanlivebff_webaiChatDetail": {"type": "object", "properties": {"assistant_avatar": {"type": "string", "title": "微信客服助手头像"}, "assistant_id": {"type": "string", "format": "uint64"}, "chat_state": {"$ref": "#/definitions/aiChatCurrentState"}, "chat_type": {"$ref": "#/definitions/aiChatType"}, "create_by": {"$ref": "#/definitions/iamUserInfo"}, "create_date": {"type": "string", "format": "date-time"}, "finish_date": {"type": "string", "format": "date-time"}, "id": {"type": "string", "format": "uint64"}, "messages": {"type": "array", "title": "地区\n  tanlive.base.Region region = 4;", "items": {"type": "object", "$ref": "#/definitions/aiEventChatMessage"}}, "records": {"type": "array", "title": "非web端时通过次字段返回消息详情", "items": {"type": "object", "$ref": "#/definitions/aiChatSendRecordInfo"}}, "support_type": {"title": "当前服务状态", "$ref": "#/definitions/aiChatSupportType"}, "title": {"type": "string"}}}, "tanlivebff_webaiExternalSourceUser": {"type": "object", "properties": {"auth_source": {"type": "string", "title": "用户授权来源(xw qq)"}, "auth_state": {"title": "用户状态", "$ref": "#/definitions/aiExternalSourceUserAuthState"}, "avatar": {"type": "string", "title": "用户头像"}, "hash_user_id": {"type": "string", "title": "用户ID"}, "nickname": {"type": "string", "title": "用户昵称"}}}, "tanlivebff_webaiFeedbackReference": {"type": "object", "title": "反馈参考文献", "properties": {"file_name": {"type": "string", "title": "文件名称（type为3时必填）"}, "file_path": {"type": "string", "title": "文件路径（type为3时必填）"}, "text": {"type": "string", "title": "文本（type为2时必填）"}, "type": {"title": "文献类型", "$ref": "#/definitions/aiReferenceType"}, "url": {"type": "string", "title": "跳转链接（type为1时必填）"}}}, "tanlivebff_webaiReqAcceptFeedback": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64"}, "feedback_ids": {"type": "array", "title": "反馈ID", "items": {"type": "string", "format": "uint64"}}}}, "tanlivebff_webaiReqAuthTencentCode": {"type": "object", "properties": {"code": {"type": "string"}, "path": {"type": "string"}}}, "tanlivebff_webaiReqContinueChatFromShare": {"type": "object", "title": "从分享继续聊天请求", "properties": {"assistant_id": {"type": "string", "format": "uint64", "title": "助手ID"}, "chat_type": {"$ref": "#/definitions/aiChatType"}, "share_id": {"type": "string", "title": "分享ID"}}}, "tanlivebff_webaiReqConvertCustomLabel": {"type": "object", "properties": {"dry_run": {"type": "boolean"}, "id": {"type": "string", "format": "uint64"}, "target": {"$ref": "#/definitions/aiCustomLabel"}}}, "tanlivebff_webaiReqCreateChatShare": {"type": "object", "title": "创建聊天分享请求", "properties": {"chat_id": {"type": "string", "format": "uint64", "title": "会话ID"}, "expire_days": {"type": "integer", "format": "int32", "title": "过期天数，0表示永久有效"}, "message_ids": {"type": "array", "title": "要分享的消息ID列表", "items": {"type": "string", "format": "uint64"}}, "share_type": {"title": "分享类型", "$ref": "#/definitions/tanlivebff_webaiShareType"}}}, "tanlivebff_webaiReqCreateDocQuery": {"type": "object", "properties": {"doc": {"$ref": "#/definitions/aiReqListTextFiles"}, "qa": {"$ref": "#/definitions/tanlivebff_webaiReqListQA"}}}, "tanlivebff_webaiReqCreateDocShareConfigReceiverAssistant": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64"}, "other_state": {"$ref": "#/definitions/aiDocShareState"}, "receiver_state": {"$ref": "#/definitions/aiDocShareAcceptState"}, "user_shares": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiReqCreateDocShareConfigReceiverAssistantUserShare"}}}}, "tanlivebff_webaiReqCreateDocShareConfigReceiverUserTeam": {"type": "object", "properties": {"team_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "user_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "tanlivebff_webaiReqCreateDocShareConfigSender": {"type": "object", "properties": {"share_assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "share_team_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "share_user_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "tanlivebff_webaiReqCreateNebulaTask": {"type": "object", "properties": {"assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "clustering_method": {"type": "string"}, "eps_range": {"type": "array", "items": {"type": "number", "format": "float"}}, "filter_text": {"type": "string"}, "has_k": {"type": "boolean"}, "has_q": {"type": "boolean"}, "lang": {"type": "string"}, "min_samples_range": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "n_clusters_range": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "query_assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "tanlivebff_webaiReqCreateQA": {"type": "object", "properties": {"answer": {"type": "string"}, "assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "contributor": {"type": "array", "title": "贡献者", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "match_patterns": {"type": "array", "title": "匹配模式", "items": {"$ref": "#/definitions/aiDocMatchPattern"}}, "question": {"type": "string"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "share_assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}, "state": {"$ref": "#/definitions/tanliveaiDocState"}}}, "tanlivebff_webaiReqCreateTencentDocAuthUrl": {"type": "object"}, "tanlivebff_webaiReqDelTencentDocAuth": {"type": "object", "properties": {"hash_user_id": {"type": "string"}}}, "tanlivebff_webaiReqDeleteCustomLabels": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "tanlivebff_webaiReqDescribeChatRegionCode": {"type": "object", "properties": {"assistant_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "region": {"title": "地区", "$ref": "#/definitions/baseRegion"}}}, "tanlivebff_webaiReqDescribeDocTab": {"type": "object"}, "tanlivebff_webaiReqDescribeExportTasks": {"type": "object", "properties": {"operation_type": {"$ref": "#/definitions/aiTaskOperationType"}, "type": {"type": "array", "items": {"$ref": "#/definitions/aiExportTaskType"}}}}, "tanlivebff_webaiReqDescribeFeedbackRegionCode": {"type": "object", "properties": {"region": {"title": "地区", "$ref": "#/definitions/baseRegion"}}}, "tanlivebff_webaiReqDescribeMyDoc": {"type": "object"}, "tanlivebff_webaiReqDescribeNebulaData": {"type": "object", "properties": {"content_hash": {"type": "array", "items": {"type": "string"}}, "id": {"type": "string", "format": "uint64"}}}, "tanlivebff_webaiReqDescribeNebulaProjection": {"type": "object", "properties": {"algorithm": {"type": "string"}, "query": {"type": "string"}, "uuid": {"type": "string"}}}, "tanlivebff_webaiReqDescribeNebulaTask": {"type": "object", "properties": {"assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "lang": {"type": "string"}, "query_assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "uuid": {"type": "string"}}}, "tanlivebff_webaiReqDescribeNebulaTaskList": {"type": "object", "properties": {"limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "to_model": {"type": "string"}, "uuid": {"type": "array", "items": {"type": "string"}}}}, "tanlivebff_webaiReqDescribeTencentDocTask": {"type": "object"}, "tanlivebff_webaiReqGetChatDetail": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "chat_id"}, "keyword": {"type": "string", "title": "消息内容搜索关键词"}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "question_id": {"type": "string", "format": "uint64", "title": "问题ID"}, "send_range": {"title": "创建时间区间", "$ref": "#/definitions/baseTimeRange"}}}, "tanlivebff_webaiReqGetChunkDocTasks": {"type": "object", "properties": {"doc_id": {"type": "array", "title": "文档ID", "items": {"type": "string", "format": "uint64"}}}}, "tanlivebff_webaiReqGetCustomLabelValueTopN": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}}}, "tanlivebff_webaiReqGetDocChunks": {"type": "object", "properties": {"assistant_id": {"type": "array", "title": "助手ID", "items": {"type": "string", "format": "uint64"}}, "doc_id": {"type": "string", "format": "uint64", "title": "文档ID"}}}, "tanlivebff_webaiReqGetDocEmbeddingModels": {"type": "object", "properties": {"doc_id": {"type": "string", "format": "uint64", "title": "文档ID"}}}, "tanlivebff_webaiReqGetFeedbackLogs": {"type": "object", "properties": {"action": {"type": "array", "title": "操作类型", "items": {"$ref": "#/definitions/aiFeedbackAction"}}, "create_date_range": {"title": "操作时间区间", "$ref": "#/definitions/baseTimeRange"}, "create_identity": {"title": "操作人", "$ref": "#/definitions/baseIdentity"}, "feedback_id": {"type": "string", "format": "uint64", "title": "反馈ID"}, "limit": {"type": "integer", "format": "int64", "title": "分页大小"}, "offset": {"type": "integer", "format": "int64", "title": "分页偏移量"}, "order_by": {"type": "array", "title": "排序", "items": {"type": "object", "$ref": "#/definitions/baseOrderBy"}}, "region": {"title": "地区", "$ref": "#/definitions/baseRegion"}}}, "tanlivebff_webaiReqGetFeedbacks": {"type": "object", "properties": {"assistant_ids": {"type": "array", "title": "助手ID", "items": {"type": "string", "format": "uint64"}}, "create_by": {"type": "array", "title": "上传用户ID", "items": {"type": "string", "format": "uint64"}}, "create_date_range": {"title": "创建时间区间", "$ref": "#/definitions/baseTimeRange"}, "handled_at_range": {"title": "处理时间区间", "$ref": "#/definitions/baseTimeRange"}, "limit": {"type": "integer", "format": "int64", "title": "分页大小"}, "offset": {"type": "integer", "format": "int64", "title": "分页偏移量"}, "order_by": {"type": "array", "title": "排序", "items": {"type": "object", "$ref": "#/definitions/baseOrderBy"}}, "region": {"title": "地区", "$ref": "#/definitions/baseRegion"}, "region_codes": {"type": "array", "items": {"type": "string"}}, "state": {"type": "array", "title": "状态筛选", "items": {"$ref": "#/definitions/aiFeedbackState"}}}}, "tanlivebff_webaiReqGetQaTip": {"type": "object", "title": "获取QA知识提示请求", "properties": {"id": {"type": "string", "format": "uint64", "title": "文档ID"}}}, "tanlivebff_webaiReqGetTextFileTip": {"type": "object", "title": "获取文件文本知识提示请求", "properties": {"id": {"type": "string", "format": "uint64", "title": "文档ID"}}}, "tanlivebff_webaiReqImportTencentDoc": {"type": "object", "properties": {"file_ids": {"type": "array", "items": {"type": "string"}}, "hash_user_id": {"type": "string"}, "path": {"type": "string"}}}, "tanlivebff_webaiReqImportTencentDocWebClip": {"type": "object", "properties": {"after_time": {"type": "string", "format": "date-time"}, "hash_user_id": {"type": "string"}}}, "tanlivebff_webaiReqListAssistant": {"type": "object", "properties": {"language": {"type": "string", "title": "语言，为空会从header里取"}, "limit": {"type": "integer", "format": "int64"}, "name": {"type": "string", "title": "搜索名称关键词"}, "offset": {"type": "integer", "format": "int64"}, "type": {"$ref": "#/definitions/aiChatType"}}}, "tanlivebff_webaiReqListAssistantCanShareDoc": {"type": "object", "properties": {"doc_id": {"type": "string", "format": "uint64"}, "language": {"type": "string", "title": "语言，为空会从header里取"}, "name": {"type": "string", "title": "搜索名称关键词"}}}, "tanlivebff_webaiReqListChat": {"type": "object", "properties": {"assistant_ids": {"type": "array", "title": "助手id", "items": {"type": "string", "format": "uint64"}}, "create_date_range": {"title": "创建时间区间", "$ref": "#/definitions/baseTimeRange"}, "filter": {"$ref": "#/definitions/tanlivebff_webaiReqListChatFilter"}, "ids": {"type": "array", "title": "筛选ids", "items": {"type": "string", "format": "uint64"}}, "labels": {"type": "array", "title": "自定义标签kv对", "items": {"type": "object", "$ref": "#/definitions/aiLabelFilter"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "order_by": {"type": "array", "items": {"type": "string"}}, "order_by_label": {"title": "自定义标签排序，只能当个标签排序", "$ref": "#/definitions/aiOrderByLabel"}, "update_date_range": {"title": "处理时间区间", "$ref": "#/definitions/baseTimeRange"}}}, "tanlivebff_webaiReqListChatFilter": {"type": "object", "properties": {"chat_titles": {"type": "array", "title": "对话内容", "items": {"type": "string"}}, "chat_type": {"$ref": "#/definitions/aiChatType"}, "is_manual": {"type": "integer", "format": "int32", "title": "是否转过人工服务 1 否 2 是"}, "nicknames": {"type": "array", "items": {"type": "string"}}, "rating_scale": {"type": "array", "items": {"$ref": "#/definitions/aiRatingScale"}}, "region": {"title": "地区", "$ref": "#/definitions/baseRegion"}, "region_codes": {"type": "array", "title": "国家或地区编码", "items": {"type": "string"}}, "reject_job_result": {"type": "integer", "format": "int64", "title": "筛选审核 1 违规 2 敏感 3 正常"}, "user_ids": {"type": "array", "title": "用户id", "items": {"type": "string", "format": "uint64"}}}}, "tanlivebff_webaiReqListChatLiveAgent": {"type": "object", "properties": {"chat_id": {"type": "string", "format": "uint64"}}}, "tanlivebff_webaiReqListContributor": {"type": "object", "properties": {"data_source": {"$ref": "#/definitions/aiDocDataSource"}, "search": {"type": "string"}, "type": {"$ref": "#/definitions/aiListDocFilterType"}}}, "tanlivebff_webaiReqListCustomLabel": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "limit": {"type": "integer", "format": "int64"}, "object_type": {"$ref": "#/definitions/aiCustomLabelObjectType"}, "offset": {"type": "integer", "format": "int64"}}}, "tanlivebff_webaiReqListDocShareConfigReceiverAssistant": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64"}}}, "tanlivebff_webaiReqListDocShareConfigReceiverUserTeam": {"type": "object"}, "tanlivebff_webaiReqListExternalSourceUser": {"type": "object"}, "tanlivebff_webaiReqListMyAssistantIds": {"type": "object"}, "tanlivebff_webaiReqListQA": {"type": "object", "properties": {"assistant_id": {"type": "array", "title": "助手id", "items": {"type": "string", "format": "uint64"}}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "create_by": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiOperator"}}, "embedding_state": {"title": "向量状态", "$ref": "#/definitions/aiDocEmbeddingState"}, "excluded_assistant_id": {"type": "array", "title": "不在助手中", "items": {"type": "string", "format": "uint64"}}, "group_repeated": {"type": "boolean", "title": "重复 doc 紧凑排序"}, "ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiLabelFilter"}}, "limit": {"type": "integer", "format": "int64"}, "match_patterns": {"type": "array", "title": "匹配模式", "items": {"$ref": "#/definitions/aiDocMatchPattern"}}, "offset": {"type": "integer", "format": "int64"}, "order_by": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/baseOrderBy"}}, "order_by_label": {"title": "自定义标签排序，只能当个标签排序", "$ref": "#/definitions/aiOrderByLabel"}, "search": {"type": "string"}, "share_assistant_id": {"type": "array", "title": "分享的助手", "items": {"type": "string", "format": "uint64"}}, "share_team_id": {"type": "array", "title": "分享的团队id", "items": {"type": "string", "format": "uint64"}}, "share_user_id": {"type": "array", "title": "分享的用户id", "items": {"type": "string", "format": "uint64"}}, "shared_state": {"type": "array", "items": {"$ref": "#/definitions/aiDocSharedState"}}, "show_contributor": {"type": "integer", "format": "int64"}, "state": {"$ref": "#/definitions/tanliveaiDocState"}, "tip_filter": {"$ref": "#/definitions/tanlivebff_webaiReqListQATipFilter"}, "tql_expression": {"type": "string", "title": "TQL表达式（高级搜索）"}, "update_by": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiOperator"}}, "with_tips": {"type": "boolean", "title": "是否返回知识提示"}}}, "tanlivebff_webaiReqListQATipFilter": {"type": "object", "title": "知识提示过滤条件，用来筛选问题超长等问题的记录", "properties": {"warning": {"type": "boolean"}}}, "tanlivebff_webaiReqListSharedAssistant": {"type": "object", "properties": {"data_source": {"$ref": "#/definitions/aiDocDataSource"}, "search": {"type": "string"}, "type": {"$ref": "#/definitions/aiListDocFilterType"}}}, "tanlivebff_webaiReqListSharedTeam": {"type": "object", "properties": {"data_source": {"$ref": "#/definitions/aiDocDataSource"}, "search": {"type": "string"}, "type": {"$ref": "#/definitions/aiListDocFilterType"}}}, "tanlivebff_webaiReqListSharedUser": {"type": "object", "properties": {"data_source": {"$ref": "#/definitions/aiDocDataSource"}, "search": {"type": "string"}, "type": {"$ref": "#/definitions/aiListDocFilterType"}}}, "tanlivebff_webaiReqListTeamCanShareDoc": {"type": "object", "properties": {"doc_id": {"type": "string", "format": "uint64", "title": "文档ID"}, "limit": {"type": "string", "format": "uint64", "title": "分页大小"}, "name": {"type": "string", "title": "搜索名称关键词"}, "offset": {"type": "string", "format": "uint64", "title": "分页偏移量"}}}, "tanlivebff_webaiReqListUserCanShareDoc": {"type": "object", "properties": {"doc_id": {"type": "string", "format": "uint64", "title": "文档ID"}, "name": {"type": "string", "title": "搜索名称关键词"}}}, "tanlivebff_webaiReqListeDocShareConfigSender": {"type": "object", "properties": {"language": {"type": "string", "title": "语言，为空会从header里取"}, "name": {"type": "string", "title": "搜索名称关键词"}}}, "tanlivebff_webaiReqModifyDocTab": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "type": {"type": "integer", "format": "int64"}}}, "tanlivebff_webaiReqRateAiAnswer": {"type": "object", "properties": {"message_id": {"type": "string", "format": "uint64", "title": "消息ID"}, "rating_scale": {"title": "评价等级", "$ref": "#/definitions/aiRatingScale"}}}, "tanlivebff_webaiReqReimportTencentDoc": {"type": "object", "title": "重新导入腾讯文档(doc_id 知识库的文档id)", "properties": {"doc_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "tanlivebff_webaiReqReparseTextFiles": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "parse_mode": {"$ref": "#/definitions/aiDocParseMode"}, "query_id": {"type": "string", "format": "uint64"}}}, "tanlivebff_webaiReqSearchCollection": {"type": "object", "properties": {"assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "clean_chunks": {"type": "boolean"}, "doc_type": {"$ref": "#/definitions/aiDocType"}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "search": {"type": "string"}, "temperature": {"type": "number", "format": "float"}, "text_recall_pattern": {"title": "关键词召回模式", "$ref": "#/definitions/aiTextRecallPattern"}, "text_recall_query": {"title": "关键词召回匹配目标", "$ref": "#/definitions/aiTextRecallQuery"}, "text_recall_slop": {"type": "integer", "format": "int32", "title": "关键词召回允许平移距离允许平移距离"}, "text_recall_top_n": {"type": "integer", "format": "int64"}, "text_weight": {"type": "number", "format": "float"}, "threshold": {"type": "number", "format": "float"}, "top_n": {"type": "integer", "format": "int64"}}}, "tanlivebff_webaiReqStopQuestionReply": {"type": "object", "properties": {"hash_id": {"type": "string"}, "id": {"type": "string", "format": "uint64"}, "question_id": {"type": "string", "format": "uint64"}}}, "tanlivebff_webaiReqSwitchChatLiveAgent": {"type": "object", "properties": {"chat_id": {"type": "string", "format": "uint64", "title": "会话id"}, "live_agent_id": {"type": "string", "format": "uint64", "title": "人工客服id"}}}, "tanlivebff_webaiReqUpdateQA": {"type": "object", "properties": {"answer": {"type": "string"}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "id": {"type": "string", "format": "uint64"}, "labels": {"type": "array", "title": "标签", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "mask": {"type": "string"}, "match_patterns": {"type": "array", "title": "匹配模式", "items": {"$ref": "#/definitions/aiDocMatchPattern"}}, "question": {"type": "string"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}, "states": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocAssistantState"}}}}, "tanlivebff_webaiReqUpdateTextFile": {"type": "object", "properties": {"contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "download_as_ref": {"title": "是否可以作为参考资料下载", "$ref": "#/definitions/aiDocFileDownloadAsRef"}, "file_name": {"type": "string"}, "id": {"type": "string", "format": "uint64"}, "labels": {"type": "array", "title": "标签", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "mask": {"type": "string"}, "parsed_url": {"type": "string", "title": "解析后的文件url"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}, "states": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocAssistantState"}}, "text": {"type": "string"}, "ugc_id": {"type": "string", "format": "uint64"}, "ugc_type": {"$ref": "#/definitions/baseDataType"}, "url": {"type": "string"}}}, "tanlivebff_webaiRspAuthTencentCode": {"type": "object"}, "tanlivebff_webaiRspContinueChatFromShare": {"type": "object", "title": "从分享继续聊天响应", "properties": {"assistant_id": {"type": "string", "format": "uint64", "title": "助手ID"}, "chat_id": {"type": "string", "format": "uint64", "title": "新会话ID"}, "title": {"type": "string", "title": "会话标题"}}}, "tanlivebff_webaiRspConvertCustomLabel": {"type": "object", "properties": {"deleted": {"type": "integer", "format": "int64"}, "reserved": {"type": "integer", "format": "int64"}}}, "tanlivebff_webaiRspCreateChatShare": {"type": "object", "title": "创建聊天分享响应", "properties": {"share_id": {"type": "string", "title": "分享ID"}}}, "tanlivebff_webaiRspCreateDocQuery": {"type": "object", "properties": {"is_empty": {"type": "boolean"}, "query_id": {"type": "string", "format": "uint64"}, "total_count": {"type": "integer", "format": "int64"}}}, "tanlivebff_webaiRspCreateNebulaTask": {"type": "object", "properties": {"uuid": {"type": "string"}}}, "tanlivebff_webaiRspCreateTencentDocAuthUrl": {"type": "object", "properties": {"url": {"type": "string"}}}, "tanlivebff_webaiRspDescribeChatRegionCode": {"type": "object", "properties": {"region_codes": {"type": "array", "items": {"type": "string"}}}}, "tanlivebff_webaiRspDescribeDocTab": {"type": "object", "properties": {"tabs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiRspDescribeDocTabDocTab"}}}}, "tanlivebff_webaiRspDescribeDocTabDocTab": {"type": "object", "properties": {"has_expired": {"type": "boolean"}, "id": {"type": "string", "format": "uint64"}, "is_show": {"type": "boolean"}, "name": {"type": "string"}, "type": {"type": "integer", "format": "int64"}}}, "tanlivebff_webaiRspDescribeExportTasks": {"type": "object", "properties": {"tasks": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiExportTask"}}}}, "tanlivebff_webaiRspDescribeFeedbackRegionCode": {"type": "object", "properties": {"region_codes": {"type": "array", "items": {"type": "string"}}}}, "tanlivebff_webaiRspDescribeMyDoc": {"type": "object", "properties": {"file_ids": {"type": "array", "items": {"type": "string"}}}}, "tanlivebff_webaiRspDescribeNebulaData": {"type": "object", "properties": {"content": {"type": "array", "items": {"type": "string"}}, "query_name": {"type": "string"}}}, "tanlivebff_webaiRspDescribeNebulaProjection": {"type": "object", "properties": {"projection": {"type": "array", "items": {"type": "number", "format": "double"}}}}, "tanlivebff_webaiRspDescribeNebulaTask": {"type": "object", "properties": {"calcu_result": {"type": "array", "items": {"type": "string"}}, "cluster_list": {"type": "string"}, "connect_info": {"type": "string"}, "end_date": {"type": "string"}, "filter_text": {"type": "string"}, "start_date": {"type": "string"}, "state": {"type": "integer", "format": "int64"}, "uuid": {"type": "string"}}}, "tanlivebff_webaiRspDescribeNebulaTaskList": {"type": "object", "properties": {"tasks": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiRspDescribeNebulaTaskListTask"}}, "total_count": {"type": "integer", "format": "int64"}, "unread_num": {"type": "integer", "format": "int64"}}}, "tanlivebff_webaiRspDescribeNebulaTaskListTask": {"type": "object", "properties": {"end_date": {"type": "string"}, "filter_text": {"type": "string"}, "is_read": {"type": "integer", "format": "int64"}, "lang": {"type": "string"}, "start_date": {"type": "string"}, "state": {"type": "integer", "format": "int64"}, "uuid": {"type": "string"}}}, "tanlivebff_webaiRspDescribeTencentDocTask": {"type": "object", "properties": {"is_running": {"type": "boolean"}}}, "tanlivebff_webaiRspGetAssistantOptions": {"type": "object", "properties": {"ask_suggestion_model": {"type": "array", "title": "问题建议模型", "items": {"type": "string"}}, "chat_model": {"type": "array", "title": "对话模型", "items": {"type": "string"}}, "chat_model_v2": {"type": "array", "title": "对话模型", "items": {"type": "object", "$ref": "#/definitions/aiChatModelOption"}}, "chat_or_sql_model": {"type": "array", "title": "ChatOrSql模型", "items": {"type": "string"}}, "embedding_model": {"type": "array", "title": "向量化模型", "items": {"type": "object", "$ref": "#/definitions/aiEmbeddingModelOption"}}, "graph_parse_mode": {"type": "array", "title": "解析图谱模型", "items": {"type": "string"}}, "interactive_code": {"type": "array", "title": "互动暗号", "items": {"type": "object", "$ref": "#/definitions/aiInteractiveCodeOption"}}, "mini_white_url": {"type": "array", "title": "小程序URL白名单", "items": {"type": "string"}}, "quick_actions": {"type": "array", "title": "快捷指令", "items": {"type": "object", "$ref": "#/definitions/aiQuickAction"}}, "search_engine": {"type": "array", "title": "搜索引擎", "items": {"type": "string"}}, "search_engine_v2": {"type": "array", "title": "对话模型", "items": {"type": "object", "$ref": "#/definitions/aiSearchEngineOption"}}, "visible_chain": {"type": "array", "title": "链路查询", "items": {"type": "object", "$ref": "#/definitions/aiVisibleChainOption"}}}}, "tanlivebff_webaiRspGetChatDetail": {"type": "object", "properties": {"chat_detail": {"$ref": "#/definitions/tanlivebff_webaiChatDetail"}, "totalCount": {"type": "integer", "format": "int64"}}}, "tanlivebff_webaiRspGetChunkDocTasks": {"type": "object", "properties": {"tasks": {"type": "array", "title": "任务列表", "items": {"type": "object", "$ref": "#/definitions/aiDocChunkTask"}}}}, "tanlivebff_webaiRspGetCustomLabelValueTopN": {"type": "object", "properties": {"values": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiLabelValue"}}}}, "tanlivebff_webaiRspGetDocChunks": {"type": "object", "properties": {"assistant_chunks": {"type": "array", "title": "助手的分段列表", "items": {"type": "object", "$ref": "#/definitions/aiAssistantChunks"}}}}, "tanlivebff_webaiRspGetDocEmbeddingModels": {"type": "object", "properties": {"embedding_models": {"type": "array", "title": "向量化模型列表", "items": {"type": "object", "$ref": "#/definitions/aiEmbeddingModelCount"}}}}, "tanlivebff_webaiRspGetFeedbackLogs": {"type": "object", "properties": {"items": {"type": "array", "title": "日志列表", "items": {"type": "object", "$ref": "#/definitions/aiFullFeedbackLog"}}, "team_cards": {"type": "array", "title": "团队卡片列表", "items": {"type": "object", "$ref": "#/definitions/teamTeamCard"}}, "total_count": {"type": "integer", "format": "int64", "title": "总数"}, "user_cards": {"type": "array", "title": "用户卡片列表", "items": {"type": "object", "$ref": "#/definitions/iamUserCard"}}}}, "tanlivebff_webaiRspGetFeedbacks": {"type": "object", "properties": {"items": {"type": "array", "title": "反馈列表", "items": {"type": "object", "$ref": "#/definitions/aiRspGetFeedbacksItem"}}, "team_cards": {"type": "array", "title": "团队卡片列表", "items": {"type": "object", "$ref": "#/definitions/teamTeamCard"}}, "total_count": {"type": "integer", "format": "int64", "title": "总数"}, "user_cards": {"type": "array", "title": "用户卡片列表", "items": {"type": "object", "$ref": "#/definitions/iamUserCard"}}}}, "tanlivebff_webaiRspGetQaTip": {"type": "object", "title": "获取QA知识提示响应", "properties": {"question_over_size": {"type": "boolean", "title": "问题超长提示"}, "repeated": {"type": "array", "title": "内容重复信息", "items": {"type": "string"}}}}, "tanlivebff_webaiRspGetTextFileTip": {"type": "object", "title": "获取文件文本知识提示响应", "properties": {"repeated": {"type": "array", "title": "内容重复信息", "items": {"type": "string"}}, "state": {"title": "解析状态", "$ref": "#/definitions/tanliveaiDocState"}, "table_over_size": {"type": "array", "title": "表头过长的表格", "items": {"type": "object", "$ref": "#/definitions/aiTextFileTipTableOverSize"}}}}, "tanlivebff_webaiRspImportTencentDoc": {"type": "object", "properties": {"uuid": {"type": "string"}}}, "tanlivebff_webaiRspImportTencentDocWebClip": {"type": "object", "properties": {"docs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiTencentDoc"}}}}, "tanlivebff_webaiRspListAssistant": {"type": "object", "properties": {"assistants": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveaiAssistant"}}, "total_count": {"type": "integer", "format": "int64"}}}, "tanlivebff_webaiRspListAssistantCanShareDoc": {"type": "object", "properties": {"assistants": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiRspListAssistantCanShareDocSharedAssistant"}}}}, "tanlivebff_webaiRspListAssistantCanShareDocSharedAssistant": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "is_selected": {"type": "boolean"}, "name": {"type": "string"}, "name_en": {"type": "string"}}}, "tanlivebff_webaiRspListChat": {"type": "object", "properties": {"chats": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatInfo"}}, "total_count": {"type": "integer", "format": "int64"}}}, "tanlivebff_webaiRspListChatLiveAgent": {"type": "object", "properties": {"chatLiveAgent": {"$ref": "#/definitions/aiChatLiveAgentInfo"}}}, "tanlivebff_webaiRspListContributor": {"type": "object", "properties": {"contributors": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}}}, "tanlivebff_webaiRspListCustomLabel": {"type": "object", "properties": {"labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "total_count": {"type": "integer", "format": "int64"}}}, "tanlivebff_webaiRspListDocShareConfigReceiverAssistant": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64"}, "other_state": {"$ref": "#/definitions/aiDocShareState"}, "receiver_state": {"$ref": "#/definitions/aiDocShareAcceptState"}, "user_shares": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiRspListDocShareConfigReceiverAssistantUserShare"}}}}, "tanlivebff_webaiRspListDocShareConfigReceiverAssistantUserShare": {"type": "object", "properties": {"group_id": {"type": "string", "format": "uint64"}, "state": {"$ref": "#/definitions/aiDocShareState"}, "teams": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspListDocShareConfigReceiverAssistantMembers"}}, "users": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspListDocShareConfigReceiverAssistantMembers"}}}}, "tanlivebff_webaiRspListDocShareConfigReceiverUserTeam": {"type": "object", "properties": {"teams": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspListDocShareConfigReceiverUserTeamMembers"}}, "users": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspListDocShareConfigReceiverUserTeamMembers"}}}}, "tanlivebff_webaiRspListExternalSourceUser": {"type": "object", "properties": {"users": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiExternalSourceUser"}}}}, "tanlivebff_webaiRspListMyAssistantIds": {"type": "object", "properties": {"share_assistant_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "tanlivebff_webaiRspListQA": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCollectionQA"}}, "total_count": {"type": "integer", "format": "int64"}}}, "tanlivebff_webaiRspListSharedAssistant": {"type": "object", "properties": {"assistants": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiAssistant"}}}}, "tanlivebff_webaiRspListSharedTeam": {"type": "object", "properties": {"teams": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspListSharedTeamSharedTeam"}}}}, "tanlivebff_webaiRspListSharedUser": {"type": "object", "properties": {"users": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspListSharedUserSharedUser"}}}}, "tanlivebff_webaiRspListTeamCanShareDoc": {"type": "object", "properties": {"teams": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiRspListTeamCanShareDocTeams"}}}}, "tanlivebff_webaiRspListTeamCanShareDocTeams": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "is_selected": {"type": "boolean"}, "name": {"type": "string"}, "name_en": {"type": "string"}}}, "tanlivebff_webaiRspListUserCanShareDoc": {"type": "object", "properties": {"users": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiRspListUserCanShareDocUsers"}}}}, "tanlivebff_webaiRspListUserCanShareDocUsers": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "is_selected": {"type": "boolean"}, "name": {"type": "string"}}}, "tanlivebff_webaiRspListeDocShareConfigSender": {"type": "object", "properties": {"assistants": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspListeDocShareConfigSenderSharedAssistant"}}, "teams": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspListeDocShareConfigSenderSharedUserTeam"}}, "users": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspListeDocShareConfigSenderSharedUserTeam"}}}}, "tanlivebff_webaiRspReimportTencentDoc": {"type": "object", "properties": {"failed": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiRspReimportTencentDocFailInfo"}}}}, "tanlivebff_webaiRspReimportTencentDocFailInfo": {"type": "object", "properties": {"doc_id": {"type": "string", "format": "uint64"}, "file_name": {"type": "string"}, "user": {"$ref": "#/definitions/tanliveaiExternalSourceUser"}}}, "tanlivebff_webaiRspReparseTextFiles": {"type": "object", "properties": {"async": {"type": "boolean"}}}, "tanlivebff_webaiRspSearchCollection": {"type": "object", "properties": {"end": {"type": "string", "format": "date-time"}, "item": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webaiSearchCollectionItem"}}, "match_pattern": {"$ref": "#/definitions/aiDocMatchPattern"}, "start": {"type": "string", "format": "date-time"}, "total_count": {"type": "integer", "format": "int64"}}}, "tanlivebff_webaiRspStopQuestionReply": {"type": "object", "properties": {"message": {"$ref": "#/definitions/aiChatMessage"}}}, "tanlivebff_webaiSearchCollectionItem": {"type": "object", "properties": {"contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}, "doc_id": {"type": "string", "format": "uint64"}, "doc_name": {"type": "string"}, "doc_type": {"title": "文件类型", "$ref": "#/definitions/aiDocType"}, "file_name": {"type": "string"}, "id": {"type": "string"}, "is_related": {"type": "boolean", "title": "是否相关"}, "question": {"type": "string"}, "score": {"type": "number", "format": "float"}, "text": {"type": "string"}, "type": {"title": "召回类型", "$ref": "#/definitions/aiSearchCollectionType"}, "update_by": {"$ref": "#/definitions/aiDocOperator"}, "url": {"type": "string"}}}, "tanlivebff_webaiShareStatus": {"description": "- 1: 有效\n - 2: 已失效（手动失效）\n - 3: 已过期", "type": "integer", "format": "int32", "title": "分享状态", "enum": [1, 2, 3]}, "tanlivebff_webaiShareType": {"description": "- 1: 链接分享\n - 2: 二维码分享\n - 3: 小程序码分享", "type": "integer", "format": "int32", "title": "分享类型", "enum": [1, 2, 3]}, "tanlivebff_webaiTencentDoc": {"type": "object", "properties": {"file_browse_time": {"type": "string"}, "file_create_time": {"type": "string"}, "file_create_user": {"type": "string"}, "file_id": {"type": "string"}, "file_modify_time": {"type": "string"}, "file_name": {"type": "string"}, "file_owner_name": {"type": "string"}, "file_type": {"type": "string"}, "file_url": {"type": "string"}, "title": {"type": "string"}, "url": {"type": "string"}}}, "tanlivebff_websearchSearchOption": {"type": "object", "title": "搜索筛选项", "properties": {"name": {"type": "string", "title": "筛选项名称"}, "refer_id": {"type": "string", "format": "int64", "title": "筛选项关联的id，比如图谱id"}}}, "tanlivebff_websearchSearchPrompt": {"type": "object", "title": "搜索提示语", "properties": {"content": {"type": "string"}}}, "tanlivebff_webteamFullTeam": {"type": "object", "title": "完整团队信息", "properties": {"team_info": {"title": "团队信息", "$ref": "#/definitions/teamTeamInfo"}}}, "teamReqSearchTeamsInAiShareSetting": {"type": "object", "properties": {"keyword": {"type": "string", "title": "搜索关键词"}, "limit": {"type": "integer", "format": "int64", "title": "分页大小"}, "offset": {"type": "integer", "format": "int64", "title": "分页偏移量"}}}, "teamReqUpdateUgcCustomRoute": {"type": "object", "properties": {"ugc_id": {"type": "string", "format": "uint64"}, "ugc_route": {"type": "string"}}}, "teamRspSearchTeamsInAiShareSetting": {"type": "object", "properties": {"teams": {"type": "array", "title": "团队列表", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_webteamFullTeam"}}, "total_count": {"type": "integer", "format": "int64", "title": "总数"}}}, "teamTeamCard": {"type": "object", "title": "团队卡片", "properties": {"brief_intro": {"type": "string", "title": "一句话介绍"}, "full_name": {"type": "string", "title": "主体名称"}, "id": {"type": "string", "format": "uint64", "title": "团队ID"}, "is_published": {"type": "boolean", "title": "是否已发布"}, "is_verified": {"type": "boolean", "title": "是否认证"}, "level": {"title": "共创等级", "$ref": "#/definitions/teamTeamLevel"}, "logo_url": {"type": "string", "title": "团队LOGO"}, "short_name": {"type": "string", "title": "简称"}}}, "teamTeamInfo": {"type": "object", "title": "团队信息", "properties": {"brief_intro": {"type": "string", "title": "一句话介绍"}, "create_by": {"type": "string", "format": "uint64", "title": "创建人"}, "create_date": {"type": "string", "format": "date-time", "title": "创建时间"}, "detected_lang": {"type": "string", "title": "检测语言"}, "full_name": {"type": "string", "title": "团队主体名称"}, "holder_id": {"type": "string", "format": "uint64", "title": "团队持有者ID"}, "id": {"type": "string", "format": "uint64", "title": "团队ID"}, "is_deregistered": {"type": "boolean", "title": "是否已注销"}, "is_draft": {"type": "boolean", "title": "是否为草稿"}, "is_published": {"type": "boolean", "title": "是否已发布"}, "is_verified": {"type": "boolean", "title": "是否已认证"}, "is_virtual": {"type": "boolean", "title": "是否为虚拟团队"}, "level": {"title": "共创等级", "$ref": "#/definitions/teamTeamLevel"}, "logo_url": {"type": "string", "title": "团队LOGO"}, "main_id": {"type": "string", "format": "uint64", "title": "主数据ID"}, "principal_country": {"type": "string", "title": "主体国家"}, "short_name": {"type": "string", "title": "团队简称"}, "source_lang": {"type": "string", "title": "源语言"}, "ugc_state": {"title": "UGC状态", "$ref": "#/definitions/baseUgcState"}, "update_by": {"type": "string", "format": "uint64", "title": "最后编辑者"}, "update_date": {"type": "string", "format": "date-time", "title": "最后编辑时间"}}}, "teamTeamLevel": {"description": "- 1: CONTRIBUTOR\n - 2: COMMITTER\n - 3: MAIN<PERSON>INER", "type": "integer", "format": "int32", "title": "团队共创等级", "enum": [1, 2, 3]}}, "tags": [{"name": "NotifyGuestBff"}]}