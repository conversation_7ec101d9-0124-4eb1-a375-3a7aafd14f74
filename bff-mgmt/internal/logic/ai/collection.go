package ai

import (
	"context"
	"sync"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/gokits/xslice"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/logic"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/hashids"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/mgmt"
	"golang.org/x/sync/errgroup"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	ContributorWudu           = "无毒"
	CollectionWudu            = 2
	ContributorShenghuan      = "生环标准"
	CollectionShenghuanWeb    = 3
	CollectionShenghuanWechat = 4

	ContributorTFoundation      = "腾讯公益基金会"
	CollectionTFoundationWeb    = 6
	CollectionTFoundationWechat = 5

	ContributorZHFoundation      = "洲明基金会"
	CollectionZHFoundationWeb    = 8
	CollectionZHFoundationWechat = 7
)

// GetWuduUserId 无毒用户的id
func GetWuduUserId() uint64 {
	return config.GetUint64Or("ai.collection.wuduUserId", 100360)
}

// IsWuduUser 是否是无毒用户
func IsWuduUser(ctx context.Context) bool {
	user := xsession.UserFromContext[mgmt.OpUser](ctx)
	if user == nil {
		return false
	}
	return user.Id == GetWuduUserId()
}

// GetShenghuanUserId 获取生环用户id
func GetShenghuanUserId() uint64 {
	return config.GetUint64Or("ai.collection.shenghuanUserId", 100353)
}

// IsShenghuanUser 是否是生环用户
func IsShenghuanUser(ctx context.Context) bool {
	user := xsession.UserFromContext[mgmt.OpUser](ctx)
	if user == nil {
		return false
	}
	return user.Id == GetShenghuanUserId()
}

// GetTFoundationUserId 获取腾讯基金会用户id
func GetTFoundationUserId() uint64 {
	return config.GetUint64Or("ai.collection.txjijinUserId", 100369)
}

// IsTFoundationUser 是否是腾讯基金会用户
func IsTFoundationUser(ctx context.Context) bool {
	user := xsession.UserFromContext[mgmt.OpUser](ctx)
	if user == nil {
		return false
	}
	return user.Id == GetTFoundationUserId()
}

// GetZHFoundationUserId 获取洲明基金会用户id
func GetZHFoundationUserId() uint64 {
	return config.GetUint64Or("ai.collection.zhmjijinUserId", 100370)
}

// IsZHFoundationUser 是否是洲明基金会用户
func IsZHFoundationUser(ctx context.Context) bool {
	user := xsession.UserFromContext[mgmt.OpUser](ctx)
	if user == nil {
		return false
	}
	return user.Id == GetZHFoundationUserId()
}

// ListQAHook ...
func ListQAHook(ctx context.Context, req *aipb.ReqListQA) {
}

// ListTextFileHook ...
func ListTextFileHook(ctx context.Context, req *aipb.ReqListTextFile) {
}

// GetAiDocOperatorsShowInfo 获取知识库操作者的显示信息
func GetAiDocOperatorsShowInfo(ctx context.Context, operators ...*aipb.Operator) (
	map[*aipb.Operator]*ai.Operator, error,
) {
	if len(operators) == 0 {
		return nil, nil
	}
	ret := make(map[*aipb.Operator]*ai.Operator, len(operators))
	mtx := sync.Mutex{}
	var userIds []uint64
	var opUserIds []uint64
	var teamUserIds []uint64
	var teamTeamIds []uint64
	var teamIds []uint64
	for _, v := range operators {
		switch v.GetType() {
		case base.IdentityType_IDENTITY_TYPE_MGMT:
			opUserIds = append(opUserIds, v.Id)
		case base.IdentityType_IDENTITY_TYPE_USER:
			userIds = append(userIds, v.Id)
		case base.IdentityType_IDENTITY_TYPE_TEAM:
			if v.GetUserId() == 0 {
				teamIds = append(teamIds, v.Id)
			} else {
				teamTeamIds = append(teamTeamIds, v.Id)
				teamUserIds = append(teamUserIds, v.UserId)
			}
		}
	}
	xslice.SetUint64Unique(&userIds, 0)
	xslice.SetUint64Unique(&opUserIds, 0)
	xslice.SetUint64Unique(&teamIds, 0)
	xslice.SetUint64Unique(&teamUserIds, 0)
	xslice.SetUint64Unique(&teamTeamIds, 0)
	g := &errgroup.Group{}
	if len(opUserIds) > 0 {
		g.Go(func() error {
			names, err := logic.FetchOpUserName(ctx, opUserIds)
			if err != nil {
				return err
			}
			mtx.Lock()
			defer mtx.Unlock()
			for _, v := range operators {
				name := names[v.Id]
				if v.Type == base.IdentityType_IDENTITY_TYPE_MGMT && name != "" {
					ret[v] = &ai.Operator{Type: v.Type, Id: v.Id, Username: name}
				}
			}
			return nil
		})
	}
	if len(userIds) > 0 {
		g.Go(func() error {
			names, err := logic.FetchUserName(ctx, userIds)
			if err != nil {
				return err
			}
			mtx.Lock()
			defer mtx.Unlock()
			for _, v := range operators {
				name := names[v.Id]
				if v.Type == base.IdentityType_IDENTITY_TYPE_USER && name != "" {
					ret[v] = &ai.Operator{Type: v.Type, Id: v.Id, Username: name}
				}
			}
			return nil
		})
	}

	if len(teamIds) > 0 {
		g.Go(func() error {
			infos, err := logic.FetchTeamInfo(ctx, teamIds)
			if err != nil {
				return err
			}
			mtx.Lock()
			defer mtx.Unlock()
			for _, v := range operators {
				teamName := infos[v.Id]
				if v.Type == base.IdentityType_IDENTITY_TYPE_TEAM && v.UserId == 0 && teamName != nil {
					ret[v] = &ai.Operator{Type: v.Type, Id: v.Id, TeamName: teamName.ShortName}
				}
			}
			return nil
		})
	}

	if len(teamUserIds) > 0 {
		g.Go(func() error {
			userNames, err := logic.FetchUserName(ctx, teamUserIds)
			if err != nil {
				return err
			}
			teamNames, err := logic.FetchTeamInfo(ctx, teamTeamIds)
			if err != nil {
				return err
			}
			mtx.Lock()
			defer mtx.Unlock()
			for _, v := range operators {
				userName := userNames[v.UserId]
				teamName := teamNames[v.Id]
				if v.Type == base.IdentityType_IDENTITY_TYPE_TEAM && v.UserId != 0 && userName != "" && teamName != nil {
					ret[v] = &ai.Operator{Type: v.Type, Id: v.Id, UserId: v.UserId, Username: userName, TeamName: teamName.ShortName}
				}
			}
			return nil
		})
	}
	err := g.Wait()
	if err != nil {
		return nil, err
	}
	return ret, nil
}

// GetAiDoctContributorShowInfo 获取知识库贡献者的显示信息
// 会重写入参中contributors的值
func GetAiDoctContributorShowInfo(ctx context.Context, contributors ...*aipb.Contributor) (
	map[*aipb.Contributor]*aipb.Contributor, error,
) {
	if len(contributors) == 0 {
		return map[*aipb.Contributor]*aipb.Contributor{}, nil
	}
	ret := make(map[*aipb.Contributor]*aipb.Contributor, len(contributors))
	var userIds []uint64
	var opUserIds []uint64
	var teamIds []uint64
	for _, v := range contributors {
		switch v.GetType() {
		case base.IdentityType_IDENTITY_TYPE_MGMT:
			opUserIds = append(opUserIds, v.Id)
		case base.IdentityType_IDENTITY_TYPE_TEAM:
			teamIds = append(teamIds, v.Id)
		case base.IdentityType_IDENTITY_TYPE_USER:
			userIds = append(userIds, v.Id)
		case base.IdentityType_IDENTITY_TYPE_CUSTOM:
		}
		ret[v] = v
	}
	g := &errgroup.Group{}
	if len(opUserIds) > 0 {
		g.Go(func() error {
			names, err := logic.FetchOpUserName(ctx, opUserIds)
			if err != nil {
				return err
			}
			for _, v := range contributors {
				name := names[v.Id]
				if v.Type == base.IdentityType_IDENTITY_TYPE_MGMT && name != "" {
					v.Text = name
				}
			}
			return nil
		})
	}
	if len(userIds) > 0 {
		g.Go(func() error {
			names, err := logic.FetchUserName(ctx, userIds)
			if err != nil {
				return err
			}
			for _, v := range contributors {
				name := names[v.Id]
				if v.Type == base.IdentityType_IDENTITY_TYPE_USER && name != "" {
					v.Text = name
				}
			}
			return nil
		})
	}
	if len(teamIds) > 0 {
		g.Go(func() error {
			infos, err := logic.FetchTeamInfo(ctx, teamIds)
			if err != nil {
				return err
			}
			for _, v := range contributors {
				info := infos[v.Id]
				if v.Type == base.IdentityType_IDENTITY_TYPE_TEAM && info != nil {
					v.Text = info.ShortName
					v.Level = info.Level
					v.IsPublished = info.IsPublished
					v.FullName = info.FullName
				}
			}
			return nil
		})
	}
	err := g.Wait()
	if err != nil {
		return nil, err
	}
	return ret, nil
}

// SpiltDocAssistantsByShared 通过判断助手是否为分享接受的 doc
func SpiltDocAssistantsByShared(allStates []*aipb.DocAssistantState) (owned []*aipb.DocAssistantState) {
	owned = make([]*aipb.DocAssistantState, 0)
	for _, v := range allStates {
		// 过滤从助手中删除的
		if v.State == aipb.DocState_DOC_STATE_UNBOUNDED {
			continue
		} else {
			owned = append(owned, v)
		}
	}
	return owned
}

// GetSharedDoc 通过判断助手是否为分享接受的 doc
func GetSharedDoc(allStates []*aipb.DocAssistantState) (shared []*aipb.DocAssistantState) {
	shared = make([]*aipb.DocAssistantState, 0)
	for _, v := range allStates {
		if v.IsShared == 2 {
			// 贡献出去的知识，如果删除了，显示为已禁用
			if v.State == aipb.DocState_DOC_STATE_DELETING {
				v.State = aipb.DocState_DOC_STATE_DISABLED
			}
			shared = append(shared, v)
		}
	}
	return shared
}

// FullTextFileToCollectionTextFile 协议转换
func FullTextFileToCollectionTextFile(tf *aipb.FullTextFile,
	operators map[*aipb.Operator]*ai.Operator,
) *ai.CollectionTextFile {
	ctf := TextFileToCollectionTextFile(tf.Doc, operators)
	ctf.Copies = TextFilesToCollectionTextFiles(tf.Copies, operators)
	return ctf
}

// FullTextFilesToCollectionTextFiles ...
func FullTextFilesToCollectionTextFiles(tfs []*aipb.FullTextFile,
	operators map[*aipb.Operator]*ai.Operator,
) []*ai.CollectionTextFile {
	ctfs := make([]*ai.CollectionTextFile, 0, len(tfs))
	for _, tf := range tfs {
		ctfs = append(ctfs, FullTextFileToCollectionTextFile(tf, operators))
	}
	return ctfs
}

// TextFileToCollectionTextFile ...
func TextFileToCollectionTextFile(tf *aipb.TextFile,
	operators map[*aipb.Operator]*ai.Operator,
) *ai.CollectionTextFile {
	ugcHashid, _ := hashids.Encode(tf.UgcId)
	return &ai.CollectionTextFile{
		Id:                 tf.Id,
		FileName:           tf.Name,
		Text:               tf.Text,
		States:             SpiltDocAssistantsByShared(tf.States),
		Assistants:         tf.Assistants,
		Contributor:        tf.Contributor,
		Url:                tf.Url,
		HitCount:           tf.HitCount,
		UgcType:            tf.UgcType,
		UgcId:              tf.UgcId,
		UpdateDate:         tf.UpdateDate,
		CreateDate:         tf.CreateDate,
		UpdateBy:           operators[tf.UpdateBy],
		CreateBy:           operators[tf.CreateBy],
		EmbeddingState:     GenEmbeddingState(tf.EmbeddingVersion),
		ParseProgress:      tf.ParseProgress,
		State:              tf.State,
		IsCopy:             tf.IsCopy,
		UgcTitle:           tf.UgcTitle,
		IsSystem:           tf.DataSource == aipb.DocDataSource_DOC_DATA_SOURCE_UGC,
		DataSource:         tf.DataSource,
		ContentState:       tf.ContentState,
		ShowContributor:    tf.ShowContributor,
		UgcHashid:          ugcHashid,
		SharedStates:       GetSharedDoc(tf.States),
		Labels:             tf.Labels,
		Reference:          tf.Reference,
		DownloadAsRef:      tf.DownloadAsRef,
		ParseMode:          tf.ParseMode,
		HasOverSizedTables: tf.HasOverSizedTables,
		HasRepeated:        tf.HasRepeated,
		DataSourceState:    tf.DataSourceState,
	}
}

// TextFilesToCollectionTextFiles ...
func TextFilesToCollectionTextFiles(tfs []*aipb.TextFile,
	operators map[*aipb.Operator]*ai.Operator,
) []*ai.CollectionTextFile {
	ctfs := make([]*ai.CollectionTextFile, 0, len(tfs))
	for _, v := range tfs {
		ctfs = append(ctfs, TextFileToCollectionTextFile(v, operators))
	}
	return ctfs
}

// GenEmbeddingState 生成向量状态
// 基于传入的EmbeddingVersion列表来判断向量状态：
// 1. 如果没有版本记录，返回UNSPECIFIED
// 2. 如果有任何版本不一致，返回SYNCING
// 3. 如果所有版本一致且至少同步过一次，返回SYNCED
// 4. 如果所有版本一致但从未同步过，返回SYNCING
func GenEmbeddingState(versions []*aipb.EmbeddingVersion) aipb.DocEmbeddingState {
	if len(versions) == 0 {
		return aipb.DocEmbeddingState_DOC_EMBEDDING_STATE_UNSPECIFIED
	}

	var ragGt0 bool
	for _, v := range versions {
		if v.DocVersion != v.RagVersion {
			return aipb.DocEmbeddingState_DOC_EMBEDDING_STATE_SYNCING
		}
		if v.RagVersion > 0 {
			ragGt0 = true
		}
	}

	// 至少同步了一次
	if ragGt0 {
		return aipb.DocEmbeddingState_DOC_EMBEDDING_STATE_SYNCED
	}

	// 有版本记录但从未同步过，返回同步中
	return aipb.DocEmbeddingState_DOC_EMBEDDING_STATE_SYNCING
}

// MatchQACollection ...
func MatchQACollection(ctx context.Context, req *aipb.ReqSearchCollectionOneShot) (*aipb.RspSearchCollection, aipb.DocMatchPattern, error) {
	if len(req.AssistantId) == 0 {
		return nil, aipb.DocMatchPattern_DOC_MATCH_PATTERN_UNSPECIFIED, nil
	}
	start := time.Now()
	qa, err := client.AiClient.DescribeMessageMatchQa(ctx, &aipb.ReqDescribeMessageMatchQa{
		Text:        req.Search,
		AssistantId: req.AssistantId[0],
		Page: &base.Paginator{
			Offset: req.Offset,
			Limit:  req.Limit,
		},
	})
	if err != nil {
		return nil, aipb.DocMatchPattern_DOC_MATCH_PATTERN_UNSPECIFIED, err
	}
	if qa == nil || len(qa.Docs) == 0 {
		return nil, aipb.DocMatchPattern_DOC_MATCH_PATTERN_UNSPECIFIED, nil
	}

	rsp := &aipb.RspSearchCollection{
		TotalCount: qa.TotalCount,
		StartTime:  timestamppb.New(start),
		EndTime:    timestamppb.Now(),
		Items:      make([]*aipb.SearchCollectionItem, len(qa.Docs)),
	}
	for i, v := range qa.Docs {
		rsp.Items[i] = &aipb.SearchCollectionItem{
			Text:     v.Text,
			Question: v.IndexText,
			// RefName:     "",
			// RefUrl:      "",
			Contributor: v.Contributor,
			UpdateBy:    v.UpdateBy,
			// Id:          "",
			DocName: v.FileName,
			DocType: aipb.DocType_DOCTYPE_QA,
		}
	}
	return rsp, qa.MatchPattern, nil
}

// CreateAiReqListTextFile ...
func CreateAiReqListTextFile(ctx context.Context, req *ai.ReqListTextFiles) (*aipb.ReqListTextFile, error) {
	tenant, err := GetTanliveOpCustomLabelTenant()
	if err != nil {
		return nil, err
	}
	pbReq := &aipb.ReqListTextFile{
		AssistantId:         req.AssistantId,
		ExcludedAssistantId: req.ExcludedAssistantId,
		State:               req.State,
		OrderBy:             req.OrderBy,
		Page: &base.Paginator{
			Offset: req.Offset,
			Limit:  req.Limit,
		},
		Search: &aipb.ReqListTextFile_Search{
			Text:     req.GetSearch().GetText(),
			FileName: req.GetSearch().GetFileName(),
			UgcTitle: req.GetSearch().GetUgcTitle(),
		},
		Id:              req.Ids,
		GroupRepeated:   req.GroupRepeated,
		ShowContributor: req.ShowContributor,
		// 已废弃
		// IsSystem:        req.IsSystem,
		WithCopies:      req.WithCopies,
		TextExcerpt:     true, // 只获取片段 300字符
		UgcType:         req.UgcType,
		ContentState:    req.ContentState,
		Labels:          req.Labels,
		OrderByLabel:    req.OrderByLabel,
		LabelTenant:     tenant,
		DownloadAsRef:   req.DownloadAsRef,
		ParseMode:       req.ParseMode,
		ParseState:      req.ParseState,
		WithTips:        true,
		DataSource:      req.DataSource,
		DataSourceState: req.DataSourceState,
		EmbeddingState:  req.EmbeddingState,
	}
	// 兼容之前的参数
	if req.IsSystem {
		pbReq.DataSource = aipb.DocDataSource_DOC_DATA_SOURCE_UGC
	}

	if req.TipFilter != nil {
		pbReq.TipFilter = &aipb.ReqListTextFile_TipFilter{}
		if req.TipFilter.Warning {
			pbReq.TipFilter.WarningGroup = &aipb.ReqListTextFile_TipFilter_WarningGroup{
				TableOversize: true,
				ParseFailed:   true,
			}
		}
	}

	for _, v := range req.UpdateBy {
		pbReq.UpdateBy = append(pbReq.UpdateBy, &aipb.OperatorFilter{
			Operator: v,
		})
	}
	for _, v := range req.CreateBy {
		pbReq.CreateBy = append(pbReq.CreateBy, &aipb.OperatorFilter{
			Operator: v,
		})
	}
	for _, v := range req.Contributor {
		pbReq.Contributor = append(pbReq.Contributor, &aipb.ContributorFilter{
			Contributor: v,
		})
	}
	ListTextFileHook(ctx, pbReq)
	return pbReq, nil
}

// CreateAiListQA 获取QA列表
func CreateAiListQA(ctx context.Context, req *ai.ReqListQA) (*aipb.ReqListQA, error) {
	tenant, err := GetTanliveOpCustomLabelTenant()
	if err != nil {
		return nil, err
	}
	pbReq := &aipb.ReqListQA{
		AssistantId:         req.AssistantId,
		ExcludedAssistantId: req.ExcludedAssistantId,
		State:               req.State,
		OrderBy:             req.OrderBy,
		Page: &base.Paginator{
			Offset: req.Offset,
			Limit:  req.Limit,
		},
		Search:          &aipb.ReqListQA_Search{Qa: req.Search},
		GroupRepeated:   req.GroupRepeated,
		ShowContributor: req.ShowContributor,
		Labels:          req.Labels,
		OrderByLabel:    req.OrderByLabel,
		LabelTenant:     tenant,
		MatchPatterns:   req.MatchPatterns,
		WithTips:        true,
		EmbeddingState:  req.EmbeddingState,
	}

	if req.TipFilter != nil {
		pbReq.TipFilter = &aipb.ReqListQA_TipFilter{}
		if req.TipFilter.Warning {
			pbReq.TipFilter.WarningGroup = &aipb.ReqListQA_TipFilter_WarningGroup{
				QuestionOversize: true,
			}
		}
	}

	for _, v := range req.UpdateBy {
		pbReq.UpdateBy = append(pbReq.UpdateBy, &aipb.OperatorFilter{
			Operator: v,
		})
	}
	for _, v := range req.CreateBy {
		pbReq.CreateBy = append(pbReq.CreateBy, &aipb.OperatorFilter{
			Operator: v,
		})
	}
	for _, v := range req.Contributor {
		pbReq.Contributor = append(pbReq.Contributor, &aipb.ContributorFilter{
			Contributor: v,
		})
	}
	ListQAHook(ctx, pbReq)
	return pbReq, nil
}
